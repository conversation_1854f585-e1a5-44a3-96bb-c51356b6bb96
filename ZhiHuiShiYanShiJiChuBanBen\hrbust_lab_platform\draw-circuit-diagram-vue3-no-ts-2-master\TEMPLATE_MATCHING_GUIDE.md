# 📝 测点模板匹配功能使用指南

## 🎯 功能概述

测点模板匹配功能允许教师保存电路模板，学生可以将自己绘制的电路与模板进行匹配，系统会自动评分并给出详细的反馈。

## 🔧 核心功能

### 1. 测点识别
- ✅ 自动识别电路中的测点
- ✅ 检测测点连接的器件类型
- ✅ 支持多种器件：电阻、可变电阻、电容、电感等
- ✅ 按测点标识符分组显示（A测点、B测点等）

### 2. 模板保存
- ✅ 教师可以保存当前电路为标准模板
- ✅ 自动记录每个测点的连接要求
- ✅ 支持多个模板管理

### 3. 自动匹配
- ✅ 学生电路与模板自动对比
- ✅ 智能评分系统
- ✅ 详细的错误分析和建议

## 📋 使用步骤

### 教师端：保存模板

1. **绘制标准电路**
   - 在画布上绘制完整的电路图
   - 添加测点并正确连接到相应器件
   - 确保测点有正确的标识符（A、B、C等）

2. **调试测点关系**
   - 点击"调试测点关系"按钮
   - 查看控制台输出，确认测点识别正确：
     ```
     A测点: 连接了电阻、可变电阻
     B测点: 连接了极性电容、无极性电容
     ```

3. **保存为模板**
   - 点击"📝 保存模板"按钮
   - 输入模板名称（如"实验一_RC电路"）
   - 系统自动保存测点连接要求

### 学生端：匹配模板

1. **绘制学生电路**
   - 按照实验要求绘制电路图
   - 添加测点并连接到相应器件
   - 确保测点标识符与模板一致

2. **执行匹配**
   - 点击"🔍 匹配模板"按钮
   - 选择要匹配的模板
   - 查看匹配结果和得分

3. **查看详细反馈**
   - 控制台会显示详细的匹配结果
   - 包括每个测点的匹配状态
   - 错误提示和改进建议

## 📊 匹配结果说明

### 匹配状态
- ✅ **正确**：测点连接完全符合要求
- ❌ **错误**：测点连接的器件类型不正确
- ⚠️ **缺失**：缺少必需的测点
- ➕ **多余**：存在不需要的测点

### 评分规则
- **满分**：所有测点连接正确 = 100分
- **部分分**：正确测点数 / 总测点数 × 100
- **零分**：没有正确的测点连接 = 0分

## 🔍 示例场景

### 场景1：RC电路实验

**教师模板：**
```
A测点: 电阻 R1
B测点: 极性电容 C1、无极性电容 C1
```

**学生电路：**
```
A测点: 电阻 R1 ✅ 正确
B测点: 极性电容 C1 ❌ 缺少无极性电容
```

**匹配结果：**
- 得分：50%
- A测点：✅ 正确
- B测点：❌ 错误，缺少无极性电容

### 场景2：电阻电路实验

**教师模板：**
```
A测点: 电阻 R1、可变电阻 R2
B测点: 电阻 R3
```

**学生电路：**
```
A测点: 电阻 R1、可变电阻 R2 ✅ 正确
B测点: 电阻 R3 ✅ 正确
```

**匹配结果：**
- 得分：100%
- 完全匹配！

## 🛠️ 故障排除

### 问题1：测点识别不正确
**解决方案：**
1. 确保测点有正确的标识符
2. 检查测点与器件的连接线是否完整
3. 使用"调试测点关系"按钮查看详细信息

### 问题2：器件类型显示错误
**解决方案：**
1. 确认器件的type属性设置正确
2. 检查器件库中的类型定义
3. 查看控制台的详细匹配日志

### 问题3：模板保存失败
**解决方案：**
1. 确保电路中至少有一个测点
2. 检查测点是否正确连接到器件
3. 查看浏览器控制台的错误信息

### 问题4：匹配结果不准确
**解决方案：**
1. 确认学生电路的测点标识符与模板一致
2. 检查器件类型是否完全匹配
3. 使用调试功能对比模板和学生电路

## 📝 最佳实践

### 教师端
1. **标准化测点命名**：使用A、B、C等简单标识符
2. **完整的电路**：确保所有连接都正确无误
3. **清晰的要求**：每个测点的连接要求要明确
4. **多次验证**：保存模板前多次调试确认

### 学生端
1. **仔细阅读要求**：理解每个测点应该连接什么器件
2. **规范绘制**：按照标准流程绘制电路
3. **及时调试**：绘制完成后立即进行匹配测试
4. **根据反馈改进**：根据匹配结果调整电路

## 🔄 数据格式

### 模板数据结构
```json
{
  "templateName": "实验一_RC电路",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "testPointRequirements": {
    "A": {
      "testPoint": {
        "identifier": "A",
        "name": "测点"
      },
      "connectedDevices": [
        {
          "typeName": "电阻",
          "identifier": "R1",
          "type": "resistor"
        }
      ]
    }
  }
}
```

### 匹配结果结构
```json
{
  "isMatch": true,
  "score": 100,
  "matchedPoints": 2,
  "totalPoints": 2,
  "details": [
    {
      "testPointId": "A",
      "status": "correct",
      "message": "A测点连接正确",
      "expected": ["电阻"],
      "actual": ["电阻"]
    }
  ]
}
```

## 🚀 未来扩展

1. **云端存储**：模板保存到服务器
2. **批量评分**：支持多个学生作业批量评分
3. **可视化报告**：生成图表形式的匹配报告
4. **智能提示**：根据错误类型给出具体的修改建议
5. **历史记录**：保存学生的匹配历史和进步轨迹

---

**注意**：此功能需要确保测点组件有正确的标识符，建议在绘制电路时为每个测点设置清晰的标识符（如A、B、C等）。
