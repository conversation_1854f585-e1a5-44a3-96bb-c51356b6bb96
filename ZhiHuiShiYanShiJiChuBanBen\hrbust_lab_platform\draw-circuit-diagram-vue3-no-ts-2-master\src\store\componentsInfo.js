import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useComponentsInfoStore = defineStore('componentsInfo', () => {
  // 画布中的组件数组
  const components = reactive([])

  // 添加更新组件数据的 action
  const updateComponents = (newComponents) => {
    components.length = 0
    newComponents.forEach((component) => components.push(component))
  }

  // 清空组件数据
  const clearComponents = () => {
    components.length = 0
  }

  return { components, updateComponents, clearComponents }
})
