/**
 * @name connectionPointsData.js
 * <AUTHOR>
 * @description 连接点信息
 */

// 定义极性和是否有极性相关的常量对象
const POLARITY = {
  POSITIVE: 'positive', // 正极常量
  NEGATIVE: 'negative', // 负极常量
  NO: null, // 无极性
}
const HAS_POLARITY = {
  YES: true, // 有极性
  NO: false, // 无极性
}
const IS_GND = {
  YES: true, // 是地
  NO: false, // 不是地
}
const IS_INOUT = {
  IN: 'IN', // 是输入
  OUT: 'OUT', // 不是输入
  NO: null, // 不是输入和输出
}

// 电阻
export const resistorConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
]

// 可变电阻
export const rheostatConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
]

// 无极性电容
export const nonPolarizedCapacitorConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, //无极性
  },
]

// 极性电容
export const polarizedCapacitorConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 是否有极性
    polarity: POLARITY.POSITIVE, // 正极
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 是否有极性
    polarity: POLARITY.NEGATIVE, // 负极
  },
]

// 电感
export const inductorConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 9, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 9, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO, // 无极性
  },
]

// 二极管
export const diodeConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 正极
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 负极
  },
]

// 接地
export const groundConnectionPoints = [
  {
    id: 1,
    x: 20, // 描点的横坐标位置
    y: 0, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 负极
  },
]

// 电压源
export const voltageSourceConnectionPoints = [
  {
    id: 1,
    x: 20, // 描点的横坐标位置 - 下方中心连接点
    y: 20, // 描点的纵坐标位置 - 下方
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 正极
  },
]

// 开关
export const switchConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 正极
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 负极
  },
]

// 示波器
export const oscilloscopeConnectionPoints = [
  {
    id: 1,
    x: 10, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 正极
  },
  {
    id: 2,
    x: 20, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 负极
  },
]

// 电流表
export const ammeterConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 正极
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 负极
  },
]

// 电压表
export const voltmeterConnectionPoints = [
  {
    id: 1,
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 负极
  },
  {
    id: 2,
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 正极
  },
]

// 原酸放大器
export const Op_AmpConnectionPoints = [
  {
    id: 'V+',
    x: 30, // 描点的横坐标位置
    y: 0, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.POSITIVE, // 正极
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: 'V-',
    x: 30, // 描点的横坐标位置
    y: 40, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES, // 有极性
    polarity: POLARITY.NEGATIVE, // 负极
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '+',
    x: 0, // 描点的横坐标位置
    y: 30, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.IN,
    isGnd: IS_GND.NO,
  },
  {
    id: '-',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.IN,
    isGnd: IS_GND.NO,
  },
  {
    id: 'OUT',
    x: 60, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.OUT,
    isGnd: IS_GND.NO,
  },
]

// 保险管
export const FuseConnectionPoints = [
  {
    id: '1',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
  },
  {
    id: '2',
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
  },
]

// 三端稳压集成电路
export const LM78xxConnectionPoints = [
  {
    id: 'IN',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES,
    polarity: POLARITY.POSITIVE,
    isInOut: IS_INOUT.IN,
    isGnd: IS_GND.NO,
  },
  {
    id: 'OUT',
    x: 60, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES,
    polarity: POLARITY.NEGATIVE,
    isInOut: IS_INOUT.OUT,
    isGnd: IS_GND.NO,
  },
  {
    id: 'GND',
    x: 30, // 描点的横坐标位置
    y: 30, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.YES,
  },
]

// 可调电容
export const VariableCapacitorConnectionPoints = [
  {
    id: '1',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO, // 无极性
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '2',
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// 单向稳压管
export const ZenerDiodeConnectionPoints = [
  {
    id: '+',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES,
    polarity: POLARITY.POSITIVE,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '-',
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.YES,
    polarity: POLARITY.NEGATIVE,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// 双向稳压管
export const DualZenerDiodeConnectionPoints = [
  {
    id: '1',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '2',
    x: 40, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// NPN 三级管
export const Transistor_NPNConnectionPoints = [
  {
    id: 'e',
    x: 30, // 描点的横坐标位置
    y: 40, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: 'b',
    x: 0, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: 'c',
    x: 30, // 描点的横坐标位置
    y: 0, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// PNP 三极管
export const Transistor_PNPConnectionPoints = [
  {
    id: 'E',
    x: 30, // 描点的横坐标位置
    y: 0, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: 'B',
    x: 0, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: 'C',
    x: 30, // 描点的横坐标位置
    y: 40, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// 单刀双掷开关
export const SPDTSwitchConnectionPoints = [
  {
    id: '1',
    x: 0, // 描点的横坐标位置
    y: 10, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '2',
    x: 0, // 描点的横坐标位置
    y: 30, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
  {
    id: '3',
    x: 50, // 描点的横坐标位置
    y: 20, // 描点的纵坐标位置
    r: 2, // 描点的半径
    isPolar: HAS_POLARITY.NO,
    polarity: POLARITY.NO,
    isInOut: IS_INOUT.NO,
    isGnd: IS_GND.NO,
  },
]

// 添加测点的连接点
export const testPointConnectionPoints = [
  {
    id: 1,
    x: 20,
    y: 10,
    polarity: null // 测点没有极性
  } // 测点中心位置
]
// 添加测点的连接点
export const singal = [
  { id:'singal',
  	x: 10,
  	y: 30, 
  	isInOut: IS_INOUT.NO,
  } // 测点中心位置
]

