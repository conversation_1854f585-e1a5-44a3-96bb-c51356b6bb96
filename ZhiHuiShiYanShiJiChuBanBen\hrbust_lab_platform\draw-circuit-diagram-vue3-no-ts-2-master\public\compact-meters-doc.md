# 紧凑型电流表和电压表设计文档

## 设计概述

针对实验室环境中对电路仪表显示的需求，设计了一套紧凑、专业且具有实用性的电流表和电压表UI组件。这些组件模仿真实实验室仪表的特点，支持与电路的连接交互，并提供直观的数值显示。

## 设计特点

### 1. 专业紧凑

- 采用与示波器类似的紧凑设计风格
- 合理布局关键信息，保持界面简洁
- 仪表尺寸适中，不占用过多屏幕空间

### 2. 连接交互

- 提供实体连接点，支持与电路元件连接
- 连接点具有明确的标识（输入/输出，正极/负极）
- 可视化连接状态，连接后显示连接线

### 3. 可拖动定位

- 支持自由拖动调整位置
- 连接线会自动跟随仪表移动
- 拖动时保持流畅响应

### 4. 数据可视化

- 大字号显示数值，便于阅读
- 颜色区分不同类型仪表（蓝色电流，绿色电压）
- 动画效果展示数值变化

## 组件结构

### 容器结构

```
meter-container
  ├── meter-header (可拖动区域)
  │    ├── meter-title
  │    └── drag-handle
  └── meter-body
       ├── connection-points
       │    ├── connection-point (输入/正极)
       │    └── connection-point (输出/负极)
       ├── meter-display (数值显示)
       └── meter-footer (额外信息)
```

### 电流表特有结构

- 蓝色顶部边框
- 输入/输出连接点
- 蓝色数值显示

### 电压表特有结构

- 绿色顶部边框
- 正极/负极连接点
- 绿色数值显示

## 功能实现

### 1. 拖动功能

使用 JavaScript 实现拖动功能，通过 mousedown、mousemove 和 mouseup 事件处理拖动过程：

```javascript
function makeElementDraggable(elementId, handleId) {
  // 获取元素和拖动把手
  const element = document.getElementById(elementId);
  const handle = document.getElementById(handleId);
  
  // 设置拖动事件
  handle.onmousedown = function(e) {
    // 处理拖动逻辑
  };
}
```

### 2. 连接功能

实现连接点的交互和连接线的创建：

```javascript
// 处理连接点点击
function handleConnectionClick(e) {
  const point = e.currentTarget;
  
  if (!selectedPoint) {
    // 选择第一个点
    selectedPoint = point;
  } else {
    // 创建连接
    createConnection(selectedPoint, point);
    selectedPoint = null;
  }
}
```

### 3. 连接线绘制

使用 CSS 和 JavaScript 计算并绘制连接线：

```javascript
// 更新连接线位置
function updateConnectionLine(lineId) {
  // 获取两个连接点的位置
  const rect1 = point1.getBoundingClientRect();
  const rect2 = point2.getBoundingClientRect();
  
  // 计算线的长度和角度
  const length = Math.sqrt(dx*dx + dy*dy);
  const angle = Math.atan2(dy, dx) * 180 / Math.PI;
  
  // 设置连接线的位置和旋转
  line.style.width = `${length}px`;
  line.style.left = `${x1}px`;
  line.style.top = `${y1}px`;
  line.style.transform = `rotate(${angle}deg)`;
}
```

## 使用指南

### 基本使用

1. 引入 CSS 文件：
   ```html
   <link rel="stylesheet" href="compact-meters.css">
   ```

2. 添加电流表和电压表的 HTML 结构：
   ```html
   <!-- 电流表 -->
   <div class="meter-container ammeter-container" id="ammeter-1">
     <!-- 结构内容 -->
   </div>
   
   <!-- 电压表 -->
   <div class="meter-container voltmeter-container" id="voltmeter-1">
     <!-- 结构内容 -->
   </div>
   ```

3. 初始化拖拽和连接功能：
   ```javascript
   makeElementDraggable("ammeter-1", "ammeter-header-1");
   makeElementDraggable("voltmeter-1", "voltmeter-header-1");
   setupConnectionPoints();
   ```

### 连接操作

1. 点击第一个连接点（如电流表的输入）
2. 点击第二个连接点（如测试点）
3. 系统自动创建连接线
4. 拖动仪表时连接线会自动跟随

### 数值更新

通过简单的 JavaScript 调用更新数值：

```javascript
// 更新电流值
document.getElementById('ammeter-value-1').textContent = "1.25";

// 更新电压值
document.getElementById('voltmeter-value-1').textContent = "5.00";
```

## 集成方案

### 与实验环境集成

1. 将 CSS 文件添加到实验环境页面
2. 将 HTML 结构添加到适当位置
3. 初始化 JavaScript 功能
4. 连接数据源，进行实时数据更新

### 示例集成代码

```javascript
// 在实验环境加载完成后初始化仪表
document.addEventListener('DOMContentLoaded', function() {
  // 初始化电流表和电压表
  initializeMeters();
  
  // 连接到数据源
  connectToDataSource(function(data) {
    // 更新电流表和电压表的值
    updateMeterValues(data);
  });
});
```

## 后续优化

1. **多仪表支持**：支持动态添加多个电流表和电压表
2. **连接状态存储**：保存连接状态，支持页面刷新后恢复
3. **更多连接类型**：支持与更多电路元件的连接
4. **数据记录**：添加数据记录和图表显示功能
5. **主题适配**：支持深色模式等不同主题

# 电路仪表拖拽功能实现文档

## 概述

本文档详细介绍了电路实验环境中电流表和电压表的拖拽功能实现方案。该方案保留了原有的仪表UI设计风格，同时增加了全屏自由拖拽功能，提升了用户交互体验。

## 功能特点

- **保留原始UI设计**：完全保留了原有仪表的视觉风格和尺寸
- **全屏自由拖拽**：仪表可在整个实验环境中自由移动
- **边缘保护**：防止仪表被完全拖出视口
- **多设备支持**：同时支持鼠标和触摸设备的拖拽操作
- **动画流畅**：使用requestAnimationFrame优化拖拽动画，提升流畅度
- **自动检测**：自动识别页面中的仪表元素并添加拖拽功能
- **动态响应**：监听DOM变化，为新添加的仪表自动启用拖拽功能

## 技术实现

### 核心文件

1. **meters-drag.js** - 主要拖拽功能实现
2. **shiyanhuanjing_new.html** - 实验环境页面
3. **meters-preview.html** - 测试预览页面

### 代码结构

`meters-drag.js` 的主要组成部分：

#### 1. 核心拖拽功能

```javascript
function makeDraggable(elementId, handleId) {
  // 获取DOM元素
  const element = document.getElementById(elementId);
  const handle = document.getElementById(handleId) || element;
  
  // 初始化拖拽状态变量
  let isDragging = false;
  let startX, startY;
  let initialLeft, initialTop;
  
  // 使用requestAnimationFrame优化动画
  let rafId = null;
  
  // 边界检测与视觉反馈等功能实现
  // ...
}
```

#### 2. 仪表初始化

```javascript
function initializeAmmeterDrag() {
  // 初始化电流表拖拽
  // ...
}

function initializeVoltmeterDrag() {
  // 初始化电压表拖拽
  // ...
}

function initAllMetersDrag() {
  // 初始化所有仪表并监听DOM变化
  // ...
}
```

### 实现细节

#### 拖拽处理流程

1. **开始拖拽**：
   - 记录鼠标/触摸起始位置
   - 记录元素初始位置
   - 添加视觉反馈（阴影、透明度变化）
   - 提升元素z-index值

2. **拖拽过程**：
   - 计算位移差值
   - 使用requestAnimationFrame优化动画性能
   - 应用边界检测，防止元素完全移出视口
   - 更新元素位置

3. **结束拖拽**：
   - 移除事件监听
   - 恢复元素视觉状态
   - 恢复正常z-index值

#### 自动检测与初始化

使用MutationObserver监听DOM变化，自动为新添加的仪表元素启用拖拽功能：

```javascript
const observer = new MutationObserver((mutations) => {
  let needsUpdate = false;
  
  mutations.forEach(mutation => {
    if (mutation.type === 'childList' && mutation.addedNodes.length) {
      mutation.addedNodes.forEach(node => {
        if (node.classList && 
           (node.classList.contains('ammeter-component') || 
            node.classList.contains('voltmeter-component'))) {
          needsUpdate = true;
        }
      });
    }
  });
  
  if (needsUpdate) {
    initializeAmmeterDrag();
    initializeVoltmeterDrag();
  }
});
```

## 使用方法

### 1. 引入脚本

在HTML页面的head部分引入拖拽脚本：

```html
<script src="meters-drag.js"></script>
```

### 2. 初始化

在页面加载完成后初始化拖拽功能：

```html
<script>
  document.addEventListener('DOMContentLoaded', function() {
    initAllMetersDrag();
  });
</script>
```

### 3. HTML结构

确保电流表和电压表使用正确的类名和结构：

```html
<!-- 电流表 -->
<div class="ammeter-component" id="ammeter-1">
  <div class="ammeter-header" id="ammeter-header-1">
    <!-- 表头内容 -->
  </div>
  <div class="ammeter-content">
    <!-- 表身内容 -->
  </div>
</div>

<!-- 电压表 -->
<div class="voltmeter-component" id="voltmeter-1">
  <div class="voltmeter-header" id="voltmeter-header-1">
    <!-- 表头内容 -->
  </div>
  <div class="voltmeter-content">
    <!-- 表身内容 -->
  </div>
</div>
```

## 注意事项

1. 确保每个仪表元素都有唯一的ID
2. 表头元素需要单独的ID用于拖拽把手
3. 使用绝对定位（position: absolute）确保仪表可以自由定位
4. 建议在拖拽区域的父容器添加类名 "circuit-container" 用于边界检测

## 性能优化

1. 使用requestAnimationFrame代替直接DOM操作，提高动画流畅度
2. 使用事件委托减少事件监听器数量
3. 拖拽结束后取消未完成的动画帧请求，避免不必要的计算
4. 延迟初始化，确保其他组件已加载完成

## 兼容性

- 支持现代浏览器：Chrome, Firefox, Safari, Edge
- 支持触摸设备和鼠标操作
- 响应式设计，适应不同屏幕尺寸 