import {
  getdianwieqi,
  csdianyajizhun,
  csdianliu,
  cskebiandianrong,
  cskebiandianzu,
  kaiguanliang,
  csshiboqi,
  duojiIoPianYi,
} from '@/api/sendCommand'
import { ElNotification } from 'element-plus'

// 电位器
const send_getdianwieqi = (ip, canshuxz = 16, banhao, dianweiqi, dushu) => {
  getdianwieqi(ip, canshuxz, banhao, dianweiqi, dushu)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: '[电位器] 指令发送成功',
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: '[电位器] 指令发送失败',
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: '[电位器] 指令发送失败',
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 测试电压基准
const send_csdianyajizhun = (ip, param, param2) => {
  csdianyajizhun(ip, param, param2)
    .then((res) => {
      console.log('@@@ [测试电压基准] res', res)
      if (res.data.status === 200) {
        ElNotification({
          title: '[测试电压基准] 指令发送成功',
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: '[测试电压基准] 指令发送失败',
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: '[测试电压基准] 指令发送失败',
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 测试电流
const send_csdianliu = (ip, param, param2) => {
  csdianliu(ip, param, param2)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: '[测试电流] 指令发送成功',
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: '[测试电流] 指令发送失败',
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: '[测试电流] 指令发送失败',
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 测试可变电容
const send_cskebiandianrong = (ip, param, param2) => {
  cskebiandianrong(ip, param, param2)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: '[测试可变电容] 指令发送成功',
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: '[测试可变电容] 指令发送失败',
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: '[测试可变电容] 指令发送失败',
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 测试可变电阻
const send_cskebiandianzu = (ip, param, param2) => {
  cskebiandianzu(ip, param, param2)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: '[测试可变电阻] 指令发送成功',
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: '[测试可变电阻] 指令发送失败',
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: '[测试可变电阻] 指令发送失败',
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 继电器-开关量
const send_kaiguanliang = (duojiip, sort, param) => {
  kaiguanliang(duojiip, sort, param)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: `[继电器K${sort}-开关] 指令发送成功`,
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: `[继电器K${sort}-开关] 指令发送失败`,
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: `[继电器K${sort}-开关] 指令发送失败`,
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 示波器
const send_csshiboqi = (ip, param, param2) => {
  csshiboqi(ip, param, param2)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: `[测试示波器] 指令发送成功`,
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: `[测试示波器] 指令发送失败`,
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: `[测试示波器] 指令发送失败`,
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

// 舵机连线
const send_duojiIoPianYi = (ip, duoji, iopbianhao, pyl) => {
  duojiIoPianYi(ip, duoji, iopbianhao, pyl)
    .then((res) => {
      if (res.data.status === 200) {
        ElNotification({
          title: `[舵机 ${duoji}-${iopbianhao}-${pyl} 连线] 指令发送成功`,
          message: res.data.result.optMessage,
          type: 'success',
          duration: 2000,
        })
      } else {
        ElNotification({
          title: `[舵机 ${duoji}-${iopbianhao}-${pyl} 连线] 指令发送失败`,
          message: '',
          type: 'error',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      ElNotification({
        title: `[舵机 ${duoji}-${iopbianhao}-${pyl} 连线] 指令发送失败`,
        message: '',
        type: 'error',
        duration: 2000,
      })
    })
}

export {
  send_getdianwieqi,
  send_csdianyajizhun,
  send_csdianliu,
  send_cskebiandianrong,
  send_cskebiandianzu,
  send_kaiguanliang,
  send_csshiboqi,
  send_duojiIoPianYi,
}
