import fs from 'fs'
import path from 'path'

function traverseDirectory(directory) {
  fs.readdirSync(directory).forEach((file) => {
    const filePath = path.join(directory, file)
    const stats = fs.statSync(filePath)
    if (stats.isFile()) {
      const fileName = path.basename(filePath)
      const fileNameWithoutExtension = path.basename(filePath, path.extname(filePath))
      console.log(fileNameWithoutExtension)
    } else if (stats.isDirectory()) {
      traverseDirectory(filePath)
    }
  })
}

// 调用 traverseDirectory 函数，以当前目录作为参数
traverseDirectory('./')
