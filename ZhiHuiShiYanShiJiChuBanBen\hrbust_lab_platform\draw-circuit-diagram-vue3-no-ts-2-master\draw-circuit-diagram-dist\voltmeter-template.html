<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电压表组件</title>
  <link rel="stylesheet" href="voltmeter.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body style="background-color: #f5f5f5; padding: 20px; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0;">
  
  <!-- 电压表组件 -->
  <div class="voltmeter-component" id="voltmeter-1">
    <div class="voltmeter-header" id="voltmeter-header-1">
      <div class="voltmeter-title">
        <span class="material-icons voltmeter-icon">bolt</span>
        <span>电压表</span>
        <small style="margin-left: 5px; font-size: 11px; color: #6c757d;">V1</small>
      </div>
      <span class="material-icons voltmeter-drag-handle">drag_indicator</span>
    </div>
    
    <div class="voltmeter-content">
      <div class="voltmeter-display">
        <div class="voltmeter-value" id="voltmeter-value-1">0.00</div>
        <div class="voltmeter-unit">伏特 (V)</div>
      </div>
      
      <div class="voltmeter-info">
        <div class="voltmeter-label">电压测量</div>
        <div class="voltmeter-range">量程: 0-30V</div>
      </div>
    </div>
  </div>

  <script>
    // 使元素可拖拽的函数
    function makeElementDraggable(elementId, handleId) {
      const element = document.getElementById(elementId);
      const handle = document.getElementById(handleId);
      
      if (!element || !handle) return;
      
      let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
      
      handle.onmousedown = dragMouseDown;
      
      function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();
        
        // 获取鼠标初始位置
        pos3 = e.clientX;
        pos4 = e.clientY;
        
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
      }
      
      function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();
        
        // 计算新位置
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        
        // 设置元素新位置
        element.style.top = (element.offsetTop - pos2) + "px";
        element.style.left = (element.offsetLeft - pos1) + "px";
      }
      
      function closeDragElement() {
        // 停止拖拽
        document.onmouseup = null;
        document.onmousemove = null;
      }
    }

    // 初始化拖拽功能
    document.addEventListener('DOMContentLoaded', function() {
      makeElementDraggable("voltmeter-1", "voltmeter-header-1");
      
      // 模拟数值变化的演示功能
      setInterval(function() {
        const value = (Math.random() * 12).toFixed(2);
        const valueElement = document.getElementById('voltmeter-value-1');
        valueElement.classList.add('updating');
        
        setTimeout(function() {
          valueElement.textContent = value;
          valueElement.classList.remove('updating');
        }, 500);
      }, 3000);
    });
  </script>
</body>
</html> 