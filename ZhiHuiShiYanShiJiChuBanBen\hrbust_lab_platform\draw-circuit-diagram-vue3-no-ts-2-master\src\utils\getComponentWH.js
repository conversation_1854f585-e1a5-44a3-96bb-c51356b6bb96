/**
 * 获取组件的宽度和高度
 * @param {Object} component - 组件对象
 * @returns {Object} 包含组件宽度和高度的对象
 */
export function getComponentWH(component) {
  // 默认宽高
  let componentWidth = component.width || 50;
  let componentHeight = component.height || 50;
  
  // 考虑组件的缩放比例
  if (component.scale) {
    componentWidth *= component.scale;
    componentHeight *= component.scale;
  }
  
  return {
    componentWidth,
    componentHeight
  };
} 