// store/lineStateStore.js
import { ref, reactive } from 'vue'
import { defineStore } from 'pinia'
import { useLineModeStore } from '@/store/lineMode.js'

export const useLineStateStore = defineStore('lineState', () => {
  /**
   * store: 连线模式
   */
  const lineModeStore = useLineModeStore()

  const hoveredLine = ref(null) // 悬停线段索引
  const selectedLine = ref(null) // 选中线段索引
  const fixedLines = reactive([]) // 固定线段集合
  const intersections = reactive([]) // 交点集合

  const updateFixedLines = (newFixedLines) => {
    fixedLines.length = 0
    newFixedLines.forEach((line) => fixedLines.push(line))
  }

  const updateIntersections = (newIntersections) => {
    intersections.length = 0
    newIntersections.forEach((intersection) => intersections.push(intersection))
  }

  const setHoveredLine = (index) => {
    if (lineModeStore.lineMode === 'disabled') {
      hoveredLine.value = index
    }
  }

  const clearHoveredLine = () => {
    hoveredLine.value = null
  }

  const setSelectedLine = (index) => {
    if (lineModeStore.lineMode === 'disabled') {
      selectedLine.value = index
    }
  }

  const clearSelectedLine = () => {
    selectedLine.value = null
  }

  const setFixedLines = (lines) => {
    fixedLines.splice(0, fixedLines.length, ...lines)
  }

  const addFixedLine = (line) => {
    fixedLines.push(line)
  }

  // 清空 固定线段集合
  const clearFixedLines = () => {
    fixedLines.length = 0
  }
  // 清空 交点集合
  const clearIntersections = () => {
    intersections.length = 0
  }

  return {
    selectedLine,
    hoveredLine,
    fixedLines,
    intersections,
    setSelectedLine,
    clearSelectedLine,
    setHoveredLine,
    clearHoveredLine,
    setFixedLines,
    addFixedLine,
    updateFixedLines,
    updateIntersections,
    clearFixedLines,
    clearIntersections,
  }
})
