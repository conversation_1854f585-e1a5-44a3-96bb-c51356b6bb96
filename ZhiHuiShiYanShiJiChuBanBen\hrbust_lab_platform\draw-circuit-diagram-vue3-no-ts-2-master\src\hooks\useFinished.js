import { storeToRefs } from 'pinia'
import { reactive } from 'vue'
import { ElMessageBox } from 'element-plus'
import { getCurrentTime } from '@/utils/getCurrentTime'
import { getComponentWH } from '@/utils/getComponentWH'
import {
  send_duojiIoPianYi,
} from '@/utils/sendCommandUtils'
import { saveData, getStoreData } from '@/utils/canvasDataManager.js'
import html2canvas from 'html2canvas'
import { ElNotification } from 'element-plus'

/**
 * 点击完成按钮之后的操作
 */
export default function () {
  const finished = async () => {
    try {
      // 显示加载提示
      const loadingIndicator = document.createElement('div');
      loadingIndicator.innerHTML = '正在保存电路图...<br><small>这可能需要几秒钟时间</small>';
      loadingIndicator.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 9999;
        text-align: center;
      `;
      document.body.appendChild(loadingIndicator);

      // 1. 保存当前电路图数据到数据库
      console.log('💾 完成按钮：开始保存电路图数据到数据库...')
      await saveData();
      console.log('✅ 完成按钮：电路图数据保存完成')

      // 获取画布元素
      const canvasElement = document.getElementById('canvas-area');
      if (!canvasElement) {
        console.error('找不到画布元素');
        document.body.removeChild(loadingIndicator);
        return;
      }

      // 获取网格大小
      const gridSize = 20;
      localStorage.setItem('circuit_grid_size', gridSize.toString());
      
      // 获取画布尺寸
      const canvasRect = canvasElement.getBoundingClientRect();
      const canvasWidth = canvasRect.width;
      const canvasHeight = canvasRect.height;

      // 收集可变电阻组件信息
      const variableResistors = [];
      const resistorElements = document.querySelectorAll('.component[data-component-type="rheostat"]');
      
      resistorElements.forEach((element, index) => {
        const resistorId = element.id || `resistor-${index}`;
        const resistorLabel = element.dataset.label || `可变电阻${index + 1}`;
        
        variableResistors.push({
          id: resistorId,
          label: resistorLabel,
          defaultValue: 50, // 默认电阻值百分比
          name: '可变电阻'
        });
      });

      // 如果没有找到可变电阻，尝试其他可能的选择器
      if (variableResistors.length === 0) {
        const alternativeSelectors = [
          '.component[data-type="rheostat"]',
          '.rheostat',
          '[data-component="rheostat"]',
          '.component[data-component-type="variable-resistor"]',
          '.component[data-type="variable-resistor"]'
        ];
        
        for (const selector of alternativeSelectors) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            elements.forEach((element, index) => {
              const resistorId = element.id || `resistor-${index}`;
              const resistorLabel = element.dataset.label || `可变电阻${index + 1}`;
              const name = element.dataset.name || '可变电阻';
              
              variableResistors.push({
                id: resistorId,
                label: resistorLabel,
                defaultValue: 55,
                name: name
              });
            });
            break;
          }
        }
      }

      // 如果仍然没有找到可变电阻，添加一个默认的
      if (variableResistors.length === 0) {
        variableResistors.push({
          id: 'default-resistor',
          label: '默认可变电阻',
          defaultValue: 55
        });
      }

      // 收集测试点并计算百分比位置
      let measurementPoints = [];
      const testPointElements = document.querySelectorAll('.test-point');
      
      testPointElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        
        // 获取元素中心点相对于画布的位置
        const centerX = rect.left + rect.width/2 - canvasRect.left;
        const centerY = rect.top + rect.height/2 - canvasRect.top;
        
        // 计算最接近的网格交叉点
        const gridX = Math.round(centerX / gridSize);
        const gridY = Math.round(centerY / gridSize);
        
        // 使用网格坐标计算百分比
        const percentX = (gridX * gridSize / canvasWidth) * 100;
        const percentY = (gridY * gridSize / canvasHeight) * 100;
        
        // 记录原始计算值用于调试
        console.log(`测点${index + 1}: 原始位置(${centerX},${centerY}), 网格(${gridX},${gridY}), 百分比(${percentX.toFixed(2)}%,${percentY.toFixed(2)}%)`);
        
        measurementPoints.push({
          id: element.dataset.id || `point-${index}`,
          label: element.dataset.label || `测点${index+1}`,
          x: percentX,
          y: percentY,
          original: {
            centerX, centerY,
            gridX, gridY
          }
        });
      });

      // 保存测试点数据
      const testPointsJSON = JSON.stringify(measurementPoints);
      localStorage.setItem('circuit_test_points', testPointsJSON);
      localStorage.setItem('variable_resistors', JSON.stringify(variableResistors));

      // 获取测点周围组件信息（使用提取函数）
      const { components } = getStoreData();
      const { extractTestPoints } = await import('@/utils/canvasDataManager.js');
      
      // 🔧 修复：安全访问组件数据，防止删除连线后的状态更新错误
      if (components && components.value) {
        try {
          const componentsArray = components?.value || []
          if (componentsArray.length > 0) {
            const testPointsWithSurrounding = extractTestPoints(componentsArray);
            // 保存包含周围器件信息的测点数据
            localStorage.setItem('circuit_test_points_with_surrounding', JSON.stringify(testPointsWithSurrounding));
            console.log('测点周围器件信息已保存:', testPointsWithSurrounding);
          } else {
            console.warn('⚠️ 组件数组为空，跳过测点提取')
          }
        } catch (error) {
          console.error('❌ 提取测点信息时出错:', error)
        }
      } else {
        console.warn('⚠️ 组件数据不可用，跳过测点提取')
      }

			let switchDoms = [];
      const switchElements = document.querySelectorAll('.switch');
      switchElements.forEach((element, index) => {
      	switchDoms.push({
          id: element.dataset.id,
          label: element.dataset.label,
          
        });
      })
      localStorage.setItem('switchDoms', JSON.stringify(switchDoms));
      // 处理电路图
      try {
        const canvasClone = canvasElement.cloneNode(true);
        
        // 找到所有组件，确保它们可见
        const componentElements = canvasClone.querySelectorAll('.component');
        componentElements.forEach(el => {
          el.style.visibility = 'visible';
          el.style.display = 'block';
          el.style.opacity = '1';
        });
        
        // 确保网格线可见
        const gridElements = canvasClone.querySelectorAll('svg.grid, .grid-container, .grid-line, [class*="grid"]');
        gridElements.forEach(el => {
          el.style.visibility = 'visible';
          el.style.display = 'block';
          el.style.opacity = '1';
        });
        
        // 设置背景
        canvasClone.style.background = 'white';
        canvasClone.style.backgroundColor = 'white';
        
        // 在Canvas上标记测点位置
        measurementPoints.forEach((point, index) => {
          // 创建测点标记元素
          const marker = document.createElement('div');
          marker.style.position = 'absolute';
          
          const x = point.original.gridX * gridSize;
          const y = point.original.gridY * gridSize;
          
          marker.style.left = `${x}px`;
          marker.style.top = `${y}px`;
          marker.style.width = '16px';
          marker.style.height = '16px';
          marker.style.backgroundColor = '#ff5722';
          marker.style.borderRadius = '50%';
          marker.style.transform = 'translate(-50%, -50%)';
          marker.style.border = '2px solid white';
          marker.style.boxShadow = '0 0 4px rgba(0,0,0,0.5)';
          marker.style.zIndex = '1000';
          
          // 添加标签
          const label = document.createElement('div');
          label.style.position = 'absolute';
          label.style.top = '-20px';
          label.style.left = '50%';
          label.style.transform = 'translateX(-50%)';
          label.style.backgroundColor = 'rgba(0,0,0,0.7)';
          label.style.color = 'white';
          label.style.padding = '2px 6px';
          label.style.borderRadius = '3px';
          label.style.fontSize = '10px';
          label.style.whiteSpace = 'nowrap';
          label.style.zIndex = '1001';
          label.textContent = point.label;
          
          marker.appendChild(label);
          canvasClone.appendChild(marker);
        });
        
        // 临时将克隆的元素添加到DOM并隐藏
        canvasClone.style.position = 'absolute';
        canvasClone.style.top = '-9999px';
        canvasClone.style.left = '-9999px';
        document.body.appendChild(canvasClone);
        
        // 使用html2canvas将DOM元素转换为canvas
        html2canvas(canvasClone, {
          backgroundColor: 'white',
          scale: 1,
          logging: true,
          useCORS: true,
          allowTaint: true
        }).then(canvas => {
          const imageDataURL = canvas.toDataURL('image/png');
          
          // 保存图片数据
          localStorage.setItem('temp_circuit_png', imageDataURL);
          localStorage.setItem('circuit_image', imageDataURL);
          
          // 保存图片尺寸信息
          localStorage.setItem('circuit_image_width', canvas.width);
          localStorage.setItem('circuit_image_height', canvas.height);
          
          console.log('电路图已保存到localStorage，尺寸:', canvas.width, 'x', canvas.height);
          
          // 清理DOM
          if (document.body.contains(canvasClone)) {
            document.body.removeChild(canvasClone);
          }
          if (document.body.contains(loadingIndicator)) {
            document.body.removeChild(loadingIndicator);
          }
          
          // 显示成功通知
          ElNotification({
            title: '保存成功',
            message: '电路图及相关数据已保存到本地存储',
            type: 'success',
            duration: 2000
          });
        }).catch(error => {
          console.error('生成电路图失败:', error);
          if (document.body.contains(canvasClone)) {
            document.body.removeChild(canvasClone);
          }
          if (document.body.contains(loadingIndicator)) {
            document.body.removeChild(loadingIndicator);
          }
          
          ElNotification({
            title: '保存失败',
            message: error.message,
            type: 'error',
            duration: 3000
          });
        });
      } catch (error) {
        console.error('处理电路图失败:', error);
        if (document.body.contains(loadingIndicator)) {
          document.body.removeChild(loadingIndicator);
        }
        
        ElNotification({
          title: '保存失败',
          message: error.message,
          type: 'error',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('完成操作失败:', error);
      ElNotification({
        title: '保存失败',
        message: error.message,
        type: 'error',
        duration: 3000
      });
    }
  };

  return { finished };
}
