<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测点电阻识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .component-list {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .component-item {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .component-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测点电阻识别功能测试</h1>
        <p>此页面用于测试修复后的测点识别电阻和可变电阻功能。</p>
        
        <div class="test-section">
            <div class="test-title">1. 组件类型识别测试</div>
            <button onclick="testComponentTypeRecognition()">测试组件类型识别</button>
            <div id="typeTestResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 模拟组件数据测试</div>
            <button onclick="testMockComponents()">测试模拟组件数据</button>
            <div id="mockTestResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 连接关系测试</div>
            <button onclick="testConnectionLogic()">测试连接关系逻辑</button>
            <div id="connectionTestResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 使用说明</div>
            <div class="info test-result">
                <h4>如何测试修复效果：</h4>
                <ol>
                    <li>在电路图中放置一些电阻和可变电阻组件</li>
                    <li>添加测点并将其连接到电阻组件</li>
                    <li>鼠标悬停在测点上，查看是否能正确显示连接的电阻信息</li>
                    <li>检查控制台输出，查看详细的调试信息</li>
                </ol>
                <h4>预期结果：</h4>
                <ul>
                    <li>测点应该能识别并显示"电阻"类型</li>
                    <li>测点应该能识别并显示"可变电阻"类型</li>
                    <li>显示正确的组件标识符（如R1, R2等）</li>
                    <li>控制台应该输出详细的匹配过程</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟getComponentTypeName函数进行测试
        function getComponentTypeName(type) {
            if (!type) return '未知器件';
            
            console.log('获取组件类型名称，输入类型:', type);
            
            const lowerType = type.toLowerCase();
            
            // 精确匹配常见组件类型
            const typeMapping = {
                'resistor': '电阻',
                'rheostat': '可变电阻',
                'potentiometer': '可变电阻',
                'capacitor': '电容',
                'nonpolarizedcapacitor': '无极性电容',
                'polarizedcapacitor': '极性电容',
                'inductor': '电感',
                'diode': '二极管'
            };
            
            // 首先尝试精确匹配
            if (typeMapping[lowerType]) {
                console.log('精确匹配到类型:', typeMapping[lowerType]);
                return typeMapping[lowerType];
            }
            
            // 模糊匹配
            if (type.includes('可变电阻') || lowerType.includes('rheostat')) {
                return '可变电阻';
            } else if (type.includes('电阻') || lowerType.includes('resistor')) {
                return '电阻';
            }
            
            return type;
        }

        function testComponentTypeRecognition() {
            const testTypes = [
                'resistor',
                'rheostat', 
                'potentiometer',
                'Resistor',
                'RHEOSTAT',
                '电阻',
                '可变电阻',
                'unknown_type'
            ];
            
            let results = '<div class="component-list">';
            results += '<h4>组件类型识别结果：</h4>';
            
            testTypes.forEach(type => {
                const result = getComponentTypeName(type);
                const isCorrect = (
                    (type.toLowerCase().includes('resistor') && result === '电阻') ||
                    (type.toLowerCase().includes('rheostat') && result === '可变电阻') ||
                    (type.toLowerCase().includes('potentiometer') && result === '可变电阻') ||
                    (type.includes('电阻') && !type.includes('可变') && result === '电阻') ||
                    (type.includes('可变电阻') && result === '可变电阻')
                );
                
                results += `<div class="component-item">
                    <strong>输入:</strong> ${type} → 
                    <strong>输出:</strong> ${result} 
                    <span style="color: ${isCorrect ? 'green' : 'orange'}">${isCorrect ? '✓' : '?'}</span>
                </div>`;
            });
            
            results += '</div>';
            document.getElementById('typeTestResult').innerHTML = results;
        }

        function testMockComponents() {
            const mockComponents = [
                { id: 'r1', type: 'resistor', identifier: 'R1', label: '电阻1' },
                { id: 'r2', type: 'rheostat', identifier: 'R2', label: '可变电阻1' },
                { id: 'c1', type: 'capacitor', identifier: 'C1', label: '电容1' }
            ];
            
            let results = '<div class="component-list">';
            results += '<h4>模拟组件数据：</h4>';
            
            mockComponents.forEach(comp => {
                const typeName = getComponentTypeName(comp.type);
                results += `<div class="component-item">
                    <strong>ID:</strong> ${comp.id}, 
                    <strong>类型:</strong> ${comp.type} → ${typeName}, 
                    <strong>标识:</strong> ${comp.identifier}, 
                    <strong>标签:</strong> ${comp.label}
                </div>`;
            });
            
            results += '</div>';
            document.getElementById('mockTestResult').innerHTML = results;
        }

        function testConnectionLogic() {
            const result = `
                <div class="info test-result">
                    <h4>连接关系测试说明：</h4>
                    <p>连接关系测试需要在实际的Vue应用中进行，因为它依赖于：</p>
                    <ul>
                        <li>verifyStore.generateConnectionRelationships() 函数</li>
                        <li>实际的组件数据和连接线路</li>
                        <li>测点组件的位置信息</li>
                    </ul>
                    <p><strong>请在主应用中进行以下测试：</strong></p>
                    <ol>
                        <li>创建一个电路，包含电阻和可变电阻</li>
                        <li>添加测点并连接到这些组件</li>
                        <li>鼠标悬停在测点上查看识别结果</li>
                        <li>查看浏览器控制台的详细日志</li>
                    </ol>
                </div>
            `;
            document.getElementById('connectionTestResult').innerHTML = result;
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            console.log('测点电阻识别测试页面已加载');
            console.log('请点击测试按钮进行各项功能测试');
        };
    </script>
</body>
</html>
