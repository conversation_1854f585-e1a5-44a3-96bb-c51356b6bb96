import service from '@/utils/request'

/**
 * 获取模板的测点连接关系
 * @param {Object} data - 请求参数，包含templateId、courseId等信息
 * @returns {Promise} - 返回请求Promise
 */
export function getTemplateTestPointRelations(data) {
  return service({
    url: '/testPointRelations/getTemplate',
    method: 'post',
    data,
  })
}

/**
 * 获取学生的测点连接关系
 * @param {Object} data - 请求参数，包含userId、courseId、experimentId等信息
 * @returns {Promise} - 返回请求Promise
 */
export function getStudentTestPointRelations(data) {
  return service({
    url: '/testPointRelations/getStudent',
    method: 'post',
    data,
  })
}

/**
 * 保存测点连接关系
 * @param {Object} data - 请求参数，包含userId、courseId、experimentId、testPointRelations等
 * @returns {Promise} - 返回请求Promise
 */
export function saveTestPointRelations(data) {
  return service({
    url: '/testPointRelations/save',
    method: 'post',
    data,
  })
}

/**
 * 比对学生测点连接关系与模板的匹配度
 * @param {Object} data - 请求参数，包含templateId、studentId、courseId、experimentId等
 * @returns {Promise} - 返回请求Promise
 */
export function compareTestPointRelations(data) {
  return service({
    url: '/testPointRelations/compare',
    method: 'post',
    data,
  })
} 