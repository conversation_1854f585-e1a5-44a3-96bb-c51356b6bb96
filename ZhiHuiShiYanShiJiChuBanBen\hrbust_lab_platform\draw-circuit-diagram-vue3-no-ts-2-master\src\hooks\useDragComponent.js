import { storeToRefs } from 'pinia'
import { useCanvasInfoStore } from '@/store/canvasInfo'
import { useComponentInfoStore } from '@/store/componentInfo'
import { useLineModeStore } from '@/store/lineMode'
import { useTextBoxStore } from '@/store/textBox'
import { useLineStateStore } from '@/store/lineState'

export default function useDragComponent() {
  // store: 获取 useCanvasInfoStore 实例，并解构
  const canvasInfoStore = useCanvasInfoStore()
  const { canvasSize } = storeToRefs(canvasInfoStore) // 获取网格间距
  const { getClosestGridPoint } = canvasInfoStore

  // store: 获取 useComponentInfoStore 实例，并解构
  const componentInfoStore = useComponentInfoStore()
  const { getComponentWH } = componentInfoStore

  // store: 获取 useComponentInfoStore 实例，并解构
  const { isLineDrawing } = storeToRefs(useLineModeStore())

  // store: 获取 useTextBoxStore 实例，并解构
  const textBoxStore = useTextBoxStore()
  const { updateTextBoxesPositionByComponent } = textBoxStore

  // 当前正在拖拽的组件
  let currentDraggedComponent = null
  let offsetX = 0
  let offsetY = 0

  // 开始拖拽
  const startDrag = (component, event) => {
    // 如果当前是连线模式，则不允许拖拽
    if (isLineDrawing.value) {
      return
    }

    // 只在点击组件的空白区域时才进行拖拽
    if (event.target.classList.contains('connection-point')) return // 如果点击的是连接点，跳过拖拽

    event.preventDefault()

    // 初始化拖拽状态
    currentDraggedComponent = component
    offsetX = event.clientX - component.x
    offsetY = event.clientY - component.y

    // 绑定鼠标移动和松开事件
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('mouseup', stopDrag)

    // 在拖拽开始时更新连线等其他操作（如果有） TODO
    // updateLines()
  }

  // 处理拖拽移动
  const handleMouseMove = (event) => {
    if (!currentDraggedComponent) {
      return
    }

    // 计算当前鼠标位置相对于组件的偏移
    const rawX = event.clientX - offsetX
    const rawY = event.clientY - offsetY

    // 获取最接近的网格点
    const closestPoint = getClosestGridPoint(rawX, rawY)

    // 更新组件位置
    currentDraggedComponent.x = closestPoint.x
    currentDraggedComponent.y = closestPoint.y

    // 获取组件的宽度和高度
    const { componentWidth, componentHeight } = getComponentWH(currentDraggedComponent)

    // 限制拖拽范围（比如，防止组件拖出画布）
    currentDraggedComponent.x = Math.max(0, Math.min(canvasSize.value.width - componentWidth, closestPoint.x))
    currentDraggedComponent.y = Math.max(0, Math.min(canvasSize.value.height - componentHeight, closestPoint.y))

    // 更新文本框位置
    updateTextBoxesPositionByComponent(currentDraggedComponent.componentId)
  }

  // 停止拖拽
  const stopDrag = () => {
    if (currentDraggedComponent) {
      // 如果拖动的是测点，保存电路图以更新测点周围器件信息
      if (currentDraggedComponent.type === 'testPoint' || currentDraggedComponent.isTestPoint) {
        // 异步调用saveData以确保UI更新完成
        setTimeout(() => {
          const { saveData } = require('@/utils/canvasDataManager');
          saveData();
          console.log('测点被移动，已更新周围器件信息');
        }, 100);
      } else {
        // 普通器件拖拽结束后，检查是否需要更新连线
        handleComponentDragEnd(currentDraggedComponent);
      }
    }

    currentDraggedComponent = null;
    offsetX = 0;
    offsetY = 0;

    // 移除鼠标移动和松开事件
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', stopDrag);
  }

  // 处理器件拖拽结束后的连线更新
  const handleComponentDragEnd = (component) => {
    try {
      // 使用已导入的store
      const lineStateStore = useLineStateStore();
      const { fixedLines } = lineStateStore;

      console.log(`🔧 器件 ${component.identifier || component.id} 拖拽结束，检查连线更新`);

      // 检查器件是否拖拽到现有连线上
      const componentX = component.x;
      const componentY = component.y;

      // 检查器件的连接点是否与现有连线重叠
      let hasOverlap = false;
      const overlappingLines = [];

      fixedLines.forEach((line, lineIndex) => {
        // 检查器件是否与这条线重叠
        if (isComponentOnLine(component, line)) {
          overlappingLines.push({ line, index: lineIndex });
          hasOverlap = true;
        }
      });

      if (hasOverlap && overlappingLines.length > 0) {
        console.log(`🎯 检测到器件与 ${overlappingLines.length} 条连线重叠，开始处理连线`);

        // 处理重叠的连线
        overlappingLines.forEach(({ line, index }) => {
          handleLineOverlap(component, line, index);
        });
      }

    } catch (error) {
      console.error('处理器件拖拽结束时出错:', error);
    }
  }

  // 检查器件是否在连线上
  const isComponentOnLine = (component, line) => {
    // 简化检查：检查器件中心点是否接近连线
    const componentCenterX = component.x + (component.width || 40) / 2;
    const componentCenterY = component.y + (component.height || 20) / 2;

    // 检查器件中心点是否在连线的某个线段上
    for (let i = 0; i < line.length - 1; i++) {
      const startPoint = line[i];
      const endPoint = line[i + 1];

      // 计算点到线段的距离
      const distance = pointToLineDistance(
        componentCenterX, componentCenterY,
        startPoint.x, startPoint.y,
        endPoint.x, endPoint.y
      );

      // 如果距离小于阈值，认为器件在线上
      if (distance < 30) { // 30像素阈值
        return true;
      }
    }

    return false;
  }

  // 计算点到线段的距离
  const pointToLineDistance = (px, py, x1, y1, x2, y2) => {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
      // 线段退化为点
      return Math.sqrt(A * A + B * B);
    }

    let param = dot / lenSq;

    let xx, yy;
    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // 处理连线重叠
  const handleLineOverlap = (component, line, lineIndex) => {
    try {
      const lineStateStore = useLineStateStore();

      console.log(`🔧 处理器件 ${component.identifier} 与连线 ${lineIndex} 的重叠`);

      // 获取器件的连接点
      if (!component.connectionPoints || component.connectionPoints.length === 0) {
        console.log('❌ 器件没有连接点，跳过处理');
        return;
      }

      // 找到器件最接近连线的连接点
      let bestConnectionPoint = null;
      let minDistanceToLine = Infinity;

      component.connectionPoints.forEach(connectionPoint => {
        const cpX = connectionPoint.point.x;
        const cpY = connectionPoint.point.y;

        // 计算连接点到连线的最短距离
        let minDistToLine = Infinity;
        for (let i = 0; i < line.length - 1; i++) {
          const segmentDistance = pointToLineDistance(
            cpX, cpY,
            line[i].x, line[i].y,
            line[i + 1].x, line[i + 1].y
          );
          minDistToLine = Math.min(minDistToLine, segmentDistance);
        }

        if (minDistToLine < minDistanceToLine) {
          minDistanceToLine = minDistToLine;
          bestConnectionPoint = connectionPoint;
        }
      });

      if (!bestConnectionPoint || minDistanceToLine > 30) {
        console.log('❌ 没有找到足够接近的连接点');
        return;
      }

      const connectionX = bestConnectionPoint.point.x;
      const connectionY = bestConnectionPoint.point.y;

      console.log(`🎯 使用连接点 (${connectionX}, ${connectionY})`);

      // 找到连接点在连线上的最佳插入位置
      let bestSegmentIndex = 0;
      let minSegmentDistance = Infinity;
      let projectionPoint = null;

      for (let i = 0; i < line.length - 1; i++) {
        const startPoint = line[i];
        const endPoint = line[i + 1];

        // 计算连接点在这个线段上的投影
        const projection = getProjectionOnSegment(
          connectionX, connectionY,
          startPoint.x, startPoint.y,
          endPoint.x, endPoint.y
        );

        const distance = Math.sqrt(
          Math.pow(projection.x - connectionX, 2) +
          Math.pow(projection.y - connectionY, 2)
        );

        if (distance < minSegmentDistance) {
          minSegmentDistance = distance;
          bestSegmentIndex = i;
          projectionPoint = projection;
        }
      }

      if (!projectionPoint) {
        console.log('❌ 无法找到投影点');
        return;
      }

      // 创建新的连线路径
      const newLine = [];

      // 添加投影点之前的所有点
      for (let i = 0; i <= bestSegmentIndex; i++) {
        newLine.push(line[i]);
      }

      // 添加器件连接点
      newLine.push({
        x: connectionX,
        y: connectionY
      });

      // 添加投影点之后的所有点
      for (let i = bestSegmentIndex + 1; i < line.length; i++) {
        newLine.push(line[i]);
      }

      // 更新连线
      lineStateStore.fixedLines[lineIndex] = newLine;

      console.log(`✅ 已将器件连接点正确插入到连线 ${lineIndex} 中`);

    } catch (error) {
      console.error('处理连线重叠时出错:', error);
    }
  }

  // 计算点在线段上的投影
  const getProjectionOnSegment = (px, py, x1, y1, x2, y2) => {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
      // 线段退化为点
      return { x: x1, y: y1 };
    }

    let param = dot / lenSq;

    // 限制投影点在线段范围内
    param = Math.max(0, Math.min(1, param));

    return {
      x: x1 + param * C,
      y: y1 + param * D
    };
  }

  return {
    startDrag,
    handleMouseMove,
    stopDrag,
  }
}
