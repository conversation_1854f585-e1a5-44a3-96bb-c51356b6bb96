/* 电流表组件样式 - 现代扁平化设计 */
.ammeter-component {
  width: 200px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  border-left: 4px solid #007bff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: all 0.3s ease;
}

.ammeter-component:hover {
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.25);
  transform: translateY(-2px);
}

/* 头部样式 */
.ammeter-header {
  background: linear-gradient(to right, #e6f2ff, #f8faff);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6f2ff;
}

.ammeter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #0056b3;
}

.ammeter-icon {
  color: #007bff;
  font-size: 16px;
}

.ammeter-drag-handle {
  color: #99c2ff;
  cursor: move;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.ammeter-drag-handle:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

/* 主体内容样式 */
.ammeter-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ammeter-display {
  width: 100%;
  background-color: #f8faff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
  text-align: center;
  border: 1px solid #e6f2ff;
}

.ammeter-value {
  font-size: 28px;
  font-weight: 700;
  color: #007bff;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.ammeter-unit {
  color: #5c7cfa;
  font-size: 14px;
  margin-top: 5px;
}

.ammeter-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6c757d;
  margin-top: 10px;
}

.ammeter-range {
  background-color: rgba(0, 123, 255, 0.08);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.ammeter-label {
  font-style: italic;
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

.ammeter-value.updating {
  animation: pulse 1.5s infinite;
} 