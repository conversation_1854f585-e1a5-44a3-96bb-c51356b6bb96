<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电路仪表拖拽预览</title>
  <link rel="stylesheet" href="ammeter.css">
  <link rel="stylesheet" href="voltmeter.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="meters-drag.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
      margin: 0;
      padding: 20px;
      overflow: hidden;
    }
    
    .circuit-container {
      position: relative;
      width: 100%;
      height: calc(100vh - 40px);
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .instructions {
      text-align: center;
      padding: 10px;
      margin-bottom: 10px;
      background-color: #e8f5e9;
      border-radius: 4px;
      font-size: 14px;
    }
    
    /* 拖拽样式 */
    .draggable {
      transition: box-shadow 0.3s, opacity 0.3s;
    }
    
    .dragging {
      opacity: 0.9;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
    }
  </style>
</head>
<body>
  <div class="instructions">
    <p>原始UI设计的电流表和电压表，保持原有外观，增加全屏自由拖拽功能</p>
  </div>
  
  <div class="circuit-container">
    <!-- 电流表 -->
    <div class="ammeter-component" id="ammeter-1" style="position: absolute; left: 50px; top: 50px;">
      <div class="ammeter-header" id="ammeter-header-1">
        <div class="ammeter-title">
          <span class="material-icons ammeter-icon">electric_bolt</span>
          <span>电流表</span>
          <small style="margin-left: 5px; font-size: 11px; color: #6c757d;">A1</small>
        </div>
        <span class="material-icons ammeter-drag-handle">drag_indicator</span>
      </div>
      
      <div class="ammeter-content">
        <div class="ammeter-display">
          <div class="ammeter-value" id="ammeter-value-1">0.09</div>
          <div class="ammeter-unit">安培 (A)</div>
        </div>
        
        <div class="ammeter-info">
          <div class="ammeter-label">电流测量</div>
          <div class="ammeter-range">量程: 0-10A</div>
        </div>
      </div>
    </div>
    
    <!-- 电压表 -->
    <div class="voltmeter-component" id="voltmeter-1" style="position: absolute; right: 50px; top: 50px;">
      <div class="voltmeter-header" id="voltmeter-header-1">
        <div class="voltmeter-title">
          <span class="material-icons voltmeter-icon">bolt</span>
          <span>电压表</span>
          <small style="margin-left: 5px; font-size: 11px; color: #6c757d;">V1</small>
        </div>
        <span class="material-icons voltmeter-drag-handle">drag_indicator</span>
      </div>
      
      <div class="voltmeter-content">
        <div class="voltmeter-display">
          <div class="voltmeter-value" id="voltmeter-value-1">10.13</div>
          <div class="voltmeter-unit">伏特 (V)</div>
        </div>
        
        <div class="voltmeter-info">
          <div class="voltmeter-label">电压测量</div>
          <div class="voltmeter-range">量程: 0-30V</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 页面加载完成后初始化拖拽功能
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化拖拽功能
      initAllMetersDrag();
      
      // 随机更新数值演示
      setInterval(function() {
        // 更新电流表数值
        const ammeterValue = (Math.random() * 10).toFixed(2);
        const ammeterElement = document.getElementById('ammeter-value-1');
        ammeterElement.classList.add('updating');
        setTimeout(() => {
          ammeterElement.textContent = ammeterValue;
          ammeterElement.classList.remove('updating');
        }, 300);
        
        // 更新电压表数值
        const voltmeterValue = (Math.random() * 30).toFixed(2);
        const voltmeterElement = document.getElementById('voltmeter-value-1');
        voltmeterElement.classList.add('updating');
        setTimeout(() => {
          voltmeterElement.textContent = voltmeterValue;
          voltmeterElement.classList.remove('updating');
        }, 300);
      }, 3000);
    });
  </script>
</body>
</html> 