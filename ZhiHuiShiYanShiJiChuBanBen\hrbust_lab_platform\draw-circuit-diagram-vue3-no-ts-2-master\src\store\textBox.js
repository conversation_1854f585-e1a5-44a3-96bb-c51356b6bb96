import { defineStore, storeToRefs } from 'pinia'
import { nanoid } from 'nanoid'
import { ref, watchEffect } from 'vue'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { useSelectedComponentStore } from '@/store/selectedComponent'

export const useTextBoxStore = defineStore('textBox', () => {
  // store: 组件
  const componentsInfoStore = useComponentsInfoStore()
  const { components } = storeToRefs(componentsInfoStore)

  // store: 选中组件
  const selectedComponentStore = useSelectedComponentStore()
  const { selectedComponent } = storeToRefs(selectedComponentStore)

  // 管理所有文本框
  const textBoxs = ref([])
  // 管理被选中的文本框，初始值为 null
  const selectedTextBox = ref(null)

  const updateTextBoxs = (newTextBoxs) => {
    textBoxs.value.length = 0
    newTextBoxs.forEach((textBox) => textBoxs.value.push(textBox))
  }

  // 设置选中文本框
  const setSelectedTextBox = (paramTextBox) => {
    console.log('@@@ 选中文本框', paramTextBox)
    selectedTextBox.value = paramTextBox
  }

  // 清除选中文本框
  const clearSelectedTextBox = () => {
    console.log('@@@ 清除选中文本框', selectedTextBox.value)
    selectedTextBox.value = null
  }

  // 上文本框默认 X、Y 偏移量
  const TOP_DEFAULT_OFFSET_X = ref(15)
  const TOP_DEFAULT_OFFSET_Y = ref(-30)
  // 下文本框默认 X、Y 偏移量
  const BOTTOM_DEFAULT_OFFSET_X = ref(0)
  const BOTTOM_DEFAULT_OFFSET_Y = ref(40)
  // 自定义类型的文本框默认 X、Y 偏移量
  const CUSTOM_DEFAULT_OFFSET_X = ref(60)
  const CUSTOM_DEFAULT_OFFSET_Y = ref(20)

  // 更改某文本框的偏移量
  const changeTextBoxOffset = (textBoxId, offsetX, offsetY) => {
    if (textBoxId) {
      const textBox = textBoxs.value.find((item) => item.id === textBoxId)
      textBox.offsetX = offsetX
      textBox.offsetY = offsetY
    }
  }
  // 添加文本框时进行条件判断
  const addTextBox = (textBox) => {
    textBoxs.value.push(textBox)
  }

  // 移除文本框
  const removeTextBox = (textBox) => {
    const index = textBoxs.value.indexOf(textBox)
    if (index !== -1) {
      textBoxs.value.splice(index, 1)
      console.log('@@@ 移除文本框', textBox)
      // 如果移除的是选中的文本框，则清除选中状态
      if (selectedTextBox.value === textBox) {
        clearTextBox()
      }
    }
  }

  // 生成一个新的文本框对象
  const createTextBox = (content, type, offsetX, offsetY) => {
    // 规范化 TextBox 数据结构
    const newTextBox = {
      id: `${Date.now()}_${nanoid()}`, // 使用时间戳作为唯一标识
      type,
      componentId: null,
      content,
      offsetX,
      offsetY,
      relativeOffsetX: 0,
      relativeOffsetY: 0,
    }

    return newTextBox
  }

  // 新生成一个【文本框】来和【某个组件】绑定起来
  const generateTextBoxeToComponent = (componentId, type, content, offsetX, offsetY) => {
    const newTextBox = createTextBox(content, type, offsetX, offsetY)
    // 将文本框与组件关联
    newTextBox.componentId = componentId
    // 计算相对偏移量
    updateTextBoxRelativeOffset(componentId, newTextBox)
    // 添加到所有文本框
    addTextBox(newTextBox)

    return newTextBox
  }

  // 计算文本框与组件的相对偏移量
  const updateTextBoxRelativeOffset = (componentId, textBox) => {
    if (componentId) {
      // get 指定组件的 x,y 坐标
      const component = components.value.find((component) => component.componentId === componentId)
      textBox.relativeOffsetX = textBox.offsetX - component.x
      textBox.relativeOffsetY = textBox.offsetY - component.y
    }
  }

  // 更新指定component对应的所有文本框的位置
  const updateTextBoxesPositionByComponent = (componentId) => {
    // get 指定组件的 x,y 坐标
    const { x, y } = components.value.find((component) => component.componentId === componentId)
    // get 对应的所有文本框
    const textBoxesToUpdate = textBoxs.value.filter((textBox) => textBox.componentId === componentId)
    // for 更新文本框位置
    textBoxesToUpdate.forEach((textBox) => {
      // console.log('@@@ ', '更新文本框位置', textBox)
      // updateTextBoxRelativeOffset(componentId, textBox)
      // console.log('@@@ ', '更新后的文本框位置', textBox)
      changeTextBoxOffset(textBox.id, x + textBox.relativeOffsetX, y + textBox.relativeOffsetY)
    })
  }

  // 更新 【文本框】的 content
  const updateTextBoxContent = () => {
    const selectedComponentId = selectedComponent.value?.componentId
    console.log('@@@ ', '被选中组件', selectedComponent.value)
    if (selectedComponentId) {
      const selectedComponentTextBoxs = textBoxs.value.filter((textBox) => textBox.componentId === selectedComponentId)
      selectedComponentTextBoxs.forEach((textBox) => {
        // 更新文本框的内容
        if (textBox.type === 'top') {
          textBox.content = `${selectedComponent.value.identifier}`
        }
        if (textBox.type === 'bottom') {
          textBox.content = `${selectedComponent.value.value} ${selectedComponent.value.unit}`
        }
      })
    }
  }

  // 清空文本框
  const clearTextBoxs = () => {
    textBoxs.value.length = 0
  }

  return {
    textBoxs,
    selectedTextBox,
    TOP_DEFAULT_OFFSET_X,
    TOP_DEFAULT_OFFSET_Y,
    BOTTOM_DEFAULT_OFFSET_X,
    BOTTOM_DEFAULT_OFFSET_Y,
    CUSTOM_DEFAULT_OFFSET_X,
    CUSTOM_DEFAULT_OFFSET_Y,
    setSelectedTextBox,
    clearSelectedTextBox,
    changeTextBoxOffset,
    addTextBox,
    removeTextBox,
    createTextBox,
    generateTextBoxeToComponent,
    updateTextBoxRelativeOffset,
    updateTextBoxesPositionByComponent,
    updateTextBoxContent,
    updateTextBoxs,
    clearTextBoxs,
  }
})
