/**
 * 电路仪表拖拽功能
 * 支持全屏范围拖拽，边缘防护，动画流畅度优化
 * 保持原有UI设计风格
 */

/**
 * 使仪表组件可自由拖拽
 * @param {string} elementId - 要拖拽的元素ID
 * @param {string} handleId - 拖拽把手元素ID，通常是标题栏
 */
function makeDraggable(elementId, handleId) {
  // 获取DOM元素
  const element = document.getElementById(elementId);
  const handle = document.getElementById(handleId) || element;
  
  if (!element || !handle) {
    console.error(`找不到拖拽元素或把手: ${elementId}, ${handleId}`);
    return;
  }
  
  // 拖拽状态变量
  let isDragging = false;
  let startX, startY;
  let initialLeft, initialTop;
  let rafId = null;
  
  // 记录工作区尺寸用于边界检测
  const workspace = document.querySelector('.circuit-container') || document.body;
  let workspaceRect = workspace.getBoundingClientRect();
  
  // 窗口大小改变时更新工作区尺寸
  window.addEventListener('resize', () => {
    workspaceRect = workspace.getBoundingClientRect();
  });
  
  // 初始化元素样式
  if (!element.style.position || element.style.position === 'static') {
    element.style.position = 'absolute';
  }
  
  // 如果元素没有初始位置，设置默认位置
  if (!element.style.left && !element.style.top) {
    element.style.left = '20px';
    element.style.top = '20px';
  }
  
  /**
   * 开始拖动处理
   */
  function startDrag(e) {
    e.preventDefault();
    
    // 获取起始位置（鼠标/触摸点）
    startX = e.clientX || (e.touches && e.touches[0].clientX);
    startY = e.clientY || (e.touches && e.touches[0].clientY);
    
    // 获取元素当前位置
    const rect = element.getBoundingClientRect();
    initialLeft = rect.left;
    initialTop = rect.top;
    
    // 标记正在拖拽
    isDragging = true;
    element.classList.add('dragging');
    
    // 提高拖拽中元素的z-index
    element.style.zIndex = '1000';
    
    // 绑定移动和结束事件
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
    document.addEventListener('touchmove', drag, { passive: false });
    document.addEventListener('touchend', stopDrag);
  }
  
  /**
   * 拖动过程中处理
   */
  function drag(e) {
    if (!isDragging) return;
    e.preventDefault();
    
    // 获取当前位置
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);
    
    // 计算位移
    const deltaX = clientX - startX;
    const deltaY = clientY - startY;
    
    // 使用requestAnimationFrame优化动画
    if (rafId) {
      cancelAnimationFrame(rafId);
    }
    
    rafId = requestAnimationFrame(() => {
      // 计算新位置
      let newLeft = initialLeft + deltaX;
      let newTop = initialTop + deltaY;
      
      // 边界检测 - 防止元素完全移出视口
      const rect = element.getBoundingClientRect();
      const padding = 30; // 留出30px的边缘空间
      
      // 检测左右边界
      if (newLeft < padding) {
        newLeft = padding;
      } else if (newLeft + rect.width - padding > workspaceRect.right) {
        newLeft = workspaceRect.right - rect.width + padding;
      }
      
      // 检测上下边界
      if (newTop < padding) {
        newTop = padding;
      } else if (newTop + rect.height - padding > workspaceRect.bottom) {
        newTop = workspaceRect.bottom - rect.height + padding;
      }
      
      // 更新元素位置
      element.style.left = `${newLeft}px`;
      element.style.top = `${newTop}px`;
    });
  }
  
  /**
   * 结束拖动处理
   */
  function stopDrag() {
    if (!isDragging) return;
    
    // 清除状态
    isDragging = false;
    element.classList.remove('dragging');
    
    // 恢复正常z-index
    setTimeout(() => {
      element.style.zIndex = '900';
    }, 200);
    
    // 解绑事件监听
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
    document.removeEventListener('touchmove', drag);
    document.removeEventListener('touchend', stopDrag);
    
    // 取消任何待处理的动画帧
    if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  }
  
  // 绑定事件监听器
  handle.addEventListener('mousedown', startDrag);
  handle.addEventListener('touchstart', startDrag, { passive: false });
  
  // 返回清理函数
  return function cleanup() {
    handle.removeEventListener('mousedown', startDrag);
    handle.removeEventListener('touchstart', startDrag);
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
    document.removeEventListener('touchmove', drag);
    document.removeEventListener('touchend', stopDrag);
  };
}

/**
 * 添加电流表拖拽功能
 */
function initializeAmmeterDrag() {
  // 初始化已存在的电流表拖拽
  const ammeters = document.querySelectorAll('.ammeter-component');
  
  ammeters.forEach((ammeter, index) => {
    const ammeterId = ammeter.id || `ammeter-${index + 1}`;
    const headerSelector = `#${ammeterId} .ammeter-header, #${ammeterId}-header, #ammeter-header-${index + 1}`;
    const headerElement = document.querySelector(headerSelector);
    
    // 设置ID如果没有
    if (!ammeter.id) {
      ammeter.id = ammeterId;
    }
    
    // 添加拖拽样式
    if (!ammeter.classList.contains('draggable')) {
      ammeter.classList.add('draggable');
      
      // 添加拖拽时的样式
      const style = document.createElement('style');
      style.textContent = `
        .draggable {
          transition: box-shadow 0.3s, opacity 0.3s;
        }
        .dragging {
          opacity: 0.9;
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }
      `;
      document.head.appendChild(style);
    }
    
    // 初始化拖拽
    makeDraggable(ammeterId, headerElement ? headerElement.id : null);
  });
}

/**
 * 添加电压表拖拽功能
 */
function initializeVoltmeterDrag() {
  // 初始化已存在的电压表拖拽
  const voltmeters = document.querySelectorAll('.voltmeter-component');
  
  voltmeters.forEach((voltmeter, index) => {
    const voltmeterId = voltmeter.id || `voltmeter-${index + 1}`;
    const headerSelector = `#${voltmeterId} .voltmeter-header, #${voltmeterId}-header, #voltmeter-header-${index + 1}`;
    const headerElement = document.querySelector(headerSelector);
    
    // 设置ID如果没有
    if (!voltmeter.id) {
      voltmeter.id = voltmeterId;
    }
    
    // 添加拖拽样式
    if (!voltmeter.classList.contains('draggable')) {
      voltmeter.classList.add('draggable');
    }
    
    // 初始化拖拽
    makeDraggable(voltmeterId, headerElement ? headerElement.id : null);
  });
}

// 初始化所有仪表拖拽功能
function initAllMetersDrag() {
  initializeAmmeterDrag();
  initializeVoltmeterDrag();
  
  // 监听DOM变化，为新添加的元素启用拖拽
  const observer = new MutationObserver((mutations) => {
    let needsUpdate = false;
    
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length) {
        mutation.addedNodes.forEach(node => {
          if (node.classList && 
             (node.classList.contains('ammeter-component') || 
              node.classList.contains('voltmeter-component'))) {
            needsUpdate = true;
          }
        });
      }
    });
    
    if (needsUpdate) {
      initializeAmmeterDrag();
      initializeVoltmeterDrag();
    }
  });
  
  // 观察body及其子元素变化
  observer.observe(document.body, { 
    childList: true, 
    subtree: true 
  });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  // 延迟初始化，确保其他组件已加载
  setTimeout(initAllMetersDrag, 500);
});

// 防止在iframe中重复初始化
if (window.self === window.top) {
  // 确保在多个时机调用初始化函数
  window.addEventListener('load', initAllMetersDrag);
  if (document.readyState === 'complete') {
    initAllMetersDrag();
  }
}

/**
 * 更新仪表显示值
 * @param {string} meterType - 'ammeter' 或 'voltmeter'
 * @param {number} value - 要显示的值
 * @param {number} maxValue - 量程最大值，用于进度条
 */
function updateMeterValue(meterType, value, maxValue) {
  // 获取元素
  const container = document.querySelector(`.${meterType}-container`);
  if (!container) return;
  
  const valueDisplay = container.querySelector('.meter-value');
  const progressBar = container.querySelector('.meter-progress-bar');
  
  if (!valueDisplay) return;
  
  // 格式化数值，保留两位小数
  const formattedValue = Number(value).toFixed(2);
  
  // 添加更新动画
  valueDisplay.classList.add('updating');
  
  // 更新显示值
  valueDisplay.textContent = formattedValue;
  
  // 如果有进度条，更新进度
  if (progressBar && maxValue) {
    const percentage = Math.min(Math.max((value / maxValue) * 100, 0), 100);
    progressBar.style.width = `${percentage}%`;
  }
  
  // 移除动画类
  setTimeout(() => {
    valueDisplay.classList.remove('updating');
  }, 300);
}

/**
 * 初始化电流表和电压表
 */
function initializeMeters() {
  // 从localStorage获取仪表数据
  const ammetersData = JSON.parse(localStorage.getItem('ammeters') || '[]');
  const voltmetersData = JSON.parse(localStorage.getItem('voltmeters') || '[]');
  
  const circuitContainer = document.querySelector('.circuit-container') || document.body;
  
  // 创建电流表
  ammetersData.forEach((ammeter, index) => {
    createMeter('ammeter', ammeter, index, circuitContainer);
  });
  
  // 创建电压表
  voltmetersData.forEach((voltmeter, index) => {
    createMeter('voltmeter', voltmeter, index, circuitContainer);
  });
}

/**
 * 创建仪表元素
 * @param {string} type - 'ammeter' 或 'voltmeter'
 * @param {object} data - 仪表数据
 * @param {number} index - 索引
 * @param {HTMLElement} container - 容器元素
 */
function createMeter(type, data, index, container) {
  const id = `${type}-${index + 1}`;
  const headerId = `${type}-header-${index + 1}`;
  
  // 创建仪表元素
  const meterElement = document.createElement('div');
  meterElement.id = id;
  meterElement.className = `meter-container ${type}-container`;
  
  // 设置初始位置 (如果有百分比位置，转换为像素)
  if (typeof data.x === 'number' && typeof data.y === 'number') {
    const containerRect = container.getBoundingClientRect();
    const xPos = (data.x / 100) * containerRect.width;
    const yPos = (data.y / 100) * containerRect.height;
    
    meterElement.style.left = `${xPos}px`;
    meterElement.style.top = `${yPos}px`;
  } else {
    // 默认位置
    const offset = index * 30;
    meterElement.style.right = `${40 + offset}px`;
    meterElement.style.top = `${50 + offset}px`;
  }
  
  // 获取仪表数据
  const label = data.label || (type === 'ammeter' ? '电流表' : '电压表');
  const identifier = data.identifier || (type === 'ammeter' ? `A${index + 1}` : `V${index + 1}`);
  const unit = data.unit || (type === 'ammeter' ? 'A' : 'V');
  const range = data.range || (type === 'ammeter' ? '0-10A' : '0-30V');
  const maxValue = parseFloat(range.split('-')[1]) || (type === 'ammeter' ? 10 : 30);
  
  // 电流表UI
  if (type === 'ammeter') {
    meterElement.innerHTML = `
      <div class="meter-header" id="${headerId}">
        <span>${identifier}</span>
        <span class="material-icons" style="font-size:14px">drag_indicator</span>
      </div>
      <div class="meter-body">
        <div class="connection-points">
          <div class="connection-point" data-point="negative"></div>
          <div class="connection-point" data-point="positive"></div>
        </div>
        
        <div class="scale-markers">
          <span>0</span>
          <span>${maxValue}</span>
        </div>
        
        <div class="meter-scale">
          <div class="meter-progress-bar" style="width: 0%"></div>
        </div>
        
        <div class="meter-display">
          <div class="meter-reading">
            <div class="meter-value">0.00</div>
            <span class="meter-unit">${unit}</span>
          </div>
        </div>
        
        <div class="meter-footer">
          <span>${identifier}</span>
          <span class="meter-range">${range}</span>
        </div>
      </div>
    `;
  } 
  // 电压表UI
  else {
    meterElement.innerHTML = `
      <div class="meter-header" id="${headerId}">
        <span>${identifier}</span>
        <span class="material-icons" style="font-size:14px">drag_indicator</span>
      </div>
      <div class="meter-body">
        <div class="connection-points">
          <div class="connection-point" data-point="negative"></div>
          <div class="connection-point" data-point="positive"></div>
        </div>
        
        <div class="scale-markers">
          <span>0</span>
          <span>${maxValue}</span>
        </div>
        
        <div class="meter-scale">
          <div class="meter-progress-bar" style="width: 0%"></div>
        </div>
        
        <div class="meter-display">
          <div class="meter-reading">
            <div class="meter-value">0.00</div>
            <span class="meter-unit">${unit}</span>
          </div>
        </div>
        
        <div class="meter-footer">
          <span>${identifier}</span>
          <span class="meter-range">${range}</span>
        </div>
      </div>
    `;
  }
  
  // 添加到容器
  container.appendChild(meterElement);
  
  // 启用拖拽
  makeDraggable(id, headerId);
  
  // 更新初始值
  updateMeterValue(type, data.defaultValue || 0, maxValue);
  
  return meterElement;
} 