<template>
  <el-dropdown @command="handleExport" placement="bottom-end">
    <el-button class="export-btn action-btn" size="small">
      <el-icon size="14px"><Download /></el-icon>
      <span>导出</span>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="png">导出 PNG</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup name="ExportPngSvg">
  import domtoimage from 'dom-to-image'
  import { Download } from '@element-plus/icons-vue'

  /**
   * Props
   * targetId: 需要被导出的目标元素的 ID，例如你的设计区域的 div，这样这个组件可以适用于不同的元素。
   * svgSelector: 选择 SVG 元素的选择器，例如你画布中包含连线的 .canvas-svg。
   */
  const props = defineProps({
    targetId: {
      type: String,
      required: true,
    },
    svgSelector: {
      type: String,
      required: true,
    },
  })

  // 导出函数
  const handleExport = (command) => {
    console.log('🎯 导出命令:', command);

    // 先检查画布内容
    const canvasElement = document.getElementById(props.targetId);
    if (canvasElement) {
      console.log('📊 画布信息:');
      console.log('  - 元素存在:', !!canvasElement);
      console.log('  - 子元素数量:', canvasElement.children.length);
      console.log('  - 画布尺寸:', canvasElement.offsetWidth, 'x', canvasElement.offsetHeight);
      console.log('  - 内容预览:', canvasElement.innerHTML.substring(0, 200) + '...');
    }

    if (command === 'png') {
      exportAsPNG()
    } else if (command === 'svg') {
      exportAsSVG()
    }
  }

  // 简化的图像处理函数
  const processCanvasToImage = (canvasElement, callback) => {
    if (!canvasElement) return null;

    console.log('🔄 处理画布图像...');

    // 直接执行回调，不做复杂的样式处理
    return callback(canvasElement);
  };

  // 获取文件名的方法 - 使用与注释代码相同的命名逻辑
  const getExportFileName = () => {
    try {
      // 使用与注释代码中相同的命名逻辑：${courseName}_${experimentName}.png
      const courseName = localStorage.getItem('courseName') || '';
      const experimentName = localStorage.getItem('experimentName') || '';

      if (courseName && experimentName) {
        const fileName = `${courseName}_${experimentName}.png`;
        console.log('📝 使用课程实验命名:', fileName);
        return fileName;
      }
    } catch (error) {
      console.warn('获取课程实验名称失败:', error);
    }

    // 如果没有找到课程和实验名称，使用默认命名
    const defaultName = `电路图_${new Date().toLocaleDateString().replace(/\//g, '-')}`;
    console.log('📝 使用默认名称:', defaultName);
    return `${defaultName}.png`;
  };

  // 导出为 PNG 的方法
  const exportAsPNG = () => {
    const canvasElement = document.getElementById(props.targetId);
    if (!canvasElement) {
      console.error('找不到画布元素:', props.targetId);
      return;
    }

    console.log('🖼️ 开始导出PNG...');
    console.log('📋 画布元素:', canvasElement);
    console.log('📏 画布尺寸:', canvasElement.offsetWidth, 'x', canvasElement.offsetHeight);

    // 获取文件名
    const fileName = getExportFileName();
    console.log('📁 导出文件名:', fileName);

    // 简化的导出方法，不隐藏网格，直接导出
    domtoimage
      .toPng(canvasElement, {
        bgcolor: '#ffffff', // 白色背景
        width: canvasElement.offsetWidth,
        height: canvasElement.offsetHeight,
        style: {
          transform: 'scale(1)',
          transformOrigin: 'top left'
        }
      })
      .then((dataUrl) => {
        console.log('✅ PNG导出成功');

        // 保存到会话存储
        sessionStorage.setItem('temp_circuit_png', dataUrl);

        // 下载文件
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName; // 使用与模板相同的命名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('📥 文件下载完成:', fileName);
      })
      .catch((error) => {
        console.error('❌ 导出 PNG 失败:', error);
        // 尝试备用方法
        tryAlternativeExport(canvasElement);
      });
  };

  // 备用导出方法
  const tryAlternativeExport = (canvasElement) => {
    console.log('🔄 尝试备用导出方法...');

    const fileName = getExportFileName();

    domtoimage
      .toBlob(canvasElement, {
        bgcolor: '#ffffff'
      })
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName; // 使用与模板相同的命名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log('✅ 备用方法导出成功:', fileName);
      })
      .catch((error) => {
        console.error('❌ 备用方法也失败了:', error);
      });
  };
</script>

<style scoped>
  .export-btn {
    background: #f59e0b !important;
    color: white !important;
    border-color: #d97706 !important;
    border-radius: 8px !important; /* 添加圆角，与其他按钮保持一致 */
    padding: 6px 10px !important; /* 与其他按钮保持一致的内边距 */
    font-size: 13px !important; /* 与其他按钮保持一致的字体大小 */
    transition: all 0.15s ease !important; /* 与其他按钮保持一致的过渡效果 */
    box-shadow: none !important; /* 去掉阴影，保持扁平化 */

    &:hover {
      background: #d97706 !important;
      border-color: #b45309 !important;
      transform: none !important; /* 去掉位移动画 */
      box-shadow: none !important; /* 悬停时也不要阴影 */
    }

    &:active {
      background: #b45309 !important;
      transform: none !important;
    }

    span {
      margin-left: 3px; /* 与其他按钮保持一致的图标文字间距 */
      font-size: 13px; /* 与其他按钮保持一致的字体大小 */
    }
  }
</style>
