/**
 * 专用于使用前，检查是否是系统用户
 */

import { checkUserExists } from '@/api/sys-user'
import { ElMessageBox } from 'element-plus'

export function checkUserExist(userId) {
  checkUserExists(userId).then((res) => {
    console.log('@@@ res', res)
    const result = res.data
    if (result == true) {
      // 是系统用户
      return
    } else {
      // 不是系统用户
      ElMessageBox.alert(
        '不是当前系统用户！' + ' <br> 点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！',
        '警告',
        {
          confirmButtonText: '收到',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          callback: () => {
            window.close()
          },
        },
      )
      return
    }
  })
}
