import { defineStore, storeToRefs } from 'pinia'
import { computed, reactive } from 'vue'
import { useLineStateStore } from '@/store/lineState'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { useCanvasInfoStore } from '@/store/canvasInfo'
import { useComponentInfoStore } from '@/store/componentInfo'
import { isUsingV1_1, printCurrentConfig } from '@/config/validationConfig'
import { componentsData } from '@/layout/ComponentsPanel/data/componentsData.js'
import { exportTestPointRelations } from '@/utils/canvasDataManager'

export const useVerifyStore = defineStore('verify', () => {
  /**
   * 外部 store
   */
  // canvasInfo
  const canvasInfoStore = useCanvasInfoStore()
  const { gridSpacing } = storeToRefs(canvasInfoStore)
  // lines、intersections
  const lineStateStore = useLineStateStore()
  const { fixedLines, intersections } = storeToRefs(lineStateStore)
  // components
  const componentsInfoStore = useComponentsInfoStore()
  const { components } = storeToRefs(componentsInfoStore)
  // componentInfo
  const componentInfoStore = useComponentInfoStore()
  const { getComponentWH } = componentInfoStore

  /**
   * getters
   */
  const lines = computed(() => {
    // 🔧 修复：安全访问 fixedLines.value
    const linesArray = fixedLines?.value || []
    return linesArray.map((line) => {
      return generateGridPoints(line)
    })
  })

  /**
   * actions
   */

  /**
   * 【用于格式化计算 component 的连接点的坐标】
   */
  const generateComponentPoints = () => {
    // 🔧 修复：安全访问 components.value
    const componentsArray = components?.value || []
    componentsArray.map((component) => {
      // 计算组件中心点
      const componentWidth = getComponentWH(component).componentWidth
      const componentHeight = getComponentWH(component).componentHeight
      const centerX = component.x + componentWidth / 2
      const centerY = component.y + componentHeight / 2

      // 将旋转角度转换为弧度
      const radian = (component.rotation * Math.PI) / 180

      // 遍历组件的连接点
      component.connectionPoints.map((connectionPoint) => {
        // 原始连接点相对组件的中心点的偏移
        const relativeX = connectionPoint.x * component.scale - componentWidth / 2
        const relativeY = connectionPoint.y * component.scale - componentHeight / 2

        // 旋转后的连接点坐标
        const rotatedX = Math.cos(radian) * relativeX - Math.sin(radian) * relativeY + centerX
        const rotatedY = Math.sin(radian) * relativeX + Math.cos(radian) * relativeY + centerY

        // 更新连接点的坐标
        connectionPoint.point = getClosestPoint(rotatedX, rotatedY)

        return connectionPoint
      })

      return component
    })
  }

  /**
   * 获取最近的网格点
   */
  const getClosestPoint = (x, y) => {
    const px = Math.round(x / gridSpacing.value) * gridSpacing.value
    const py = Math.round(y / gridSpacing.value) * gridSpacing.value
    return { x: px, y: py }
  }

  /**
   * 【用于生成 line 对应的所经过的所有 网格点坐标】
   * @returns [{}]
   */
  const generateGridPoints = (line) => {
    const points = []
    const n = line.length

    for (let i = 0; i < n - 1; i++) {
      const start = line[i]
      const end = line[i + 1]

      // 起点为关键点
      if (points.length === 0 || points[points.length - 1].x !== start.x || points[points.length - 1].y !== start.y) {
        points.push({ x: start.x, y: start.y })
      }

      // 检测方向
      const dx = end.x - start.x
      const dy = end.y - start.y

      if (dx !== 0 && dy === 0) {
        // 水平线
        const step = dx > 0 ? gridSpacing.value : -gridSpacing.value
        for (let x = start.x + step; Math.abs(x - end.x) >= Math.abs(step); x += step) {
          points.push({ x, y: start.y })
        }
      } else if (dy !== 0 && dx === 0) {
        // 垂直线
        const step = dy > 0 ? gridSpacing.value : -gridSpacing.value
        for (let y = start.y + step; Math.abs(y - end.y) >= Math.abs(step); y += step) {
          points.push({ x: start.x, y })
        }
      }
    }

    // 添加最终的终点，避免重复
    const finalPoint = { x: line[n - 1].x, y: line[n - 1].y }
    if (points.length === 0 || points[points.length - 1].x !== finalPoint.x || points[points.length - 1].y !== finalPoint.y) {
      points.push(finalPoint)
    }

    return points
  }

  /**
   * 生成【连接关系】邻接表
   */
  // ==============================================================================================
  const generateConnectionRelationships = () => {
    // 格式化计算 component 的连接点的坐标
    generateComponentPoints()

    // 构建连通性索引表
    const connectivityMap = new Map()

    // 遍历所有线段，添加点到连通性索引
    // 🔧 修复：安全访问 fixedLines.value
    const linesArray = fixedLines?.value || []
    linesArray.forEach((line) => {
      const points = generateGridPoints(line) // 获取 line 的网格点
      // 遍历所有点，进行连接时的复杂逻辑处理
      for (let i = 0; i < points.length - 1; i++) {
        let start = points[i]
        let end = points[i + 1]

        // 判断 start 点是否是重合点，如果是则跳过本次循环
        if (checkIfPointIsOverlapping(start)) {
          continue
        }

        // 检测 end 是否是重合点
        while (checkIfPointIsOverlapping(end)) {
          if (i + 2 < points.length) {
            // 还有下一个点，继续检测
            end = points[++i + 1]
          } else {
            // 到达最后一个点，终止循环
            addConnectivity(connectivityMap, start, end)
            addConnectivity(connectivityMap, end, start)
            break
          }
        }

        // 如果 end 已经不再是重合点或到了终点，绑定 start 和 end
        if (!checkIfPointIsOverlapping(end)) {
          addConnectivity(connectivityMap, start, end)
          addConnectivity(connectivityMap, end, start)
        }
      }
    })

    // 遍历所有交点，添加交点到连通性索引
    // 🔧 修复：安全访问 intersections.value
    const intersectionsArray = intersections?.value || []
    intersectionsArray.forEach((intersection) => {
      const intersectionKey = `${intersection.x},${intersection.y}`
      const connectedLines = []

      // 收集所有通过此交叉点的线段
      linesArray.forEach((line) => {
        const points = generateGridPoints(line)
        if (points.some((point) => isPointEqual(point, intersection))) {
          connectedLines.push(points)
        }
      })

      // 为交叉点建立与所有相关线段端点的连接
      connectedLines.forEach((linePoints) => {
        // 交叉点与线段起点和终点的双向连接
        addConnectivity(connectivityMap, intersection, linePoints[0])
        addConnectivity(connectivityMap, linePoints[0], intersection)
        addConnectivity(connectivityMap, intersection, linePoints[linePoints.length - 1])
        addConnectivity(connectivityMap, linePoints[linePoints.length - 1], intersection)
      })

      // 🔧 关键修复：交叉点处的所有线段端点之间也要建立连接
      for (let i = 0; i < connectedLines.length; i++) {
        for (let j = i + 1; j < connectedLines.length; j++) {
          const line1Points = connectedLines[i]
          const line2Points = connectedLines[j]

          // 线段1的两端与线段2的两端建立连接
          addConnectivity(connectivityMap, line1Points[0], line2Points[0])
          addConnectivity(connectivityMap, line2Points[0], line1Points[0])
          addConnectivity(connectivityMap, line1Points[0], line2Points[line2Points.length - 1])
          addConnectivity(connectivityMap, line2Points[line2Points.length - 1], line1Points[0])
          addConnectivity(connectivityMap, line1Points[line1Points.length - 1], line2Points[0])
          addConnectivity(connectivityMap, line2Points[0], line1Points[line1Points.length - 1])
          addConnectivity(connectivityMap, line1Points[line1Points.length - 1], line2Points[line2Points.length - 1])
          addConnectivity(connectivityMap, line2Points[line2Points.length - 1], line1Points[line1Points.length - 1])
        }
      }
    })

    // 遍历组件连接点，查找连通关系
    const connections = []

    // 🔧 修复：安全访问 components.value，防止 undefined 错误
    const componentsArray = components?.value || []
    if (componentsArray.length === 0) {
      console.warn('⚠️ 组件数组为空，无法生成连接关系')
      return connections
    }

    componentsArray.forEach((component1) => {
      if (!component1.connectionPoints) {
        console.warn('⚠️ 组件缺少连接点信息:', component1)
        return
      }

      component1.connectionPoints.forEach((point1) => {
        const pointKey1 = `${point1.point.x},${point1.point.y}`
        componentsArray.forEach((component2) => {
          if (component1 === component2) return

          if (!component2.connectionPoints) {
            console.warn('⚠️ 组件缺少连接点信息:', component2)
            return
          }

          component2.connectionPoints.forEach((point2) => {
            const pointKey2 = `${point2.point.x},${point2.point.y}`
            if (arePointsConnected(connectivityMap, pointKey1, pointKey2)) {
              const connection = {
                from: formatConnectionDescription(component1, point1),
                to: formatConnectionDescription(component2, point2),
              }

              if (!isConnectionDuplicate(connections, connection)) {
                connections.push(connection)
              }
            }
          })
        })
      })
    })

    // console.log('@@@ connectivityMap', connectivityMap)
    // console.log('@@@ connections', connections)

    return connections
  }

  /**
   * 检测指定点是否是重合点
   * @param {Object} targetPoint - 要检测的点，例如 { x: 10, y: 0 }
   * @returns {Boolean} 是否是重合点
   */
  const checkIfPointIsOverlapping = (targetPoint) => {
    // 调用 getOverlappingPoints 获取所有重合点
    const overlappingPoints = getOverlappingPoints()

    // 判断 targetPoint 是否在重合点列表中
    const isOverlapping = overlappingPoints.some((point) => point.x === targetPoint.x && point.y === targetPoint.y)

    // 输出结果便于调试
    // console.log(`检测点 (${targetPoint.x}, ${targetPoint.y}) 是否是重合点: ${isOverlapping}`)

    return isOverlapping
  }

  /**
   * 获取所有 fixedLines 的重合点，过滤掉 intersections
   */
  const getOverlappingPoints = () => {
    // 创建一个 Map 用于记录点出现的次数
    const pointCountMap = new Map()

    // 遍历所有固定线段的网格点
    // 🔧 修复：安全访问 fixedLines.value
    const linesArray = fixedLines?.value || []
    linesArray.forEach((line) => {
      const points = generateGridPoints(line)

      points.forEach((point) => {
        const pointKey = `${point.x},${point.y}`
        if (pointCountMap.has(pointKey)) {
          pointCountMap.set(pointKey, pointCountMap.get(pointKey) + 1)
        } else {
          pointCountMap.set(pointKey, 1)
        }
      })
    })

    // 获取所有出现超过一次的重合点
    const overlappingPoints = Array.from(pointCountMap.entries())
      .filter(([, count]) => count > 1)
      .map(([key]) => {
        const [x, y] = key.split(',').map(Number)
        return { x, y }
      })

    // 过滤掉 intersections 中的点
    // 🔧 修复：安全访问 intersections.value
    const intersectionsArray = intersections?.value || []
    const filteredPoints = overlappingPoints.filter(
      (point) => !intersectionsArray.some((intersection) => intersection.x === point.x && intersection.y === point.y),
    )

    // console.log('@@@ 重合点:', overlappingPoints)
    // console.log('@@@ 过滤后的重合点:', filteredPoints)

    return filteredPoints
  }

  /**
   * 格式化连接描述
   * @param {Object} component 组件
   * @param {Object} connectionPoint 连接点
   * @returns {String} 格式化后的连接描述，包含正负极性（不包含坐标）
   */
  const formatConnectionDescription = (component, connectionPoint) => {
    // 根据极性属性生成描述
    const polarityDescription =
      connectionPoint.polarity === 'positive' ? 'P'
      : connectionPoint.polarity === 'negative' ? 'N'
      : null

    // 特殊处理测点组件 - 确保测点有唯一标识符但不包含坐标
    if (component.type === 'testPoint' || component.isTestPoint) {
      const testPointLabel = component.label || '测点'
      let testPointId = component.identifier || ''

      // 如果测点没有标识符，使用组件ID的后几位作为标识符
      if (!testPointId || testPointId.trim() === '') {
        const componentId = component.id || component.componentId || ''
        testPointId = componentId.slice(-6) || 'TP'
      }

      return `${testPointLabel} - ${testPointId} - - 连接点${connectionPoint.id || ''} - ${polarityDescription}`
    }

    // 普通组件的描述（保持原有逻辑）
    return `${component.label} - ${component.identifier} - ${component.value} ${component.unit} - 连接点${connectionPoint.id} - ${polarityDescription}`
  }

  /**
   * 判断两个点是否相等
   * @param {Object} point1 点1
   * @param {Object} point2 点2
   * @returns {Boolean} 是否相等
   */
  const isPointEqual = (point1, point2) => {
    return point1.x === point2.x && point1.y === point2.y
  }
  /**
   * 单向添加点连接：【把 point2 添加到 point1 的索引】。
   * @param {Map} map - 连通性索引
   * @param {Object} point1 - 起点
   * @param {Object} point2 - 终点
   */
  const addConnectivity = (map, point1, point2) => {
    const key1 = `${point1.x},${point1.y}`
    const key2 = `${point2.x},${point2.y}`

    // 初始化起点的连接列表
    if (!map.has(key1)) {
      map.set(key1, [])
    }

    // 避免重复添加
    if (!map.get(key1).includes(key2)) {
      map.get(key1).push(key2)
    }
  }

  /**
   * 判断两点是否连通
   * @param {Map} map 连通性索引
   * @param {String} pointKey1 点1的键
   * @param {String} pointKey2 点2的键
   * @returns {Boolean} 是否连通
   */
  const arePointsConnected = (map, pointKey1, pointKey2) => {
    const visited = new Set()
    const stack = [pointKey1]

    while (stack.length > 0) {
      const current = stack.pop()
      if (current === pointKey2) return true

      visited.add(current)

      const neighbors = map.get(current) || []
      neighbors.forEach((neighbor) => {
        if (!visited.has(neighbor)) {
          stack.push(neighbor)
        }
      })
    }

    return false
  }

  /**
   * 检查连接是否重复
   * @param {Array} connections 已有连接列表
   * @param {Object} connection 新连接
   * @returns {Boolean} 是否重复
   */
  const isConnectionDuplicate = (connections, connection) => {
    return connections.some(
      (existingConnection) =>
        (existingConnection.from === connection.from && existingConnection.to === connection.to) ||
        (existingConnection.from === connection.to && existingConnection.to === connection.from),
    )
  }

  // ==============================================================================================

  // ==============================================================================================
  /**
   * 校验生成的连接关系和测点关系
   * @param {Object} templateData 模板数据，包含连接关系和测点要求
   * @param {Boolean} useTopologyComparison 是否使用拓扑比较（默认true）
   * @returns {Boolean|Object} 校验结果，成功返回 true，失败返回不匹配项
   */
  const validateConnections = (templateData, useTopologyComparison = true) => {
    // 兼容旧的调用方式（只传连接关系数组）
    if (Array.isArray(templateData)) {
      return validateConnectionsOnly(templateData, useTopologyComparison)
    }

    // 新的校验方式：同时校验连接关系和测点关系
    const { connections: baseConnections, testPointRequirements } = templateData

    // 1. 校验连接关系（使用拓扑比较）
    const connectionValidation = validateConnectionsOnly(baseConnections, useTopologyComparison)

    // 2. 校验测点关系
    const testPointValidation = validateTestPointRequirements(testPointRequirements)

    console.log('📊 连接关系校验:', connectionValidation === true ? '✅ 通过' : '❌ 不通过')
    console.log('📊 测点关系校验:', testPointValidation === true ? '✅ 通过' : '❌ 不通过')

    // 🔧 输出详细的错误信息
    if (connectionValidation !== true) {
      console.log('🔍 连接关系错误详情:', connectionValidation)
    }
    if (testPointValidation !== true) {
      console.log('🔍 测点关系错误详情:', testPointValidation)
    }

    // 如果连接关系和测点关系都校验通过
    if (connectionValidation === true && testPointValidation === true) {
      return true
    }

    // 返回详细的校验结果
    return {
      connectionValidation,
      testPointValidation,
      overall: false
    }
  }

  /**
   * 仅校验连接关系（保持向后兼容）
   * @param {Array} baseConnections 基准连接关系
   * @param {Boolean} useTopologyComparison 是否使用拓扑比较（默认true）
   * @returns {Boolean|Object} 校验结果
   */
  const validateConnectionsOnly = (baseConnections, useTopologyComparison = true) => {
    // 获取【用户连接关系】
    const newConnections = generateConnectionRelationships()
    console.log('📋 模板连接关系JSON:', JSON.stringify(baseConnections, null, 2))
    console.log('📋 用户连接关系JSON:', JSON.stringify(newConnections, null, 2))

    // 🔧 修复：处理空连接关系的边界情况
    if ((!baseConnections || baseConnections.length === 0) && (!newConnections || newConnections.length === 0)) {
      console.log('✅ 模板和用户连接关系都为空，校验通过')
      return true
    }

    if (useTopologyComparison) {
      // 使用拓扑比较方法
      const baseTopo = buildTopology(baseConnections)
      const newTopo = buildTopology(newConnections)

      console.log('@@@ 模板拓扑结构:', baseTopo)
      console.log('@@@ 当前拓扑结构:', newTopo)

      const isTopologyMatch = compareTopology(baseTopo, newTopo)

      if (isTopologyMatch) {
        return true
      } else {
        // 🔧 修复：正确处理拓扑对象差异分析
        const baseNodes = Object.keys(baseTopo)
        const currentNodes = Object.keys(newTopo)

        // 分析节点级别的差异
        const missingNodes = baseNodes.filter(node => !currentNodes.includes(node))
        const extraNodes = currentNodes.filter(node => !baseNodes.includes(node))

        // 分析连接级别的差异
        const connectionDifferences = []
        const allNodes = [...new Set([...baseNodes, ...currentNodes])]

        allNodes.forEach(node => {
          const baseConnections = baseTopo[node] || []
          const currentConnections = newTopo[node] || []

          const missingConns = baseConnections.filter(conn => !currentConnections.includes(conn))
          const extraConns = currentConnections.filter(conn => !baseConnections.includes(conn))

          if (missingConns.length > 0 || extraConns.length > 0) {
            connectionDifferences.push({
              node,
              missing: missingConns,
              extra: extraConns,
              baseCount: baseConnections.length,
              currentCount: currentConnections.length
            })
          }
        })

        // 计算总的连接数量
        const totalBaseConnections = baseNodes.reduce((sum, node) => sum + (baseTopo[node]?.length || 0), 0) / 2  // 除以2因为是双向连接
        const totalCurrentConnections = currentNodes.reduce((sum, node) => sum + (newTopo[node]?.length || 0), 0) / 2

        return {
          topologyMatch: false,
          baseTopology: baseTopo,
          currentTopology: newTopo,
          missingNodes,
          extraNodes,
          connectionDifferences,
          message: '电路拓扑结构不匹配',
          summary: {
            required: Math.round(totalBaseConnections),
            actual: Math.round(totalCurrentConnections),
            missing: missingNodes.length,
            extra: extraNodes.length,
            connectionDiffs: connectionDifferences.length
          }
        }
      }
    } else {
      // 使用传统的连接比较方法
      const baseSet = formatConnections(baseConnections)
      const newSet = formatConnections(newConnections)

      // 获取匹配的连接、缺失的连接、多余的连接
      const matchedConnections = findMatchedConnections(baseSet, newSet)
      const missingConnections = findMissingConnections(baseSet, newSet)
      const extraConnections = findExtraConnections(baseSet, newSet)

      // 如果没有缺失和多余的连接，校验通过
      if (missingConnections.length === 0 && extraConnections.length === 0) {
        return true
      }

      // 返回不匹配的连接信息
      return {
        matchedConnections,
        missingConnections,
        extraConnections,
      }
    }
  }

  /**
   * 校验测点关系
   * @param {Object} testPointRequirements 测点要求
   * @returns {Boolean|Object} 校验结果
   */
  const validateTestPointRequirements = (testPointRequirements) => {
    if (!testPointRequirements || Object.keys(testPointRequirements).length === 0) {
      // console.log('@@@ 没有测点要求，跳过测点校验')
      return true
    }

    try {
      // 获取当前用户的测点关系
      const currentTestPointData = JSON.parse(exportTestPointRelations())
      const currentTestPoints = currentTestPointData.groupedByTestPoint || {}

      console.log('📋 模板测点要求JSON:', JSON.stringify(testPointRequirements, null, 2))
      console.log('📋 用户测点关系JSON:', JSON.stringify(currentTestPoints, null, 2))

      // 🔧 添加测点分组匹配逻辑
      console.log('🔍 开始测点分组匹配...')

      // 提取模板分组
      const templateGroups = []
      Object.keys(testPointRequirements).forEach(testPointId => {
        const deviceTypes = testPointRequirements[testPointId].connectedDevices
          .map(d => d.typeName)
          .sort()
        templateGroups.push({
          id: testPointId,
          devices: deviceTypes,
          devicesStr: JSON.stringify(deviceTypes)
        })
      })

      // 提取用户分组
      const userGroups = []
      Object.keys(currentTestPoints).forEach(testPointId => {
        const deviceTypes = currentTestPoints[testPointId].connectedDevices
          .map(d => d.typeName)
          .sort()
        userGroups.push({
          id: testPointId,
          devices: deviceTypes,
          devicesStr: JSON.stringify(deviceTypes)
        })
      })

      console.log('📋 模板分组:', templateGroups.map(g => `测点${g.id}: [${g.devices.join(', ')}]`))
      console.log('📋 用户分组:', userGroups.map(g => `测点${g.id}: [${g.devices.join(', ')}]`))

      // 执行分组匹配
      const validationResults = {}
      let allValid = true
      let matchedCount = 0
      const usedUserGroups = new Set()

      templateGroups.forEach(templateGroup => {
        let foundMatch = false

        for (let i = 0; i < userGroups.length; i++) {
          if (usedUserGroups.has(i)) continue

          const userGroup = userGroups[i]
          if (templateGroup.devicesStr === userGroup.devicesStr) {
            foundMatch = true
            matchedCount++
            usedUserGroups.add(i)

            validationResults[templateGroup.id] = {
              required: templateGroup.devices,
              current: userGroup.devices,
              match: true,
              matchedWith: userGroup.id
            }

            console.log(`✅ 匹配成功: 模板测点${templateGroup.id} ↔ 用户测点${userGroup.id}`)
            break
          }
        }

        if (!foundMatch) {
          validationResults[templateGroup.id] = {
            required: templateGroup.devices,
            current: [],
            match: false
          }
          allValid = false
          console.log(`❌ 未匹配: 模板测点${templateGroup.id} [${templateGroup.devices.join(', ')}]`)
        }
      })

      // 检查用户多余的分组
      userGroups.forEach((userGroup, index) => {
        if (!usedUserGroups.has(index)) {
          console.log(`➕ 多余分组: 用户测点${userGroup.id} [${userGroup.devices.join(', ')}] 在模板中无对应`)
          allValid = false
        }
      })

      // 严格匹配：分组数量和内容都必须完全一致
      const isCompleteMatch = allValid && userGroups.length === templateGroups.length
      console.log(`📊 测点分组匹配结果: ${matchedCount}/${templateGroups.length} ${isCompleteMatch ? '✅ 完全匹配' : '❌ 不匹配'}`)

      if (isCompleteMatch) {
        return true
      }

      // 🔧 生成详细的错误信息
      const missingGroups = []
      const extraGroups = []
      const mismatchedGroups = []

      // 找出缺失的分组
      templateGroups.forEach(templateGroup => {
        if (!validationResults[templateGroup.id] || !validationResults[templateGroup.id].match) {
          missingGroups.push({
            id: templateGroup.id,
            requiredDevices: templateGroup.devices,
            description: `测点${templateGroup.id}: [${templateGroup.devices.join(', ')}]`
          })
        }
      })

      // 找出多余的分组
      userGroups.forEach((userGroup, index) => {
        if (!usedUserGroups.has(index)) {
          extraGroups.push({
            id: userGroup.id,
            actualDevices: userGroup.devices,
            description: `测点${userGroup.id}: [${userGroup.devices.join(', ')}]`
          })
        }
      })

      return {
        valid: false,
        details: validationResults,
        missingGroups,
        extraGroups,
        summary: {
          matched: matchedCount,
          required: templateGroups.length,
          actual: userGroups.length,
          missing: missingGroups.length,
          extra: extraGroups.length
        }
      }

    } catch (error) {
      console.error('测点关系校验时出错:', error)
      return {
        valid: false,
        error: error.message
      }
    }
  }

  /**
   * 规范化组件类型名称 - v1版本（原始版本，保留兼容性）
   * @param {String} type 原始组件类型
   * @returns {String} 规范化后的组件类型
   */
  const normalizeComponentTypeV1 = (type) => {
    if (!type) return type

    // 保持电容的极性区分 - 注意顺序！先判断无极性电容
    if (type.includes('无极性电容')) return '无极性电容'
    if (type.includes('极性电容')) return '极性电容'

    // 电阻类型规范化
    if (type.includes('电阻')) {
      if (type.includes('可变')) return '可变电阻'
      return '电阻'
    }

    // 示波器类型规范化
    if (type.includes('示波器')) return '示波器'

    // 测点类型规范化
    if (type.includes('测点')) return '测点'

    // 二极管类型规范化
    if (type.includes('二极管')) return '二极管'

    // 其他组件保持原样
    return type
  }

  /**
   * 规范化组件类型名称 - v1.1版本（智能匹配算法，自动支持新器件）
   * @param {String} type 原始组件类型
   * @returns {String} 规范化后的组件类型
   */
  const normalizeComponentTypeV1_1 = (type) => {
    if (!type) return type

    // 🚀 智能匹配算法：基于组件库自动匹配，支持新器件
    const typeStr = type.trim()

    // 获取所有组件类型，按长度降序排列（长词优先匹配，避免混淆）
    const componentTypes = componentsData
      .map(comp => comp.label)
      .filter(label => label && label.trim()) // 过滤空标签
      .sort((a, b) => b.length - a.length) // 长度降序：确保"无极性电容"优先于"极性电容"

    // 特殊处理：NPN/PNP 三极管的标准化
    if (typeStr === 'NPN') return 'NPN'
    if (typeStr === 'PNP') return 'PNP'

    // 智能匹配：找到第一个包含的组件类型
    for (const compType of componentTypes) {
      if (typeStr.includes(compType)) {
        return compType
      }
    }

    // 如果没有找到匹配，返回原始类型
    return type
  }

  /**
   * 统一的规范化组件类型函数 - 根据配置选择版本
   * @param {String} type 原始组件类型
   * @returns {String} 规范化后的组件类型
   */
  const normalizeComponentType = (type) => {
    // 根据配置选择使用哪个版本
    if (isUsingV1_1()) {
      return normalizeComponentTypeV1_1(type)
    } else {
      return normalizeComponentTypeV1(type)
    }
  }

  /**
   * 提取连接描述中的组件类型（忽略ID和其他参数）
   * @param {String} connectionDesc 连接描述字符串
   * @returns {String} 规范化的组件类型
   */
  const extractComponentType = (connectionDesc) => {
    if (!connectionDesc) return ''

    // 连接描述格式: "组件类型 - 组件ID - 参数 - 连接点 - 极性"
    const parts = connectionDesc.split(' - ')
    if (parts.length > 0) {
      return normalizeComponentType(parts[0].trim())
    }

    return ''
  }

  /**
   * 格式化连接关系为无向集合（基于组件类型，忽略ID）
   * @param {Array} connections 连接关系数组
   * @returns {Set} 格式化后的连接集合
   */
  const formatConnections = (connections) => {
    return new Set(
      connections.map((conn) => {
        // 提取组件类型，忽略ID和其他参数
        const fromType = extractComponentType(conn.from)
        const toType = extractComponentType(conn.to)

        // 生成基于组件类型的无向连接唯一键
        return JSON.stringify([fromType, toType].sort())
      }),
    )
  }

  /**
   * 查找匹配的连接
   * @param {Set} baseSet 基准连接集合
   * @param {Set} newSet 新生成的连接集合
   * @returns {Array} 匹配的连接
   */
  const findMatchedConnections = (baseSet, newSet) => {
    return [...baseSet].filter((conn) => newSet.has(conn)).map(JSON.parse)
  }

  /**
   * 查找缺失的连接
   * @param {Set} baseSet 基准连接集合
   * @param {Set} newSet 新生成的连接集合
   * @returns {Array} 缺失的连接
   */
  const findMissingConnections = (baseSet, newSet) => {
    return [...baseSet].filter((conn) => !newSet.has(conn)).map(JSON.parse)
  }

  /**
   * 查找多余的连接
   * @param {Set} baseSet 基准连接集合
   * @param {Set} newSet 新生成的连接集合
   * @returns {Array} 多余的连接
   */
  const findExtraConnections = (baseSet, newSet) => {
    return [...newSet].filter((conn) => !baseSet.has(conn)).map(JSON.parse)
  }

  /**
   * 构建连接拓扑图
   * @param {Array} connections 连接关系数组
   * @returns {Object} 拓扑图对象
   */
  const buildTopology = (connections) => {
    const topology = {}

    connections.forEach(conn => {
      const fromType = extractComponentType(conn.from)
      const toType = extractComponentType(conn.to)

      // 初始化节点
      if (!topology[fromType]) topology[fromType] = new Set()
      if (!topology[toType]) topology[toType] = new Set()

      // 添加双向连接
      topology[fromType].add(toType)
      topology[toType].add(fromType)
    })

    // 将Set转换为Array以便比较
    Object.keys(topology).forEach(key => {
      topology[key] = Array.from(topology[key]).sort()
    })

    return topology
  }

  /**
   * 比较两个拓扑结构是否相同
   * @param {Object} topo1 拓扑结构1
   * @param {Object} topo2 拓扑结构2
   * @returns {Boolean} 是否相同
   */
  const compareTopology = (topo1, topo2) => {
    const keys1 = Object.keys(topo1).sort()
    const keys2 = Object.keys(topo2).sort()

    // 🔧 修复：处理空拓扑的边界情况
    if (keys1.length === 0 && keys2.length === 0) {
      console.log('✅ 两个拓扑都为空，匹配成功')
      return true
    }

    // 比较节点数量
    if (keys1.length !== keys2.length) {
      console.log(`❌ 拓扑节点数量不匹配: ${keys1.length} vs ${keys2.length}`)
      return false
    }

    // 比较每个节点的连接
    for (let i = 0; i < keys1.length; i++) {
      if (keys1[i] !== keys2[i]) {
        return false
      }

      const connections1 = topo1[keys1[i]]
      const connections2 = topo2[keys2[i]]

      if (JSON.stringify(connections1) !== JSON.stringify(connections2)) {
        return false
      }
    }

    return true
  }

  // ==============================================================================================

  return {
    generateConnectionRelationships,
    validateConnections,
    generateGridPoints,
    extractComponentType,
    buildTopology,
  }
})
