import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSelectedComponentStore = defineStore('selectedComponent', () => {
  // 管理被选中的组件，初始值为 null
  const selectedComponent = ref(null)

  // 设置选中组件
  const setSelectedComponent = (component) => {
    console.log('@@@ 选中组件', component)
    selectedComponent.value = component
  }

  // 清除选中组件
  const clearSelectedComponent = () => {
    console.log('@@@ 清除选中组件', selectedComponent.value)
    selectedComponent.value = null
  }

  return {
    selectedComponent,
    setSelectedComponent,
    clearSelectedComponent,
  }
})
