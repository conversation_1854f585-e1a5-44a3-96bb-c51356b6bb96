<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电路实验环境</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- 添加 PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        /* 主容器布局 */
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
        }
        
        /* PDF 区域 - 左侧30% */
        .pdf-container {
            width: 30%;
            height: 100%;
            border-right: 1px solid #ddd;
            overflow: auto;
        }
        
        /* 电路图区域 - 右侧70% */
        .circuit-container {
            flex: 1;
            position: relative;
            min-height: 400px;
            background: #ffffff;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 电路图画布 - 居中显示 */
        .circuit-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 确保图片位于中心 */
        .circuit-canvas img {
            max-width: 85%;
            max-height: 85%;
            object-fit: contain;
            display: block;
        }
        
        /* 示波器样式 */
        .oscilloscope {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 240px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .oscilloscope-header {
            padding: 10px 15px;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }
        
        .oscilloscope-title {
            font-weight: bold;
            font-size: 14px;
        }
        
        .oscilloscope-content {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .oscilloscope-channel {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .channel-title {
            font-size: 13px;
            color: #666;
            width: 50px;
        }
        
        .channel-value {
            font-size: 13px;
            color: #999;
            flex: 1;
        }
        
        /* 探头样式 */
        .probe {
            width: 20px;
            height: 20px;
            border: 2px solid;
            border-radius: 50%;
            cursor: pointer;
            position: relative;
        }
        
        .probe-1 {
            border-color: #ff5722;
        }
        
        .probe-2 {
            border-color: #2196f3;
        }
        
        /* 探头连线样式 */
        .probe-line {
            position: fixed;
            height: 2px;
            border-top: 2px dashed;
            pointer-events: none;
            z-index: 90;
            transform-origin: left center;
        }
        
        .probe-line-1 {
            border-color: #ff5722;
        }
        
        .probe-line-2 {
            border-color: #2196f3;
        }
        
        /* 测试点样式 */
        .test-point {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: #FF5722;
            border: 2px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 100;
            transition: transform 0.1s;
        }
        
        .test-point-label {
            position: absolute;
            left: 12px;
            top: -5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .test-point:hover {
            transform: translate(-50%, -50%) scale(1.2);
        }
        
        .test-point.connected {
            background-color: #4CAF50;
        }
        
        .test-point.highlight {
            transform: translate(-50%, -50%) scale(1.3);
            box-shadow: 0 0 8px rgba(255, 87, 34, 0.8);
        }
        
        /* 标记测试点按钮 */
        .mark-point-btn {
            position: fixed;
            right: 20px;
            bottom: 20px;
            padding: 8px 16px;
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        
        .mark-point-btn:hover {
            background: #e8e8e8;
        }
        
        .mark-point-btn.active {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        
        .mark-point-btn .material-icons {
            font-size: 18px;
        }
        
        /* 测试点编辑弹窗 */
        .point-edit-dialog {
            position: fixed;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            min-width: 200px;
        }
        
        .point-edit-dialog input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .point-edit-dialog .buttons {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        
        .point-edit-dialog button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .point-edit-dialog .save-btn {
            background: #4CAF50;
            color: white;
        }
        
        .point-edit-dialog .cancel-btn {
            background: #f5f5f5;
            color: #333;
        }
        
        /* 上下文菜单样式 */
        .context-menu {
            position: absolute;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 5px 0;
            z-index: 2000;
        }
        
        .context-menu-item {
            padding: 8px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .context-menu-item:hover {
            background-color: #f5f5f5;
        }
        
        .context-menu-item.delete {
            color: #f44336;
        }
        
        /* 通知样式 */
        .notification {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        /* PDF样式 */
        .pdf-page {
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
            margin: 15px;
            padding: 20px;
            position: relative;
        }
        
        .pdf-page::after {
            content: attr(data-page);
            position: absolute;
            bottom: 5px;
            right: 10px;
            font-size: 10px;
            color: #999;
        }
        
        .pdf-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            color: #333;
        }
        
        .pdf-subtitle {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
            color: #444;
        }
        
        .pdf-paragraph {
            font-size: 12px;
            line-height: 1.5;
            margin-bottom: 8px;
            text-align: justify;
        }
        
        .pdf-list {
            font-size: 12px;
            padding-left: 20px;
            margin: 8px 0;
        }
        
        .pdf-list li {
            margin-bottom: 5px;
        }
        
        .pdf-image-placeholder {
            background-color: #f0f0f0;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            color: #999;
            font-size: 12px;
            border: 1px dashed #ddd;
        }
        
        .pdf-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            margin: 10px 0;
        }
        
        .pdf-table th, .pdf-table td {
            border: 1px solid #ddd;
            padding: 5px;
            text-align: center;
        }
        
        .pdf-table th {
            background-color: #f5f5f5;
        }
        
        /* 修改控制面板样式 - 移到右下角 */
        .control-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 10px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-button {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            background-color: #4caf50;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .control-button:hover {
            background-color: #45a049;
        }
        
        .control-button.active {
            background-color: #ff5722;
        }
        
        .control-button.active:hover {
            background-color: #e64a19;
        }
        
        /* PDF区域收起样式 */
        .container.pdf-collapsed .pdf-container {
            width: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
            border: none;
        }
        
        .container.pdf-collapsed .circuit-container {
            width: 100%;
            margin-left: 0;
        }
        
        .toggle-pdf-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 4px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 8px 12px;
            cursor: pointer;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }
        
        .toggle-pdf-btn:hover {
            background-color: #f5f5f5;
        }
        
        .toggle-pdf-icon {
            width: 16px;
            height: 16px;
            position: relative;
        }
        
        .toggle-pdf-icon:before, 
        .toggle-pdf-icon:after {
            content: '';
            position: absolute;
            background-color: #666;
            transition: transform 0.3s;
        }
        
        .toggle-pdf-icon:before {
            width: 2px;
            height: 16px;
            left: 7px;
            top: 0;
        }
        
        .toggle-pdf-icon:after {
            width: 16px;
            height: 2px;
            left: 0;
            top: 7px;
        }
        
        .pdf-collapsed .toggle-pdf-icon:before {
            transform: rotate(90deg);
        }
        
        /* 图片容器样式 */
        .circuit-image-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        /* 上传界面样式 */
        .upload-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(245, 245, 245, 0.9);
        }
        
        .upload-prompt {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .upload-text {
            margin-bottom: 20px;
            color: #666;
        }
        
        .upload-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        /* 绘图工具样式 */
        .drawing-tools {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            display: flex;
            gap: 10px;
        }
        
        .drawing-tools button {
            padding: 5px 10px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .drawing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 50;
            pointer-events: none;
        }
        
        .test-point.transparent {
            width: 16px;
            height: 16px;
            background-color: transparent;
            border: 2px solid transparent;
            cursor: pointer;
        }
        
        .test-point.transparent.highlight {
            background-color: rgba(255, 255, 0, 0.3); /* 悬停时显示半透明黄色 */
            border: 2px solid rgba(255, 165, 0, 0.5); /* 悬停时显示半透明橙色边框 */
        }
        
        .test-point.transparent.connected {
            background-color: rgba(0, 255, 0, 0.3); /* 连接时显示半透明绿色 */
            border: 2px solid rgba(0, 128, 0, 0.5); /* 连接时显示半透明深绿色边框 */
        }
        
        /* 电位器样式 */
        .potentiometer-container {
            position: absolute;
            bottom: 60px; /* 位于菜单栏上方 */
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 50px;
            padding: 15px;
            background-color: rgba(245, 245, 245, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }
        
        /* 旋钮式电位器 */
        .potentiometer-knob {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .knob {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
            border: 8px solid #ddd;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            user-select: none;
            --rotation: 0deg;
        }
        
        .knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 40%;
            background-color: #555;
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(var(--rotation));
            border-radius: 4px;
        }
        
        .knob-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .knob-value {
            font-size: 12px;
            color: #666;
            background-color: #fff;
            padding: 2px 8px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        
        /* LFO旋钮样式 */
        .lfo-knob {
            background: #1a1a1a;
            border: 2px solid #000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8), inset 0 1px 3px rgba(255, 255, 255, 0.2);
            --rotation: 0deg;
            width: 50px;
            height: 50px;
            position: relative;
        }
        
    
        
        /* 旋钮指针样式 - 增加宽度 */
        .lfo-knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #5DADE2;
            height: 45%;
            width: 4px;  /* 增加到4px */
            transform: translate(-50%, -100%) rotate(var(--rotation));
            transform-origin: bottom;
            border-radius: 2px;
            box-shadow: 0 0 5px rgba(93, 173, 226, 0.8);  /* 增加阴影效果 */
        }
        
        .lfo-container {
            position: absolute;
            right: 20px;
            bottom: 20px;
            z-index: 9999;
            background: none;
            box-shadow: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            pointer-events: auto;
        }
        
        .lfo-container .knob-label {
            font-size: 14px;
            color: #222;
            margin-top: 5px;
            font-weight: bold;
            text-transform: uppercase;
            text-align: center;
        }
        
        .lfo-container .knob-value {
            display: none; /* 隐藏值显示，更符合图片样式 */
        }
        
        /* 滑动式电位器 */
        .potentiometer-slider {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .slider-container {
            width: 200px;
            height: 40px;
            background-color: #e0e0e0;
            border-radius: 20px;
            position: relative;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .slider-handle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background-color: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            user-select: none;
        }
        
        .slider-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .slider-value {
            font-size: 12px;
            color: #666;
            background-color: #fff;
            padding: 2px 8px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        
        /* 如果有画布覆盖在旋钮上面，可以修改画布的样式 */
        .drawing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 50; /* 确保z-index低于LFO旋钮 */
            pointer-events: none; /* 在LFO旋钮区域不接收鼠标事件 */
        }
        
        /* 远程桌面按钮样式 - 优化尺寸 */
        .remote-desktop-button-container {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            padding: 0 15px;
        }
        
        .remote-desktop-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: auto;
            min-width: 120px;
            height: 28px;
            font-weight: normal;
        }
        
        .remote-desktop-button:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.25);
        }
        
        .remote-desktop-button:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .remote-icon {
            font-size: 14px;
        }

        /* 模拟PDF样式 */
        .mock-pdf {
            background: white;
            padding: 30px;
            height: 100%;
            overflow: auto;
            font-family: "Times New Roman", Times, serif;
            color: #333;
            line-height: 1.5;
        }

        .mock-pdf-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .mock-pdf-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .mock-pdf-subtitle {
            font-size: 16px;
            color: #555;
        }

        .mock-pdf-content {
            margin-bottom: 30px;
        }

        .mock-pdf-section {
            margin-bottom: 25px;
        }

        .mock-pdf-section h3 {
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .mock-pdf-section p {
            margin-bottom: 8px;
        }

        .mock-pdf-section ul {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .mock-pdf-section li {
            margin-bottom: 5px;
        }

        .mock-image-placeholder {
            background: #f5f5f5;
            border: 1px dashed #ccc;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            color: #777;
            font-style: italic;
        }

        .mock-pdf-footer {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            color: #777;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- PDF预览区域 -->
        <!-- <div class="pdf-container" id="pdf-container"> -->
            <!-- PDF内容将通过JavaScript加载 -->
        <!-- </div> -->
         <!-- 左侧PDF预览模拟 -->
<div id="pdf-container" class="pdf-container">
    <div class="mock-pdf">
        <div class="mock-pdf-header">
            <div class="mock-pdf-title">电路实验指导书</div>
            <div class="mock-pdf-subtitle">实验二：晶体管共射极单管放大器</div>
        </div>
        
        <div class="mock-pdf-content">
            <div class="mock-pdf-section">
                <h3>1. 实验目的</h3>
                <p>1) 掌握共射极单管放大器的工作原理</p>
                <p>2) 学习测量放大器的主要性能指标</p>
                <p>3) 了解放大器的频率特性</p>
            </div>
            
            <div class="mock-pdf-section">
                <h3>2. 实验仪器与材料</h3>
                <ul>
                    <li>晶体管 3DG11</li>
                    <li>电阻 1kΩ, 10kΩ, 100kΩ</li>
                    <li>电容 10μF, 100μF</li>
                    <li>直流电源</li>
                    <li>信号发生器</li>
                    <li>示波器</li>
                </ul>
            </div>
            
            <div class="mock-pdf-section">
                <h3>3. 实验原理</h3>
                <p>共射极放大电路是三极管放大电路的一种基本形式，它的特点是输入阻抗适中，输出阻抗较高，电压放大倍数大，功率放大能力强，但存在一定的非线性失真。</p>
                <div class="mock-pdf-image">
                    <div class="mock-image-placeholder">共射极放大器电路图</div>
                </div>
            </div>
            
            <div class="mock-pdf-section">
                <h3>4. 实验步骤</h3>
                <p><strong>4.1 静态工作点测量</strong></p>
                <p>1) 按照电路图搭建实验电路</p>
                <p>2) 接通电源，测量各点直流电压</p>
                <p>3) 计算静态工作点参数</p>
                
                <p><strong>4.2 动态参数测量</strong></p>
                <p>1) 接入信号发生器，设置1kHz正弦波信号</p>
                <p>2) 使用示波器观察输入、输出波形</p>
                <p>3) 测量电压放大倍数</p>
                
                <p><strong>4.3 频率特性测量</strong></p>
                <p>1) 保持输入信号幅度不变，改变频率</p>
                <p>2) 记录不同频率下的输出电压</p>
                <p>3) 绘制频率特性曲线</p>
            </div>
            
            <div class="mock-pdf-section">
                <h3>5. 注意事项</h3>
                <p>1) 接线前检查元件是否完好</p>
                <p>2) 注意电源极性，防止元件损坏</p>
                <p>3) 测量时注意选择合适的量程</p>
            </div>
        </div>
        
        <div class="mock-pdf-footer">
            <div class="mock-pdf-page">第 1 页 / 共 5 页</div>
        </div>
    </div>
</div>

        <!-- 实验环境区域 -->
        <div class="circuit-container">
            <div class="circuit-canvas" id="circuit-canvas">
                <!-- 测试点将在这里动态添加 -->
            </div>
            
            <!-- 直接添加LFO旋钮到电路容器 -->
            <div class="potentiometer-knob lfo-container">
                <div class="knob lfo-knob" id="lfo-knob">
                    <div class="arc-line"></div>
                </div>
                <div class="knob-label">LFO</div>
                <div class="knob-value" id="lfo-value">0.1Hz</div>
            </div>
        </div>
    </div>

    <!-- 可拖动示波器 -->
    <div class="oscilloscope" id="oscilloscope">
        <div class="oscilloscope-header" id="oscilloscope-header">
            <div class="oscilloscope-title">示波器</div>
            <span class="material-icons" style="font-size:14px">drag_indicator</span>
        </div>
        <div class="oscilloscope-content">
            <div class="oscilloscope-channel">
                <div class="probe probe-1" id="probe-1" data-channel="1"></div>
                <div class="channel-title">通道 1</div>
                <div class="channel-value" id="channel-1-value">未连接</div>
            </div>
            <div class="oscilloscope-channel">
                <div class="probe probe-2" id="probe-2" data-channel="2"></div>
                <div class="channel-title">通道 2</div>
                <div class="channel-value" id="channel-2-value">未连接</div>
            </div>
        </div>
        <!-- 添加远程桌面按钮 -->
        <div class="remote-desktop-button-container">
            <button id="open-remote-desktop" class="remote-desktop-button" onclick="openRemoteDesktop()">
                <span class="remote-icon">💻</span>
                打开远程桌面
            </button>
        </div>
    </div>

    <script>
        // 初始化 PDF.js
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        document.addEventListener('DOMContentLoaded', function() {
            // 加载PDF
            const pdfContainer = document.getElementById('pdf-container');
            const pdfUrl = localStorage.getItem('projectId');
            
            if (pdfUrl) {
                const pdfViewer = document.createElement('iframe');
                pdfViewer.src = pdfUrl;
                pdfViewer.style.width = '100%';
                pdfViewer.style.height = '100%';
                pdfViewer.style.border = 'none';
                pdfContainer.appendChild(pdfViewer);
            }

            // 获取电路画布
            const circuitCanvas = document.getElementById('circuit-canvas');
            const circuitContainer = document.querySelector('.circuit-container');
            
            // 确保容器有白色背景
            circuitContainer.style.background = '#ffffff';
            
            // 获取图像URL
            const imageUrl = localStorage.getItem('temp_circuit_png') || 
            localStorage.getItem('circuit_image');
            
            console.log('尝试加载电路图:', imageUrl ? imageUrl.substring(0, 50) + '...' : '无');
            
            // 图片元素引用，用于获取实际尺寸
            let circuitImage = null;
            
            if (imageUrl) {
                // 使用img元素
                const imgElement = document.createElement('img');
                imgElement.src = imageUrl;
                imgElement.style.maxWidth = '85%';
                imgElement.style.maxHeight = '85%';
                imgElement.style.objectFit = 'contain';
                imgElement.style.display = 'block';
                circuitImage = imgElement;
                
                // 确保图片容器是居中的
                const imageContainer = document.createElement('div');
                imageContainer.style.position = 'relative';
                imageContainer.style.display = 'flex';
                imageContainer.style.justifyContent = 'center';
                imageContainer.style.alignItems = 'center';
                imageContainer.style.width = '100%';
                imageContainer.style.height = '100%';
                imageContainer.appendChild(imgElement);
                
                // 添加事件以检测加载成功
                imgElement.onload = function() {
                    console.log('图片加载成功:', imgElement.width, 'x', imgElement.height);
                    // 加载测试点
                    loadTestPoints(imageContainer, imgElement);
                };
                
                imgElement.onerror = function() {
                    console.error('图片加载失败');
                    showDrawingInterface();
                };
                
                // 清空画布并添加图片容器
                circuitCanvas.innerHTML = '';
                circuitCanvas.appendChild(imageContainer);
            } else {
                console.warn('未找到电路图');
                showDrawingInterface();
            }
            
            function showDrawingInterface() {
                // 显示上传电路图的界面
                const uploadContainer = document.createElement('div');
                uploadContainer.className = 'upload-container';
                uploadContainer.innerHTML = `
                    <div class="upload-prompt">
                        <div class="upload-icon"><i class="material-icons">image</i></div>
                        <div class="upload-text">上传电路图或开始绘制</div>
                        <input type="file" id="circuitImageUpload" accept="image/*" style="display:none">
                        <button id="uploadImageBtn" class="upload-btn">上传图片</button>
                        <button id="drawCircuitBtn" class="upload-btn">绘制电路</button>
                    </div>
                `;
                
                circuitContainer.appendChild(uploadContainer);
                
                // 上传图片按钮事件
                document.getElementById('uploadImageBtn').addEventListener('click', function() {
                    document.getElementById('circuitImageUpload').click();
                });
                
                // 图片上传处理
                document.getElementById('circuitImageUpload').addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            const imageUrl = event.target.result;
                            // 保存到localStorage
                            localStorage.setItem('circuit_image', imageUrl);
                            // 重新加载页面
                            location.reload();
                        };
                        reader.readAsDataURL(e.target.files[0]);
                    }
                });
                
                // 绘制电路按钮事件
                document.getElementById('drawCircuitBtn').addEventListener('click', function() {
                    // 创建简单的绘图界面
                    uploadContainer.remove();
                    
                    const drawingTools = document.createElement('div');
                    drawingTools.className = 'drawing-tools';
                    drawingTools.innerHTML = `
                        <button id="saveDrawingBtn">保存电路图</button>
                        <button id="clearDrawingBtn">清除</button>
                    `;
                    circuitContainer.appendChild(drawingTools);
                    
                    // 创建画布
                    const canvas = document.createElement('canvas');
                    canvas.width = circuitContainer.offsetWidth;
                    canvas.height = circuitContainer.offsetHeight;
                    canvas.className = 'drawing-canvas';
                    canvas.style.border = '1px solid #ddd';
                    canvas.style.background = '#fff';
                    circuitContainer.appendChild(canvas);
                    
                    const ctx = canvas.getContext('2d');
                    ctx.strokeStyle = '#000';
                    ctx.lineWidth = 2;
                    
                    let isDrawing = false;
                    let lastX = 0;
                    let lastY = 0;
                    
                    // 绘图事件
                    canvas.addEventListener('mousedown', (e) => {
                        isDrawing = true;
                        lastX = e.offsetX;
                        lastY = e.offsetY;
                    });
                    
                    canvas.addEventListener('mousemove', (e) => {
                        if (!isDrawing) return;
                        ctx.beginPath();
                        ctx.moveTo(lastX, lastY);
                        ctx.lineTo(e.offsetX, e.offsetY);
                        ctx.stroke();
                        lastX = e.offsetX;
                        lastY = e.offsetY;
                    });
                    
                    canvas.addEventListener('mouseup', () => {
                        isDrawing = false;
                    });
                    
                    // 保存绘图
                    document.getElementById('saveDrawingBtn').addEventListener('click', function() {
                        const imageUrl = canvas.toDataURL('image/png');
                        localStorage.setItem('circuit_image', imageUrl);
                        alert('电路图已保存');
                        // 重新加载页面
                        location.reload();
                    });
                    
                    // 清除绘图
                    document.getElementById('clearDrawingBtn').addEventListener('click', function() {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    });
                });
            }
            
            // 示波器拖动功能
            const oscilloscope = document.getElementById('oscilloscope');
            const header = document.getElementById('oscilloscope-header');
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            // 探头相关变量
            let activeProbe = null;
            let probeLine = null;
            const probeLines = new Map();

            // 示波器拖动
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
                
                if (e.target === header || e.target.parentNode === header) {
                    isDragging = true;
                }
            }

            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    oscilloscope.style.transform = `translate(${currentX}px, ${currentY}px)`;
                    updateAllProbeLines();
                }
            }

            function dragEnd() {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            }

            // 探头拖动功能
            const probes = document.querySelectorAll('.probe');
            probes.forEach(probe => {
                probe.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    activeProbe = probe;
                    
                    // 移除已有连线
                    if (probe.connectedLine) {
                        probe.connectedLine.remove();
                        probe.connectedLine = null;
                        
                        // 恢复测试点状态
                        if (probe.connectedPoint) {
                            probe.connectedPoint.classList.remove('connected');
                            probe.connectedPoint = null;
                        }
                        
                        // 恢复通道显示
                        const channel = probe.getAttribute('data-channel');
                        const valueDisplay = document.getElementById(`channel-${channel}-value`);
                        valueDisplay.textContent = '未连接';
                        valueDisplay.style.color = '#999';
                    }

                    // 创建新连线 - 使用SVG实现曲线
                    probeLine = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    probeLine.setAttribute('width', '100%');
                    probeLine.setAttribute('height', '100%');
                    probeLine.style.position = 'fixed';
                    probeLine.style.top = '0';
                    probeLine.style.left = '0';
                    probeLine.style.pointerEvents = 'none';
                    probeLine.style.zIndex = '1001';
                    
                    // 创建路径
                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path.setAttribute('fill', 'none');
                    path.setAttribute('stroke', probe.getAttribute('data-channel') == '1' ? '#ff5722' : '#2196f3');
                    path.setAttribute('stroke-width', '2');
                    path.setAttribute('stroke-dasharray', '5,3'); // 保留虚线效果
                    probeLine.appendChild(path);
                    
                    document.body.appendChild(probeLine);

                    // 立即更新一次线条位置，确保起点正确
                    const probeRect = getProbeCenter(probe);
                    updateProbeLinePosition(probeRect.x, probeRect.y, e.clientX, e.clientY, probeLine);

                    document.addEventListener('mousemove', onProbeMove);
                    document.addEventListener('mouseup', onProbeUp);
                });
            });

            // 获取探头中心点的辅助函数
            function getProbeCenter(probe) {
                const rect = probe.getBoundingClientRect();
                // 获取探头的中心点
                const x = rect.left + rect.width / 2;
                const y = rect.top + rect.height / 2;
                
                // 获取探头的计算样式
                const style = window.getComputedStyle(probe);
                
                // 考虑边框宽度的影响
                const borderLeft = parseFloat(style.borderLeftWidth) || 0;
                const borderTop = parseFloat(style.borderTopWidth) || 0;
                
                // 考虑变换的影响
                const transform = style.transform;
                let offsetX = 0;
                let offsetY = 0;
                
                // 如果有变换，可能需要额外调整
                if (transform && transform !== 'none') {
                    // 这里可以添加变换的处理逻辑
                    console.log('探头有变换:', transform);
                }
                
                // 返回调整后的中心点
                return {
                    x: x + offsetX,
                    y: y + offsetY
                };
            }

            function onProbeMove(e) {
                if (!activeProbe || !probeLine) return;

                const probeCenter = getProbeCenter(activeProbe);
                updateProbeLinePosition(probeCenter.x, probeCenter.y, e.clientX, e.clientY, probeLine);

                // 检查是否悬停在测试点上
                const testPoints = document.querySelectorAll('[data-test-point="true"]');
                testPoints.forEach(point => {
                    const isNear = isPointNearTestPoint(e.clientX, e.clientY, point);
                    point.classList.toggle('highlight', isNear);
                });
            }

            function onProbeUp(e) {
                if (!activeProbe || !probeLine) return;
                
                const testPoints = document.querySelectorAll('[data-test-point="true"]');
                let connected = false;

                testPoints.forEach(point => {
                    if (isPointNearTestPoint(e.clientX, e.clientY, point)) {
                        connectProbeToTestPoint(activeProbe, point, probeLine);
                        connected = true;
                    }
                    point.classList.remove('highlight');
                });

                if (!connected && probeLine) {
                    probeLine.remove();
                }

                document.removeEventListener('mousemove', onProbeMove);
                document.removeEventListener('mouseup', onProbeUp);
                activeProbe = null;
            }

            // 连接探头到测试点
            function connectProbeToTestPoint(probe, testPoint, line) {
                const probeCenter = getProbeCenter(probe);
                const pointRect = testPoint.getBoundingClientRect();
                
                const endX = pointRect.left + pointRect.width / 2;
                const endY = pointRect.top + pointRect.height / 2;

                updateProbeLinePosition(probeCenter.x, probeCenter.y, endX, endY, line);
                
                probe.connectedLine = line;
                probe.connectedPoint = testPoint;
                probeLines.set(probe, line);
                
                // 更新测试点和探头状态
                testPoint.classList.add('connected');
                
                // 更新示波器显示
                const channel = probe.getAttribute('data-channel');
                const valueDisplay = document.getElementById(`channel-${channel}-value`);
                valueDisplay.textContent = testPoint.getAttribute('data-label');
                valueDisplay.style.color = '#4CAF50';
            }
            
            // 更新为曲线连线
            function updateProbeLinePosition(startX, startY, endX, endY, line) {
                if (!line) return;
                
                const path = line.querySelector('path');
                if (!path) return;
                
                // 计算控制点，使曲线看起来自然
                const dx = endX - startX;
                const dy = endY - startY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                // 为了创建曲线，我们需要让控制点偏离起点-终点连线
                // 计算垂直于连线的方向
                const perpX = -dy;
                const perpY = dx;
                const perpLength = Math.sqrt(perpX * perpX + perpY * perpY);
                
                // 曲线的弯曲程度
                const curvature = distance * 0.2; // 调整这个值可以改变弯曲程度
                
                // 计算控制点的偏移量
                const offsetX = perpX / perpLength * curvature;
                const offsetY = perpY / perpLength * curvature;
                
                // 第一个控制点：靠近起点，但有垂直偏移
                const controlX1 = startX + dx * 0.3 + offsetX;
                const controlY1 = startY + dy * 0.3 + offsetY;
                
                // 第二个控制点：靠近终点，但有垂直偏移
                const controlX2 = startX + dx * 0.7 + offsetX;
                const controlY2 = startY + dy * 0.7 + offsetY;
                
                // 创建贝塞尔曲线路径
                const pathData = `M ${startX},${startY} C ${controlX1},${controlY1} ${controlX2},${controlY2} ${endX},${endY}`;
                path.setAttribute('d', pathData);
            }

            function isPointNearTestPoint(x, y, testPoint) {
                const rect = testPoint.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const distance = Math.sqrt(
                    Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
                );
                return distance < 20; // 20px范围内判定为接近
            }

            function updateAllProbeLines() {
                probeLines.forEach((line, probe) => {
                    if (probe.connectedPoint) {
                        const probeCenter = getProbeCenter(probe);
                        const pointRect = probe.connectedPoint.getBoundingClientRect();
                        
                        const endX = pointRect.left + pointRect.width / 2;
                        const endY = pointRect.top + pointRect.height / 2;

                        updateProbeLinePosition(probeCenter.x, probeCenter.y, endX, endY, line);
                    }
                });
            }

            // 测试点添加功能
            const markPointBtn = document.getElementById('markPointBtn');
            const pointEditDialog = document.getElementById('pointEditDialog');
            const pointLabelInput = document.getElementById('pointLabel');
            const savePointBtn = document.getElementById('savePointBtn');
            const cancelPointBtn = document.getElementById('cancelPointBtn');
            
            let isMarkingMode = false;
            let tempPoint = null;
            let pointCounter = 0;

            // 修改添加测试点的函数，考虑图片的实际位置和尺寸
            markPointBtn.addEventListener('click', function() {
                isMarkingMode = !isMarkingMode;
                markPointBtn.classList.toggle('active');
                
                // 更新光标样式
                circuitCanvas.style.cursor = isMarkingMode ? 'crosshair' : 'default';
            });

            // 在电路画布上添加点击事件
            circuitCanvas.addEventListener('click', function(e) {
                if (!isMarkingMode) return;
                
                // 只有在有图片的情况下才允许添加测试点
                if (!circuitImage) {
                    alert('请先添加电路图');
                    return;
                }
                
                // 获取点击位置相对于电路图的百分比坐标
                const rect = circuitImage.getBoundingClientRect();
                
                // 计算点击位置相对于图片的百分比
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;
                
                if (x < 0 || x > 100 || y < 0 || y > 100) {
                    console.log('点击位置在图片外');
                    return;
                }
                
                // 创建临时测试点
                tempPoint = {
                    id: `test-point-${pointCounter++}`,
                    x: x,
                    y: y,
                    label: ''
                };
                
                // 显示编辑对话框
                pointEditDialog.style.display = 'block';
                pointEditDialog.style.left = `${e.clientX}px`;
                pointEditDialog.style.top = `${e.clientY}px`;

                pointLabelInput.focus();
            });
            
            // 保存测试点
            savePointBtn.addEventListener('click', function() {
                if (!tempPoint) return;
                
                tempPoint.label = pointLabelInput.value || `测点${pointCounter}`;
                
                // 获取图片容器
                const imageContainer = circuitImage.parentNode;
                
                // 创建测试点DOM元素
                const testPoint = document.createElement('div');
                testPoint.className = 'test-point transparent';
                testPoint.id = tempPoint.id;
                testPoint.setAttribute('data-test-point', 'true');
                testPoint.setAttribute('data-label', tempPoint.label);
                
                // 基于图片的百分比位置
                testPoint.style.left = `${tempPoint.x}%`;
                testPoint.style.top = `${tempPoint.y}%`;
                
                // 添加到图片容器
                imageContainer.appendChild(testPoint);
                
                // 保存测试点数据
                saveTestPoint(tempPoint);
                
                // 重置状态
                tempPoint = null;
                pointEditDialog.style.display = 'none';
                pointLabelInput.value = '';
            });

            // localStorage
            function loadTestPoints(container, imgElement) {
                const savedPoints = localStorage.getItem('circuit_test_points');
                if (!savedPoints) return;
                
                try {
                    // 检测缩放级别
                    const zoomLevel = window.devicePixelRatio || 1;
                    console.log(`当前浏览器缩放级别: ${Math.round(zoomLevel * 100)}%`);
                    
                    // 如果不是100%缩放，显示警告
                    if (Math.abs(zoomLevel - 1.0) > 0.01) {
                        const warning = document.createElement('div');
                        warning.style.position = 'fixed';
                        warning.style.top = '10px';
                        warning.style.left = '50%';
                        warning.style.transform = 'translateX(-50%)';
                        warning.style.backgroundColor = '#ffeb3b';
                        warning.style.color = '#000';
                        warning.style.padding = '10px 15px';
                        warning.style.borderRadius = '4px';
                        warning.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                        warning.style.zIndex = '9999';
                        warning.innerHTML = '⚠️ 为了保证测点定位准确，请将浏览器缩放设置为100%';
                        document.body.appendChild(warning);
                        
                        // 5秒后自动消失
                        setTimeout(() => {
                            if (document.body.contains(warning)) {
                                document.body.removeChild(warning);
                            }
                        }, 5000);
                    }
                    
                    // 固定的偏移量参数 - 基于100%缩放级别的最佳值
                    const manualOffsetX = 5.5;
                    const manualOffsetY = 4.15;
                    
                    // 等待图片完全加载以获取正确尺寸
                    if (imgElement.complete) {
                        addTestPoints();
                    } else {
                        imgElement.onload = addTestPoints;
                    }
                    
                    function addTestPoints() {
                        // 获取图片的实际显示区域
                        const imgRect = imgElement.getBoundingClientRect();
                        const containerRect = container.getBoundingClientRect();
                        
                        // 计算图片在容器中的偏移百分比
                        const offsetXPercent = ((containerRect.width - imgRect.width) / 2) / containerRect.width * 100;
                        const offsetYPercent = ((containerRect.height - imgRect.height) / 2) / containerRect.height * 100;
                        
                        console.log(`图片偏移量: X=${offsetXPercent}%, Y=${offsetYPercent}%`);
                        console.log(`图片尺寸: ${imgRect.width}x${imgRect.height}, 容器: ${containerRect.width}x${containerRect.height}`);
                        
                        const points = JSON.parse(savedPoints);
                        
                        points.forEach(point => {
                            const testPoint = document.createElement('div');
                            testPoint.className = 'test-point transparent';
                            testPoint.id = point.id;
                            testPoint.setAttribute('data-test-point', 'true');
                            testPoint.setAttribute('data-label', point.label || '');
                            
                            // 调整百分比坐标，考虑图片偏移、缩放和手动校正
                            const adjustedX = offsetXPercent + (point.x / 100 * (imgRect.width / containerRect.width) * 100) + manualOffsetX;
                            const adjustedY = offsetYPercent + (point.y / 100 * (imgRect.height / containerRect.height) * 100) + manualOffsetY;
                            
                            testPoint.style.left = `${adjustedX}%`;
                            testPoint.style.top = `${adjustedY}%`;
                            
                            container.appendChild(testPoint);
                            
                            console.log(`测点 ${point.label} 原始位置: (${point.x}%, ${point.y}%), 调整后: (${adjustedX.toFixed(2)}%, ${adjustedY.toFixed(2)}%)`);
                        });
                    }
                } catch (error) {
                    console.error('加载测试点失败:', error);
                }
            }

            // 保存测试点到 localStorage
            function saveTestPoint(point) {
                const testPoints = JSON.parse(localStorage.getItem('circuit_test_points') || '[]');
                testPoints.push(point);
                localStorage.setItem('circuit_test_points', JSON.stringify(testPoints));
            }

            // 初始化
            window.addEventListener('load', function() {
                // 在窗口大小变化时更新连线
                window.addEventListener('resize', updateAllProbeLines);
            });

            // 添加菜单栏
            addCircuitMenuBar(circuitContainer);
            
            // 添加电位器
            addPotentiometers(circuitContainer);
            
            // 初始化LFO旋钮
            setupLFOKnob();

            // 如果画布覆盖在旋钮上面，可以修改画布的样式
            const canvas = document.querySelector('.drawing-canvas');
            if (canvas) {
                const lfoContainer = document.querySelector('.lfo-container');
                if (lfoContainer) {
                    // 当鼠标在LFO旋钮上时，禁用画布的鼠标事件
                    lfoContainer.addEventListener('mouseenter', function() {
                        canvas.style.pointerEvents = 'none';
                    });
                    
                    lfoContainer.addEventListener('mouseleave', function() {
                        canvas.style.pointerEvents = 'auto';
                    });
                }
            }
        });

        // 添加电位器函数
        function addPotentiometers(container) {
            const potContainer = document.createElement('div');
            potContainer.className = 'potentiometer-container';
            
            // 创建旋钮式电位器
            const knobPot = document.createElement('div');
            knobPot.className = 'potentiometer-knob';
            knobPot.innerHTML = `
                <div class="knob" id="knob1"></div>
                <div class="knob-label">电阻调节</div>
                <div class="knob-value" id="knob1-value">50%</div>
            `;
            
            // 创建滑动式电位器
            const sliderPot = document.createElement('div');
            sliderPot.className = 'potentiometer-slider';
            sliderPot.innerHTML = `
                <div class="slider-container" id="slider1">
                    <div class="slider-handle" id="slider1-handle"></div>
                </div>
                <div class="slider-label">电压调节</div>
                <div class="slider-value" id="slider1-value">50%</div>
            `;
            
            potContainer.appendChild(knobPot);
            potContainer.appendChild(sliderPot);
            container.appendChild(potContainer);
            
            // 初始化旋钮电位器
            initKnobPotentiometer('knob1', 'knob1-value');
            
            // 初始化滑动电位器
            initSliderPotentiometer('slider1', 'slider1-handle', 'slider1-value');
        }

        // 初始化旋钮电位器
        function initKnobPotentiometer(knobId, valueId) {
            const knob = document.getElementById(knobId);
            const valueDisplay = document.getElementById(valueId);
            let currentRotation = 150; // 初始角度
            
            console.log("初始化旋钮:", knobId);
            
            // 设置初始值
            updateKnobRotation(currentRotation);
            
            // 添加点击事件 - 直接设置旋转角度
            knob.addEventListener('click', function(e) {
                console.log("旋钮点击事件触发");
                
                // 获取旋钮的位置信息
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算点击位置相对于中心的角度
                const clickX = e.clientX - centerX;
                const clickY = e.clientY - centerY;
                let angle = Math.atan2(clickY, clickX) * 180 / Math.PI;
                
                // 调整角度，使0度在顶部（12点钟方向）
                angle = (angle + 90) % 360;
                if (angle < 0) angle += 360;
                
                // 限制在0-300度范围内
                if (angle > 300) angle = 300;
                
                console.log(`点击角度: ${angle}度`);
                
                // 更新旋钮旋转
                updateKnobRotation(angle);
                
                // 防止事件冒泡，避免触发其他事件
                e.stopPropagation();
            });
            
            // 保留原有的拖动功能
            let isDragging = false;
            let startAngle = 0;
            
            knob.addEventListener('mousedown', function(e) {
                e.preventDefault();
                isDragging = true;
                
                // 计算鼠标相对于旋钮中心的角度
                const knobRect = knob.getBoundingClientRect();
                const knobCenterX = knobRect.left + knobRect.width / 2;
                const knobCenterY = knobRect.top + knobRect.height / 2;
                startAngle = Math.atan2(e.clientY - knobCenterY, e.clientX - knobCenterX) * 180 / Math.PI;
                
                document.addEventListener('mousemove', onKnobDrag);
                document.addEventListener('mouseup', onKnobRelease);
            });
            
            function onKnobDrag(e) {
                if (!isDragging) return;
                
                const knobRect = knob.getBoundingClientRect();
                const knobCenterX = knobRect.left + knobRect.width / 2;
                const knobCenterY = knobRect.top + knobRect.height / 2;
                
                // 计算当前角度
                const currentAngle = Math.atan2(e.clientY - knobCenterY, e.clientX - knobCenterX) * 180 / Math.PI;
                
                // 计算角度差
                let angleDiff = currentAngle - startAngle;
                
                // 更新旋转角度
                currentRotation = (currentRotation + angleDiff) % 360;
                
                // 限制旋转范围在0-300度之间
                if (currentRotation < 0) currentRotation = 0;
                if (currentRotation > 300) currentRotation = 300;
                
                updateKnobRotation(currentRotation);
                
                // 更新起始角度
                startAngle = currentAngle;
            }
            
            function onKnobRelease() {
                isDragging = false;
                document.removeEventListener('mousemove', onKnobDrag);
                document.removeEventListener('mouseup', onKnobRelease);
            }
            
            function updateKnobRotation(rotation) {
                // 更新当前旋转角度
                currentRotation = rotation;
                
                // 正确设置CSS变量 - 这是控制指针旋转的关键
                knob.style.setProperty('--rotation', `${rotation}deg`);
                
                // 计算百分比值 (0-300度映射到0-100%)
                const percentage = Math.round((rotation / 300) * 100);
                valueDisplay.textContent = `${percentage}%`;
                
                console.log(`旋钮更新: ${rotation}度, ${percentage}%`);
            }
        }

        // 初始化滑动电位器
        function initSliderPotentiometer(sliderId, handleId, valueId) {
            const slider = document.getElementById(sliderId);
            const handle = document.getElementById(handleId);
            const valueDisplay = document.getElementById(valueId);
            let isDragging = false;
            
            // 设置初始位置
            updateSliderPosition(50);
            
            handle.addEventListener('mousedown', function(e) {
                e.preventDefault();
                isDragging = true;
                document.addEventListener('mousemove', onSliderDrag);
                document.addEventListener('mouseup', onSliderRelease);
            });
            
            function onSliderDrag(e) {
                if (!isDragging) return;
                
                const sliderRect = slider.getBoundingClientRect();
                let position = (e.clientX - sliderRect.left) / sliderRect.width;
                
                // 限制在0-1范围内
                position = Math.max(0, Math.min(1, position));
                
                updateSliderPosition(position * 100);
            }
            
            function onSliderRelease() {
                isDragging = false;
                document.removeEventListener('mousemove', onSliderDrag);
                document.removeEventListener('mouseup', onSliderRelease);
            }
            
            function updateSliderPosition(percentage) {
                // 限制在滑块范围内
                const position = Math.max(0, Math.min(100, percentage));
                
                // 更新滑块位置
                handle.style.left = `${position}%`;
                
                // 更新显示值
                valueDisplay.textContent = `${Math.round(position)}%`;
                
                // 这里可以添加电位器值变化的回调函数
                console.log(`滑动电位器值: ${Math.round(position)}%`);
            }
        }

        // 添加电路菜单栏函数
        function addCircuitMenuBar(container) {
            // 空函数，保持代码结构完整
            console.log("菜单栏功能已初始化");
        }
        
        // 修改setupLFOKnob函数，实现类似图片的设计
        function setupLFOKnob() {
            console.log('设置可变电阻旋钮...');
            
            // 首先检查是否有可变电阻数据
            const resistorData = JSON.parse(localStorage.getItem('variable_resistors') || '[]');
            console.log('可变电阻数据:', resistorData);
            
            // 清除现有的所有LFO旋钮
            const existingLFOs = document.querySelectorAll('.lfo-container');
            existingLFOs.forEach(lfo => lfo.remove());
            
            // 如果没有可变电阻数据，只创建一个默认的LFO旋钮
            if (resistorData.length === 0) {
                console.log('未找到可变电阻数据，创建默认旋钮');
                createSingleLFOKnob();
                return;
            }
            
            // 为每个可变电阻创建一个LFO旋钮
            resistorData.forEach((resistor, index) => {
                console.log(`创建第 ${index+1} 个可变电阻旋钮:`, resistor);
                createLFOKnob(resistor, index);
            });
            
            console.log('可变电阻旋钮设置完成!');
        }

        // 创建单个默认LFO旋钮的函数
        function createSingleLFOKnob() {
            createLFOKnob({
                id: 'default', 
                name: 'R1',
                label: '可变电阻',
                defaultValue: 50,
                unit: 'KΩ'
            }, 0);
        }

        // 创建LFO旋钮的函数
        function createLFOKnob(resistor, index) {
            // 1. 创建LFO容器
            let lfoContainer = document.createElement('div');
            lfoContainer.className = 'lfo-container';
            lfoContainer.id = `lfo-container-${resistor.id || index}`;
            document.body.appendChild(lfoContainer);
            
            // 设置容器样式和位置 - 浅色背景
            lfoContainer.style.position = 'absolute';
            lfoContainer.style.right = `${20 + index * 100}px`; 
            lfoContainer.style.bottom = '20px';
            lfoContainer.style.zIndex = '10000';
            lfoContainer.style.width = '80px';
            lfoContainer.style.height = '110px'; // 调整高度
            lfoContainer.style.display = 'flex';
            lfoContainer.style.flexDirection = 'column';
            lfoContainer.style.alignItems = 'center';
            lfoContainer.style.justifyContent = 'center';
            lfoContainer.style.pointerEvents = 'auto';
            lfoContainer.style.background = 'rgba(240, 240, 240, 0.9)'; // 浅色背景
            lfoContainer.style.borderRadius = '8px'; // 圆角
            lfoContainer.style.padding = '8px 5px'; // 内边距
            lfoContainer.style.boxShadow = '0 2px 6px rgba(0,0,0,0.15)'; // 轻微阴影
            
            // 添加电阻标识符 - 只显示标识符，不显示"可变电阻"文字
            let resistorIdentifier = document.createElement('div');
            resistorIdentifier.className = 'resistor-identifier';
            
            // 获取电阻标识符
            const identifier = resistor.name || resistor.identifier || resistor.symbol || '';
            resistorIdentifier.textContent = identifier || `R${index + 1}`;
            
            // 设置标识符样式 - 深色文字
            resistorIdentifier.style.fontSize = '18px';
            resistorIdentifier.style.fontWeight = 'bold';
            resistorIdentifier.style.color = '#333'; // 深色文字
            resistorIdentifier.style.marginBottom = '8px';
            resistorIdentifier.style.textAlign = 'center';
            resistorIdentifier.style.width = '100%';
            lfoContainer.appendChild(resistorIdentifier);
            
            // 2. 创建旋钮 - 确保是圆形
            let knob = document.createElement('div');
            knob.className = 'lfo-knob';
            knob.id = `lfo-knob-${resistor.id || index}`;
            lfoContainer.appendChild(knob);
            
            // 设置旋钮样式 - 确保是完美的圆形
            const knobSize = 60; // 固定大小
            knob.style.width = `${knobSize}px`;
            knob.style.height = `${knobSize}px`;
            knob.style.borderRadius = '50%';
            knob.style.background = 'linear-gradient(135deg, #666, #333)'; // 深色旋钮
            knob.style.position = 'relative';
            knob.style.cursor = 'pointer';
            knob.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
            knob.style.border = '2px solid #555';
            knob.style.boxSizing = 'border-box'; // 确保边框不会改变尺寸
            
            // 3. 添加旋钮指示器
            let indicator = document.createElement('div');
            indicator.className = 'knob-indicator';
            knob.appendChild(indicator);
            
            // 设置指示器样式
            indicator.style.position = 'absolute';
            indicator.style.width = '2px';
            indicator.style.height = '20px';
            indicator.style.background = '#fff';
            indicator.style.top = '5px';
            indicator.style.left = '50%';
            indicator.style.transform = 'translateX(-50%)'; // 居中
            indicator.style.transformOrigin = 'bottom center';
            
            // 4. 添加旋钮中心点
            let centerDot = document.createElement('div');
            centerDot.className = 'knob-center';
            knob.appendChild(centerDot);
            
            // 设置中心点样式
            centerDot.style.position = 'absolute';
            centerDot.style.width = '10px';
            centerDot.style.height = '10px';
            centerDot.style.borderRadius = '50%';
            centerDot.style.background = '#888';
            centerDot.style.top = '50%';
            centerDot.style.left = '50%';
            centerDot.style.transform = 'translate(-50%, -50%)';
            
            // 5. 添加值显示 - 只显示数值和单位，不显示"可变电阻"文字
            let valueDisplay = document.createElement('div');
            valueDisplay.className = 'knob-value';
            valueDisplay.id = `knob-value-${resistor.id || index}`;
            
            // 使用可变电阻的默认值和单位
            const defaultValue = resistor.defaultValue || resistor.value || 50;
            const unit = resistor.unit || resistor.resistanceUnit || 'KΩ';
            valueDisplay.textContent = `${defaultValue}${unit}`;
            
            valueDisplay.style.fontSize = '14px';
            valueDisplay.style.fontWeight = 'bold';
            valueDisplay.style.color = '#333'; // 深色文字
            valueDisplay.style.marginTop = '8px';
            valueDisplay.style.textAlign = 'center';
            valueDisplay.style.width = '100%';
            lfoContainer.appendChild(valueDisplay);
            
            // 7. 设置旋钮旋转逻辑
            let currentRotation = 0;
            const minRotation = -135;
            const maxRotation = 135;
            
            // 8. 更新旋钮旋转的函数 - 只旋转旋钮本身，不旋转指针
            function updateKnobRotation(deg) {
                // 限制在有效范围内
                deg = Math.max(minRotation, Math.min(maxRotation, deg));
                
                // 更新旋钮旋转 - 只旋转旋钮，不旋转指针
                knob.style.transform = `rotate(${deg}deg)`;
                
                // 更新当前旋转角度
                currentRotation = deg;
                
                // 计算电阻值 (最小值到最大值)
                const normalizedRotation = (deg - minRotation) / (maxRotation - minRotation);
                
                // 获取电阻的最小值和最大值
                const minValue = 0;
                const maxValue = resistor.maxValue || 100;
                const resistanceValue = minValue + normalizedRotation * (maxValue - minValue);
                
                // 更新显示值
                valueDisplay.textContent = `${resistanceValue.toFixed(1)}${unit}`;
                
                // 保存到会话存储
                const knobValues = JSON.parse(localStorage.getItem('lfo_knob_values') || '{}');
                knobValues[resistor.id || index] = {
                    rotation: deg,
                    value: resistanceValue,
                    resistorId: resistor.id,
                    unit: unit
                };
                localStorage.setItem('lfo_knob_values', JSON.stringify(knobValues));
                
                console.log(`可变电阻 ${resistor.label || resistor.name} 旋转至: ${deg.toFixed(1)}度, ${resistanceValue.toFixed(1)}${unit}`);
            }
            
            // 9. 添加点击事件
            let isDragging = false;
            knob.addEventListener('click', function(e) {
                // 如果是拖动结束的点击，不处理
                if (isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算点击位置相对于中心的角度
                const clickX = e.clientX - centerX;
                const clickY = e.clientY - centerY;
                
                // 计算角度（以12点钟方向为0度，顺时针为正）
                let angle = Math.atan2(clickX, -clickY) * 180 / Math.PI;
                
                // 限制在有效范围内
                angle = Math.max(minRotation, Math.min(maxRotation, angle));
                
                console.log(`LFO点击角度: ${angle}度`);
                
                // 更新旋钮旋转
                updateKnobRotation(angle);
                
                // 防止事件冒泡
                e.stopPropagation();
                e.preventDefault();
            });
            
            // 10. 添加拖动功能
            let startAngle = 0;
            
            knob.addEventListener('mousedown', function(e) {
                if (e.button !== 0) return; // 只处理左键点击
                
                isDragging = true;
                console.log(`LFO旋钮 ${resistor.id || index} 开始拖动`);
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算起始角度
                const clickX = e.clientX - centerX;
                const clickY = e.clientY - centerY;
                startAngle = Math.atan2(clickX, -clickY) * 180 / Math.PI;
                
                document.addEventListener('mousemove', onDrag);
                document.addEventListener('mouseup', onRelease);
                
                // 防止事件冒泡和默认行为
                e.preventDefault();
                e.stopPropagation();
            });
            
            function onDrag(e) {
                if (!isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算当前角度
                const clickX = e.clientX - centerX;
                const clickY = e.clientY - centerY;
                const currentAngle = Math.atan2(clickX, -clickY) * 180 / Math.PI;
                
                // 计算角度差
                let angleDiff = currentAngle - startAngle;
                
                // 处理角度跨越边界的情况
                if (angleDiff > 180) angleDiff -= 360;
                if (angleDiff < -180) angleDiff += 360;
                
                // 更新旋转角度
                const newDeg = currentRotation + angleDiff;
                updateKnobRotation(newDeg);
                
                // 更新起始角度
                startAngle = currentAngle;
            }
            
            function onRelease() {
                console.log(`LFO旋钮 ${resistor.id || index} 停止拖动`);
                isDragging = false;
                document.removeEventListener('mousemove', onDrag);
                document.removeEventListener('mouseup', onRelease);
            }
            
            // 11. 禁用画布在LFO区域的事件
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach(canvas => {
                lfoContainer.addEventListener('mouseenter', function() {
                    canvas.style.pointerEvents = 'none';
                });
                
                lfoContainer.addEventListener('mouseleave', function() {
                    canvas.style.pointerEvents = 'auto';
                });
            });
            
            // 12. 从会话存储中恢复旋钮状态（如果有）
            const knobValues = JSON.parse(localStorage.getItem('lfo_knob_values') || '{}');
            if (knobValues[resistor.id || index]) {
                updateKnobRotation(knobValues[resistor.id || index].rotation);
            } else {
                // 设置初始旋转角度
                updateKnobRotation(0);
            }
        }

        // 监听会话存储变化，检测可变电阻数据的更新
        window.addEventListener('storage', function(e) {
            if (e.key === 'variable_resistors') {
                console.log('检测到可变电阻数据变化，更新LFO旋钮');
                setupLFOKnob();
            }
        });

        // 确保在多个时机调用setupLFOKnob
        document.addEventListener('DOMContentLoaded', setupLFOKnob);
        window.addEventListener('load', setupLFOKnob);
        setupLFOKnob();
        setTimeout(setupLFOKnob, 1000);

        // 打开远程桌面 - 浅色调风格
        function openRemoteDesktop() {
            console.log('开始打开远程桌面');
            
            // 创建远程桌面容器
            const desktopContainer = document.createElement('div');
            desktopContainer.id = 'remoteDesktopContainer';
            desktopContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 80%;
                height: 80%;
                background: white;
                z-index: 9999;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                display: flex;
                flex-direction: column;
                overflow: hidden;
            `;

            // 添加标题栏 - 浅色调
            const titleBar = document.createElement('div');
            titleBar.style.cssText = `
                padding: 10px 15px;
                background: #f5f7fa;
                color: #333;
                border-bottom: 1px solid #e5e9f0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
                border-radius: 8px 8px 0 0;
            `;
            titleBar.innerHTML = `
                <span style="font-weight: bold; font-size: 14px;">远程桌面</span>
                <button onclick="closeRemoteDesktop()" style="
                    border: none;
                    background: none;
                    cursor: pointer;
                    font-size: 20px;
                    color: #666;
                    padding: 0;
                    line-height: 1;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: background-color 0.2s;
                ">×</button>
            `;

            // 为关闭按钮添加悬停效果
            const closeButton = titleBar.querySelector('button');
            closeButton.onmouseover = function() {
                this.style.backgroundColor = '#f0f0f0';
            };
            closeButton.onmouseout = function() {
                this.style.backgroundColor = 'transparent';
            };

            // 创建内容区域
            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                flex: 1;
                display: flex;
                overflow: hidden;
                position: relative;
                background: #fff;
            `;

            // 创建iframe容器
            const iframeContainer = document.createElement('div');
            iframeContainer.style.cssText = `
                width: 100%;
                height: 100%;
                position: relative;
            `;

            // 添加远程桌面iframe
            const iframe = document.createElement('iframe');
            iframe.id = 'remoteIframe';
            iframe.style.cssText = `
                width: 100%;
                height: 100%;
                border: none;
            `;
            
            // 设置加载提示 - 浅色调
            const loadingDiv = document.createElement('div');
            loadingDiv.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: #666;
                font-size: 14px;
                background: rgba(255, 255, 255, 0.9);
                padding: 15px 25px;
                border-radius: 6px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            `;
            loadingDiv.innerHTML = '正在连接远程桌面...';
            
            // 添加iframe加载事件监听
            iframe.onload = function() {
                loadingDiv.style.display = 'none';
            };
            
            iframe.onerror = function() {
                loadingDiv.innerHTML = '连接远程桌面失败，请稍后重试';
                loadingDiv.style.color = '#e74c3c';
            };

            // 设置iframe的src为用户提供的远程桌面URL
            iframe.src = "https://sp.penevision.com:7443/guacamole/#/client/MwBjAG15c3Fs?username=tech&password=Tech1234&params=398909";
            iframe.frameBorder = "0";

            // 组装DOM结构
            iframeContainer.appendChild(iframe);
            iframeContainer.appendChild(loadingDiv);
            contentDiv.appendChild(iframeContainer);
            desktopContainer.appendChild(titleBar);
            desktopContainer.appendChild(contentDiv);

            // 添加遮罩层 - 更轻的透明度
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                z-index: 9998;
                backdrop-filter: blur(2px);
            `;
            
            // 点击遮罩层关闭弹窗
            overlay.onclick = function(e) {
                if (e.target === overlay) {
                    closeRemoteDesktop();
                }
            };

            // 添加到页面
            document.body.appendChild(overlay);
            document.body.appendChild(desktopContainer);

            console.log('远程桌面弹窗已创建');
        }

        // 关闭远程桌面
        function closeRemoteDesktop() {
            console.log('关闭远程桌面');
            const container = document.getElementById('remoteDesktopContainer');
            const overlay = document.querySelector('div[style*="backdrop-filter: blur(2px)"]');
            
            if (container) {
                container.remove();
            }
            
            if (overlay) {
                overlay.remove();
            }
        }
    </script>
</body>
</html>