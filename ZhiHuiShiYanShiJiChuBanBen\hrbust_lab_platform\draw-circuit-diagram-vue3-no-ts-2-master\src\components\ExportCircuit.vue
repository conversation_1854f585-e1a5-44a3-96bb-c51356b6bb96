function exportCircuit() {
  // ... 现有的导出代码 ...
  
  // 确保测试点坐标是相对于电路图的
  const circuitContainer = document.querySelector('.circuit-container');
  const containerRect = circuitContainer.getBoundingClientRect();
  
  // 获取所有测试点
  const testPointElements = document.querySelectorAll('.test-point');
  const testPoints = [];
  
  testPointElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    
    // 计算相对于容器的坐标
    const relativeX = rect.left - containerRect.left + rect.width/2;
    const relativeY = rect.top - containerRect.top + rect.height/2;
    
    testPoints.push({
      id: element.id || `point-${index}`,
      x: relativeX,
      y: relativeY
    });
  });
  
  console.log(`导出${testPoints.length}个测试点:`, testPoints);
  
  // 保存测试点数据
  sessionStorage.setItem('circuit_test_points', JSON.stringify(testPoints));
  
  // ... 其余导出代码 ...
} 