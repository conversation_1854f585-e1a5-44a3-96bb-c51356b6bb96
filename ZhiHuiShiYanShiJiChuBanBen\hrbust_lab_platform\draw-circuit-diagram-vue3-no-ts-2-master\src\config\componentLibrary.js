// 在组件库配置中添加测点组件
export const componentCategories = [
  {
    name: '基础元件',
    components: [
      // ... 其他基础元件 ...
      {
        type: 'testPoint',
        name: '测量点',
        icon: require('@/assets/icons/test-point.svg'), // 需要创建测点图标
        description: '用于在电路上标记测量点，可在仿真环境中连接示波器',
        width: 20,
        height: 20,
        isTestPoint: true, // 标记这是测点
        style: {
          backgroundColor: '#ff5722',
          borderRadius: '50%',
          border: '2px solid white',
          boxShadow: '0 0 4px rgba(0,0,0,0.5)'
        }
      }
    ]
  },
  // ... 其他分类 ...
] 