<!--draggable="true"  允许拖拽
dragstart 拖拽开始时
	dragover 允许放在画布上
	drop”被拖放到画布上时
-->
<template>
  <div
    class="canvas-area"
    id="canvas-area"
    ref="canvas"
    @mousemove="(event) => onMouseMove(event)"
    @click="(event) => { onMouseClick(event); if (canvas) canvas.focus() }"
    @dragover.prevent
    @drop="handleDrop"
    @keydown="handleCanvasKeydown"
    tabindex="0">
    <svg
      class="grid"
      :width="canvasSize.width"
      :height="canvasSize.height"
      :viewBox="`0 0 ${canvasSize.width} ${canvasSize.height}`">
      <!-- 绘制网格 -->
      <line
        v-for="line in gridLines"
        :key="line.key"
        :x1="line.x1"
        :y1="line.y1"
        :x2="line.x2"
        :y2="line.y2"
        stroke="#ccc"
        stroke-width="1" />

      <!-- 高亮的点 -->
      <g v-if="highlightedPoint">
        <line
          :x1="highlightedPoint.x - 10"
          :y1="highlightedPoint.y - 10"
          :x2="highlightedPoint.x + 10"
          :y2="highlightedPoint.y + 10"
          stroke="red"
          stroke-width="1"
          stroke-dasharray="0" />
        <line
          :x1="highlightedPoint.x - 10"
          :y1="highlightedPoint.y + 10"
          :x2="highlightedPoint.x + 10"
          :y2="highlightedPoint.y - 10"
          stroke="red"
          stroke-width="1"
          stroke-dasharray="0" />
      </g>

      <!-- 动态连线 -->
      <polyline
        v-if="currentPath.length"
        :points="currentPath.map((p) => `${p.x},${p.y}`).join(' ')"
        fill="none"
        stroke="#7f848e"
        stroke-width="2"
        stroke-dasharray="5" />

      <!-- 固定连线 -->
      <polyline
        class="fixedLines"
        v-for="(line, index) in fixedLines"
        :class="{
          hovered: index === hoveredLine,
          selected: index === selectedLine,
        }"
        :key="index"
        :points="line.map((p) => `${p.x},${p.y}`).join(' ')"
        @mouseover="handleMouseOver(index)"
        @mouseout="handleMouseOut(index)"
        @click="handleLineClick(index)" />

      <!-- 高亮交点 -->
      <circle
        v-for="(point, index) in intersections"
        :key="index"
        :cx="point.x"
        :cy="point.y"
        r="3"
        stroke="black"
        fill="black"
        stroke-width="5" />

      <!-- 绘制文本框 -->
      <g
        v-for="(textBox, index) in textBoxs"
        :key="textBox.id"
        :transform="getTextBoxTransform(textBox)"
        @click.stop="!isLineDrawing ? setSelectedTextBox(textBox) : onMouseClick($event)"
        @dblclick="!isLineDrawing ? handleDoubleClickTextBox(textBox, $event) : onMouseClick($event)"
        @mousedown="startTextBoxDrag(textBox, $event)">
        <rect
          width="100"
          height="30"
          fill="transparent"
          stroke="transparent"
          stroke-width="1"
          :style="{ cursor: 'move', pointerEvents: 'all' }" />
        <text
          x="10"
          y="20"
          fill="black"
          :style="{
            fontWeight: 'bold',
            fontSize: '20px',
            fontFamily: 'Arial, sans-serif' /* 字体设置为 Arial，可根据需求更改 */,
            cursor: 'move',
            pointerEvents: 'none',
          }">
          {{ textBox.content }}
        </text>
      </g>

      <!-- 绘制组件 -->
      <g
        v-for="(component, index) in props.components"
        :key="index"
        class="component"
        :data-component-type="component.type"
        :data-label="component.identifier"
        :id="component.componentId"
        :transform="`
            translate(${component.x}, ${component.y})
            scale(${component.scale})
            rotate(${component.rotation} ${getComponentWH(component).componentWidth / component.scale / 2} ${getComponentWH(component).componentHeight / component.scale / 2})
          `"
        @mousedown="!isLineDrawing ? startDrag(component, $event) : null"
        @click.stop="!isLineDrawing ? handleComponentClick(component, $event) : onMouseClick($event)"
        @dblclick="!isLineDrawing ? handleDoubleClickComponent(component, $event) : onMouseClick($event)"
        :style="isLineDrawing ? {} : { cursor: 'move' }">
        <!-- 添加透明矩形作为交互区域，确保在整个组件区域都能触发 hover 效果 -->
        <rect
          :width="getComponentWH(component).componentWidth / component.scale"
          :height="getComponentWH(component).componentHeight / component.scale"
          :fill="'transparent'"
          :pointer-events="'all'" />
        <g v-html="component.icon"
           :class="{ 'selected-component': selectedComponent && selectedComponent.componentId === component.componentId }"></g>
      </g>
    </svg>

    <!-- 旋转提示框 -->
    <div
      v-if="showRotationTip"
      class="rotation-tip">
      按空格键可以旋转组件
      <span class="tip-close" @click="showRotationTip = false">×</span>
    </div>
  </div>
</template>

<script setup name="CanvasArea">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { storeToRefs } from 'pinia'
  import { saveData } from '@/utils/canvasDataManager.js'
  import { getCircuitDataAndLoadData } from '@/utils/before/validateURLParam.js'

  // 画布
  const canvas = ref(null)

  /**
   * store: 画布信息
   */
  import { useCanvasInfoStore } from '@/store/canvasInfo.js'
  const canvasInfo = useCanvasInfoStore()
  const { canvasSize } = storeToRefs(canvasInfo)

  /**
   * store: 连线模式
   */
  import { useLineModeStore } from '@/store/lineMode.js'
  const { isLineDrawing } = storeToRefs(useLineModeStore())

  /**
   * store: 连线状态
   */
  import { useLineStateStore } from '@/store/lineState.js'
  const lineStateStore = useLineStateStore()
  const { fixedLines, selectedLine, hoveredLine, intersections } = storeToRefs(lineStateStore)

  /**
   * store: 组件状态
   */
  import { useComponentInfoStore } from '@/store/componentInfo.js'
  const { getComponentWH } = useComponentInfoStore()

  /**
   * store: 组件选中状态
   */
  import { useSelectedComponentStore } from '@/store/selectedComponent.js'
  const selectedComponentStore = useSelectedComponentStore()
  const { selectedComponent } = storeToRefs(selectedComponentStore)
  const { setSelectedComponent } = selectedComponentStore

  /**
   * store: 文本框
   */
  import { useTextBoxStore } from '@/store/textBox.js'
  const textBoxStore = useTextBoxStore()
  const { textBoxs } = storeToRefs(textBoxStore)
  const { setSelectedTextBox } = textBoxStore

  /**
   * store: 组件信息
   */
  import { useComponentsInfoStore } from '@/store/componentsInfo.js'
  const componentsInfoStore = useComponentsInfoStore()
  const { components } = storeToRefs(componentsInfoStore)

  /**
   * store: 按钮
   */
  import { useButtonBarInfoStore } from '@/store/buttonBarInfo.js'
  const buttonBarInfoStore = useButtonBarInfoStore()
  const { openDrawer, openTextBoxDrawer } = buttonBarInfoStore



  /**
   * hooks: 删除组件
   */
  import usedeleteComponent from '@/hooks/usedeleteComponent.js'
  const { deleteComponent } = usedeleteComponent()

  /**
   * hooks: 编辑组件（旋转）
   */
  import useEditComponent from '@/hooks/useEditComponent.js'
  const { rotateComponent } = useEditComponent()

  /**
   * 计算文本框的变换（跟随组件旋转）
   */
  const getTextBoxTransform = (textBox) => {
    // 如果文本框绑定了组件且组件有旋转，做简单的位置偏移避免遮盖
    if (textBox.componentId) {
      const component = components.value.find(comp => comp.componentId === textBox.componentId)
      if (component && component.rotation && (component.rotation % 360 !== 0)) {
        // 只在组件旋转时做简单偏移，避免与组件重叠
        let offsetX = 0
        let offsetY = 0

        const rotation = component.rotation % 360

        // 根据旋转角度做简单的避让偏移
        if (rotation === 90 || rotation === 270) {
          // 组件变成竖直时，文字左右偏移
          if (textBox.type === 'top') {
            offsetX = rotation === 90 ? 40 : -40  // 右偏移或左偏移（减少10px）
          } else if (textBox.type === 'bottom') {
            offsetX = rotation === 90 ? -40 : 40  // 左偏移（减少10px）或右偏移
          }
        } else if (rotation === 180) {
          // 组件倒置时，文字上下偏移
          if (textBox.type === 'top') {
            offsetY = 70  // 向下偏移
          } else if (textBox.type === 'bottom') {
            offsetY = -60  // 向上偏移
          }
        }

        return `translate(${textBox.offsetX + offsetX}, ${textBox.offsetY + offsetY})`
      }
    }

    // 默认情况：保持原有位置（支持拖动）
    return `translate(${textBox.offsetX}, ${textBox.offsetY})`
  }

  // 旋转提示相关状态
  const showRotationTip = ref(false)
  const hasShownRotationTip = ref(false)

  // 显示旋转提示
  const showRotationHint = () => {
    if (!hasShownRotationTip.value) {
      showRotationTip.value = true
      hasShownRotationTip.value = true

      // 3秒后自动隐藏提示
      setTimeout(() => {
        showRotationTip.value = false
      }, 3000)
    }
  }

  // 动态更新画布尺寸
  const updateCanvasSize = () => {
    if (!canvas.value) return
    const rect = canvas.value.getBoundingClientRect()
    canvasSize.value.width = rect.width > 0 ? rect.width : 800 // 确保宽度有效
    canvasSize.value.height = rect.height > 0 ? rect.height : 600 // 确保高度有效
  }

  // 监听窗口大小变化
  onMounted(() => {
    window.addEventListener('resize', updateCanvasSize)
    updateCanvasSize()
  })
  onUnmounted(() => {
    window.removeEventListener('resize', updateCanvasSize)
  })

  /**
   * hooks: 处理画布事件的【获得焦点】和【失去焦点】
   */

  /**
   * hooks: 生成网格（动态响应画布尺寸）
   */
  import useGrid from '@/hooks/useGrid'
  const { gridLines } = useGrid()

  /**
   * hooks: 连线逻辑
   */
  import useLineDrawing from '@/hooks/useLineDrawing'
  const { currentPath, highlightedPoint, onMouseMove, onMouseClick, handleKeydown } = useLineDrawing(canvas)

  /**
   * 画布键盘事件处理（包含连线、删除组件/连线、旋转组件）
   */
  const handleCanvasKeydown = (event) => {
    // 处理连线相关的键盘事件
    handleKeydown(event)

    // 处理空格键旋转组件
    if (event.code === 'Space') {
      console.log('🔑 空格键被按下')
      console.log('📦 选中的组件:', selectedComponent.value)

      // 如果有选中的组件，则右旋90°
      if (selectedComponent.value) {
        console.log('🔄 右旋组件90°')
        event.preventDefault() // 阻止空格键的默认行为（页面滚动）
        rotateComponent(90)
      } else {
        console.log('❌ 没有选中组件，无法旋转')
      }
    }

    // 处理删除的键盘事件
    if (event.key === 'Delete' || event.key === 'Backspace') {
      console.log('🔑 删除键被按下')
      console.log('📦 选中的组件:', selectedComponent.value)
      console.log('📏 选中的连线:', selectedLine.value)

      // 优先删除选中的组件
      if (selectedComponent.value) {
        console.log('🗑️ 删除选中的组件')
        event.preventDefault()
        deleteComponent()
      }
      // 如果没有选中组件，则删除选中的连线
      else if (selectedLine.value !== null) {
        console.log('🗑️ 删除选中的连线')
        event.preventDefault()
        deleteSelectedLine()
      } else {
        console.log('❌ 没有选中任何可删除的元素')
      }
    }
  }

  /**
   * 确保画布获得焦点以接收键盘事件
   */
  onMounted(() => {
    if (canvas.value) {
      canvas.value.focus()
    }
  })

  /**
   * hooks: 删除连线逻辑
   */
  import useDeleteLine from '@/hooks/useDeleteLine'
  const { handleMouseOver, handleMouseOut, handleLineClick, deleteSelectedLine } = useDeleteLine()

  /**
   * hooks: 拖拽组件，从 【元器件库】 -> 【画布】
   */
  import useDropComponent from '@/hooks/useDropComponent'
  const emit = defineEmits(['add-component']) // 用于【触发父组件事件行为】
  const props = defineProps({ components: Array }) // 用于【接收父组件传来的参数】
  const { handleDrop } = useDropComponent(emit)

  /**
   * hooks: 在画布内【绘制组件】
   * 目前直接将样式写进了标签内部。
   */

  /**
   * hooks: 拖拽组件，在【画布内部】拖拽
   */
  import useDragComponent from '@/hooks/useDragComponent.js'
  const { startDrag } = useDragComponent(props.components)

  /**
   * hooks: 拖拽文本框，在【画布内部】拖拽
   */
  import useDragTextBox from '@/hooks/useDragTextBox.js'
  const { startTextBoxDrag } = useDragTextBox()

  /**
   * 处理组件点击事件
   */
  const handleComponentClick = (component, event) => {
    console.log('🖱️ 点击组件:', component.label || component.type)
    // 设置选中组件
    setSelectedComponent(component)

    // 首次选中组件时显示旋转提示
    showRotationHint()

    // 确保画布保持焦点，以便键盘删除功能正常工作
    if (canvas.value) {
      canvas.value.focus()
      console.log('🎯 画布已获得焦点')
    }
  }

  /**
   * 处理组件双击事件
   */
  const handleDoubleClickComponent = (component) => {
    // 设置选中组件
    setSelectedComponent(component)
    // 确保画布保持焦点
    if (canvas.value) {
      canvas.value.focus()
    }
    // 显示编辑抽屉
    openDrawer()
  }

  /**
   * 处理文本框双击事件
   */
  const handleDoubleClickTextBox = (textBox) => {
    // 设置选中组件
    setSelectedTextBox(textBox)
    // 显示编辑抽屉
    openTextBoxDrawer()
  }
</script>

<style scoped>
  .canvas-area {
    position: relative;
    width: 95%;
    height: calc(100vh - 200px);
    border: 1px dashed #d3d3d3;
    background-color: #f3f6f8;
    margin: 20px auto;
    overflow: visible; /* 改为 visible */
    outline: none; /* 隐藏默认的焦点样式 */
  }

  svg.grid {
    display: block; /* 避免 inline 元素的额外间距 */
    margin: 0;
    padding: 0;
  }

  circle {
    pointer-events: none; /* 防止影响鼠标事件 */
  }

  .fixedLines {
    /* 默认颜色 */
    stroke: black;
    /* 默认线条宽度 */
    stroke-width: 2;
    fill: none;
    /* 添加过渡动画 */
    transition:
      stroke-width 0.2s ease,
      stroke 0.2s ease;

    &.hovered {
      /* 鼠标样式为移动光标（四个方向箭头） */
      cursor: move;
    }

    &.selected {
      /* 选中时颜色保持不变 */
      stroke: black;
      /* 添加蓝色阴影效果 */
      filter: drop-shadow(0px 0px 3px rgba(0, 0, 255, 1));
    }
  }

  /* 选中组件的光晕效果 */
  .selected-component {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))
            drop-shadow(0 0 16px rgba(59, 130, 246, 0.4))
            drop-shadow(0 0 24px rgba(59, 130, 246, 0.2));
    animation: selectedGlow 2s ease-in-out infinite alternate;
  }

  @keyframes selectedGlow {
    from {
      filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))
              drop-shadow(0 0 16px rgba(59, 130, 246, 0.4))
              drop-shadow(0 0 24px rgba(59, 130, 246, 0.2));
    }
    to {
      filter: drop-shadow(0 0 12px rgba(59, 130, 246, 1))
              drop-shadow(0 0 20px rgba(59, 130, 246, 0.6))
              drop-shadow(0 0 32px rgba(59, 130, 246, 0.3));
    }
  }

  /* 旋转提示框 - 白色底板样式 */
  .rotation-tip {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #374151;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid #e5e7eb;
    border-left: 4px solid #3b82f6;
    animation: slideInRight 0.3s ease-out;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .rotation-tip::before {
    content: '💡';
    font-size: 16px;
    margin-right: 4px;
  }

  .tip-close {
    margin-left: 8px;
    cursor: pointer;
    font-weight: bold;
    color: #9ca3af;
    padding: 4px 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tip-close:hover {
    color: #374151;
    background: #f3f4f6;
    transform: scale(1.1);
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
</style>
