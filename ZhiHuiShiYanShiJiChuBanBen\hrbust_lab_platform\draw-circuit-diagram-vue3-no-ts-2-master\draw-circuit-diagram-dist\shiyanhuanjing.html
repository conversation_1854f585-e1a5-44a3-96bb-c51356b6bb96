<!DOCTYPE html>
<html>

	<head>
		<base href="/labhtml/">
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc.">
		<meta name="author" content="Coderthemes">

		<link rel="shortcut icon" href="images/favicon_1.ico">

		<title>智慧实验室 </title>

		<link href="plugins/summernote/dist/summernote.css" rel="stylesheet" />
		<link href="plugins/sweetalert/dist/sweetalert.css" rel="stylesheet" type="text/css">
		<!-- select2 css -->
		<link href="plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
		<!-- 公共css -->
		<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
		<link href="css/core.css" rel="stylesheet" type="text/css" />
		<link href="css/components.css" rel="stylesheet" type="text/css" />
		<link href="css/icons.css" rel="stylesheet" type="text/css" />
		<link href="css/pages.css" rel="stylesheet" type="text/css" />
		<link href="css/responsive.css" rel="stylesheet" type="text/css" />
		<!-- HTML5 Shiv and Respond.js IE8 support of HTML5 elements and media queries -->
		<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
		<!--[if lt IE 9]>
  <script src="js/html5shiv.js"></script>
  <script src="js/respond.min.js"></script>
  <![endif]-->

		<script src="js/modernizr.min.js"></script>
		<style type="text/css">
			.note-editor .note-toolbar {
				display: none;
			}
			  /* 浮动框样式 */
			  .floating-box {
            position: fixed;
            top: 0;
            left: -550px; /* 初始位置 */
            width: 550px;
            height: 100%;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.2); /* 修改阴影效果 */
            transition: left 0.3s ease;
            z-index: 1000;
        }

        /* 背景遮罩层 */
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全移除遮罩层的背景色和显示 */
            z-index: 999;
            pointer-events: none; /* 保持点击穿透 */
            visibility: hidden; /* 完全隐藏遮罩层 */
        }

        /* 显示状态 */
        .floating-box.active {
            left: 0 !important; /* 使用 !important 确保优先级 */
        }
        .overlay.active {
            display: block;
        }

        /* iframe 样式 */
        .editor {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 拖拽手柄样式 */
        .resize-handle {
            position: absolute;
            right: 0;
            top: 0;
            width: 5px;
            height: 100%;
            background-color: #ccc;
            cursor: ew-resize;
        }

        .resize-handle:hover {
            background-color: #ccc;
        }

		</style>
		<style>
		/* 已有样式保持不变 */

		/* 添加关闭按钮样式 */
		.close-button {
			position: absolute;
			top: 15px;
			right: 15px;
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background-color: #f0f0f0;
			border: none;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: background-color 0.3s;
		}

		.close-button:hover {
			background-color: #e0e0e0;
		}

		.close-button::before,
		.close-button::after {
			content: '';
			position: absolute;
			width: 15px;
			height: 2px;
			background-color: #666;
			transition: background-color 0.3s;
		}

		.close-button::before {
			transform: rotate(45deg);
		}

		.close-button::after {
			transform: rotate(-45deg);
		}

		.close-button:hover::before,
		.close-button:hover::after {
			background-color: #333;
		}

		/* 添加浮窗标题样式 */
		.floating-box-header {
			padding: 15px 20px;
			border-bottom: 1px solid #eee;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 60px; /* 固定标题栏高度 */
			box-sizing: border-box;
		}

		.floating-box-title {
			font-size: 18px;
			font-weight: 500;
			color: #333;
			margin: 0;
		}

		.floating-box-content {
			height: calc(100% - 60px); /* 减去标题栏高度 */
			padding: 0; /* 移除内边距 */
			overflow: auto; /* 添加滚动条 */
		}

		/* 添加全屏按钮样式 */
		.expand-button {
			position: absolute;
			top: 15px;
			right: 55px; /* 位于关闭按钮左侧 */
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background-color: #f0f0f0;
			border: none;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: background-color 0.3s;
		}

		.expand-button:hover {
			background-color: #e0e0e0;
		}

		/* 全屏按钮的图标样式 */
		.expand-button::before {
			content: '';
			width: 12px;
			height: 12px;
			border: 2px solid #666;
			transition: all 0.3s;
		}

		.expand-button.expanded::before {
			width: 10px;
			height: 10px;
			border: 2px solid #666;
		}

		/* 全屏状态的浮窗样式 */
		.floating-box.expanded {
			left: 0;
			width: 100% !important; /* 强制宽度100% */
		}

		/* 添加新样式 */
		#image {
			width: 100%;
			height: 100%;
			outline: none;
		}

		#image:focus {
			box-shadow: 0 0 5px 2px rgba(0, 123, 255, 0.5); /* 蓝色阴影表示聚焦状态 */
		}

		/* 当用户点击时的活跃状态 */
		#image:active {
			box-shadow: 0 0 8px 3px rgba(0, 123, 255, 0.7);
		}
		</style>

	</head>

	<body style="overflow-x: auto;overflow-y: auto;">

		<div class="container">

			<div class="row">

				<div id="uppar" style="">

					
<!-- 浮动框 -->
<div id="overlay" class="overlay"></div>
<div id="floatingBox" class="floating-box">
    <div class="floating-box-header">
        <h3 class="floating-box-title">实验记录</h3>
        <div>
            <button id="expandButton" class="expand-button" onclick="toggleExpand()"></button>
            <button class="close-button" onclick="toggleFloatingBox()"></button>
        </div>
    </div>
    <div id="resizeHandle" class="resize-handle"></div>
    <div class="floating-box-content">
        <!-- 可编辑的 HTML 文件 -->
        <iframe src="/labhtml/html/remote/editable.html" class="editor" id="editor"></iframe>
    </div>
</div>

					<div id="leftpar" style=" width: 37%; display: inline-block;  float: left; ">

						<iframe style="        width: 100%;  border: medium none; " src="" id="neirongframe"></iframe>
					</div>
					<div id="rightpar" style="  margin-left: 10px ; display: inline-block; border: solid 2px;  float: left; background: white;">

						<div style="    height: 30px;  font-weight: bold; color: red;font-size: 20px;   " id="shijian">

							<span style="float: right;" class="countdownTime" id="timeshow"></span>


		<span style="float: left; font-size: 15px; margin-top:5px;  color:gray;"   >项目名称:<span id="pppname"></span></span>



						</div>

						<input type="hidden" id="id" name="id" value="" />
						<input type="hidden" id="userCode" value="默认值" />

<input type="hidden" id="openId" value="默认值" />
<input type="hidden" id="userId" value="默认值" />
<input type="hidden" id="userName" value="默认值" />

<input type="hidden" id="projectName" value="默认值" />

<input type="hidden" id="remoteUrl" value="默认值" />

<input type="hidden" id="messageurl" value="默认值" />

<input type="hidden" id="dianyuanip" value="默认值" />



						<div id="imgDiv" >
						<!--	<img src="html/remote/img/a.png" id="image" width="950px" height="600px">-->

<div id="remote-tip" style="position: absolute; top: 40px; right: 20px; background-color: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; z-index: 1000; font-size: 14px;">
    点击远程窗口激活键盘输入 <span style="cursor: pointer; margin-left: 5px;" onclick="this.parentNode.style.display='none'">✕</span>
</div>

<iframe id="image" src="" tabindex="0" frameborder="0"></iframe>





						</div>

						<input id="widthId" type="hidden" value="950" />
						<input id="heightId" type="hidden" value="600" />
						<input id="wb" type="hidden" value="1440" />
						<input id="hb" type="hidden" value="900" />


<!--
	<div id="chongxinlainjie" style="height: 80px; ">

						<div id="ccll" style=" margin-left: 5px;  height: 80px;  display: inline-block;  float: left;">

								<button style=" margin-left: 10px; margin-top: 20px;" onclick="chongxinlainjie()" type="button" class="btn btn-info">重连</button>


</div>



</div>-->




						<div id="divtwo" style="height: 80px;  ">

							<div id="fuweifw" style=" margin-left: 5px;  height: 80px;  display: inline-block; float: left;">

								<button style=" margin-left: 10px; margin-top: 20px;" onclick="fuwei()" type="button" class="btn btn-info">复位</button>
							</div>


<div id="openpower" style=" margin-left: 5px;  height: 80px;  display: inline-block;   float: left;">

								<button style=" margin-left: 10px; margin-top: 20px;" onclick="openpower()" type="button" class="btn btn-info">开电</button>
							</div>




<div id="closepower" style=" margin-left: 5px;  height: 80px;  display: inline-block;   float: left;">
	<button style=" margin-left: 10px; margin-top: 20px;" onclick="closepower()" type="button" class="btn btn-info">关电</button>


							</div>






<div id="jietu" style=" margin-left: 5px;  height: 80px;  display: inline-block;  float: left;">

								<!-- <button style=" margin-left: 10px; margin-top: 20px;" onclick="closepower()" type="button" class="btn btn-info">关电</button> -->


								
								<!-- <button id="recordScreen" style=" margin-left: 10px; margin-top: 20px;" onclick="jietu()" type="button" class="btn btn-info">截图</button>

								<button id="recordButton" style=" margin-left: 10px; margin-top: 20px;" onclick="function record_screen() {

									// 获取按钮元素
    const recordButton = document.getElementById('recordButton');

    // 禁用按钮并设置计时器
    recordButton.disabled = true;
    setTimeout(() => {
        recordButton.disabled = false; // 10秒后重新启用按钮
    }, 10000);

									// 定义要发送的参数

                                     const cookieString = document.cookie;  // 获取浏览器中的 cookies 字符串

                                     // 使用正则表达式匹配第一个 'userId' 的值
                                     const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
									 const lastDirectoryMatch = cookieString.match(/(?:^|;\s*)lastDirectory=([^;]*)/);

                                     var stu_id = match[1];
									 var program = lastDirectoryMatch ? lastDirectoryMatch[1] : '';  // 直接使用 'lastDirectory' 的值，无需解码


									 var params =new URLSearchParams();
									 params.append('stu_id',stu_id)
									 params.append('program',program)
									 fetch('http://sp.penevision.com:85/remote_server/video/record',
										{
											method: 'POST',
											headers:{
												'Content-Type': 'application/x-www-form-urlencoded', // 使用表单数据的编码类型
											},
											body: params.toString(),
										})
										.then(
											response =>{if(!response.ok)
											{
												throw new Error('Network response was not work');

											}
										     return response.text();
											}
										).then(data=>{
											console.log('请求成功'+data)
										})
										.catch(error =>{
											console.log('请求失败')
										})

								}
								record_screen()" type="button" class="btn btn-info">录屏</button> -->
							<!-- hh -->
								<button style=" margin-left: 10px; margin-top: 20px;" onclick="function createmydir() {
									// 定义要发送的参数

									const cookieString = document.cookie;  // 获取浏览器中的 cookies 字符串

								   // 使用正则表达式匹配第一个 'userId' 的值
							   const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
							   var targetIp = sessionStorage.getItem('stationIp');  // 获取存储的目标IP地址
                         var stu_id = match[1];  // 示例用户名
						 var projectName = decodeURIComponent(document.cookie.replace(/(?:(?:^|.*;\s*)projectName\s*=\s*([^;]*).*$)|^.*$/, '$1'));
						//  var projectPath ='F:\\Keil C51-EL-EMCU-IV\\实验指导书';

// 通过 GET 请求获取 JSON 数据
fetch(BASE_URL+ 'remote_server/shiyan?target='+encodeURIComponent(targetIp))
  .then(response => response.json())  // 解析 JSON 文件
  .then(data => {

let projectPath = data.hasOwnProperty(projectName) ? data[projectName] : 'F:\\Keil C51-EL-EMCU-IV\\实验指导书';  // 如果没有找到，使用默认路径


	
	// 提取路径中的最后一个目录层级
            var lastDirectory = projectPath.split(/[\\\/]/).pop();  // 使用正则匹配 \\ 或 /
            console.log('最后一个目录层级:', lastDirectory);  // 输出最后一个目录层级

            // 将最后一个目录层级存储到 cookie 中
document.cookie = 'lastDirectory=' + lastDirectory + '; path=/; expires=' + new Date(Date.now() +  2 * 60 * 60 * 1000).toUTCString();



	// 构建 URL，包含查询参数
   var url =BASE_URL+ 'remote_server/checkDir?stu_id=' + encodeURIComponent(stu_id) + '&dir=' + encodeURIComponent(lastDirectory)+'&target='+encodeURIComponent(targetIp);

//    -
// 发送请求检查历史环境是否存在
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data === true) {
                            // 如果历史环境存在，显示弹框询问是否创建新环境
							showDialog(() => createHistoryEnvironment(stu_id, lastDirectory), () => {
								createNewEnvironment(stu_id, projectPath);
                            });
                        } else {
                            // 如果历史环境不存在，直接创建新环境
                            createNewEnvironment(stu_id, projectPath);
                        }
                    })
                    .catch(error => {
                        console.error('检查历史环境失败:', error);
                        alert('检查历史环境失败: ' + error.message);
                    });
            })
            .catch(error => {
                console.error('获取 JSON 数据失败,创建一个空环境', error);
				createNewEnvironment(stu_id, projectPath);
            });

							   }
							   createmydir()" type="button" class="btn btn-info">创建实验环境</button>


							   <button style=" margin-left: 10px; margin-top: 20px;" onclick="toggleFloatingBox()" type="button" class="btn btn-info">实验记录</button>
							   <button style=" margin-left: 10px; margin-top: 20px;" onclick="deletemydir()" type="button" class="btn btn-info">备份</button>
</div>











<div id="cedianya" style=" margin-left: 5px;  height: 80px;  display: none;  float: left;">

								<button style=" margin-left: 10px; margin-top: 20px;" onclick="cedianya()" type="button" class="btn btn-info">电压检测</button>


</div>





<div id="maichong" style=" margin-left: 5px;  height: 80px;  display: inline-block; float: left;">	</div>



<div id="kebiandianya" style=" margin-left: 5px;  height: 80px;  display: inline-block; float: left;">


<div style="display : inline-block ;  height: 80px; ">




</div>


	 <div id="yibiao2" style="display : inline-block ;height:80px; width:80px;">



	 </div>




</div>





				 <div id="kebiandianzu1" style="height:80px; margin-left: 10px;  float: left;">

				<div style="margin-top:10px;">

				<div style="height: 30px;">
				<span>量程 100Ω-1.5KΩ</span>


	</div>

		<div style="height: 30px;">

<span> 阻值   <b id="zuzhixianshi1">100Ω</b></span>
	<input name="zuzhitype1" id="zuzhitype1"  type="hidden"     value="0"  />


	<input name="dianzu1"  id="dianzu1"   type="hidden"     value="1" type="number" min="1" max="15"    />


<button type="button" style="width: 25px; height: 25px  ;"  class="" onclick="jiadianzu1()" > +</button>
<button type="button"  style="width: 25px; height: 25px  ;"   class="" onclick="jiandianzu1()" > -</button>

	</div>

		</div>

						</div>





				 <div id="kebiandianzu2" style="height:80px;  margin-left: 10px; float: left;">

				<div style="margin-top:10px;">

				<div style="height: 30px;">
				<span>量程 1KΩ-15KΩ</span>


	</div>

		<div style="height: 30px;">

<span> 阻值   <b id="zuzhixianshi2">1KΩ</b></span>
	<input name="zuzhitype2" id="zuzhitype2"  type="hidden"     value="1"  />


	<input name="dianzu2"  id="dianzu2"   type="hidden"     value="1" type="number" min="1" max="15"    />


<button type="button" style="width: 25px; height: 25px  ;"  class="" onclick="jiadianzu2()" > +</button>
<button type="button"  style="width: 25px; height: 25px  ;"   class="" onclick="jiandianzu2()" > -</button>

	</div>

		</div>

						</div>











	 <div id="kebiandianzu3" style="height:80px;  margin-left: 10px; float: left;">

				<div style="margin-top:10px;">

				<div style="height: 30px;">
				<span>量程 10KΩ-150KΩ</span>


	</div>

		<div style="height: 30px;">

<span> 阻值   <b id="zuzhixianshi3">10KΩ</b></span>
	<input name="zuzhitype3" id="zuzhitype3"  type="hidden"     value="2"  />


	<input name="dianzu3"  id="dianzu3"   type="hidden"     value="1" type="number" min="1" max="15"    />


<button type="button" style="width: 25px; height: 25px  ;"  class="" onclick="jiadianzu3()" > +</button>
<button type="button"  style="width: 25px; height: 25px  ;"   class="" onclick="jiandianzu3()" > -</button>

	</div>

		</div>

						</div>





	 <div id="kebiandianzu4" style="height:80px;  margin-left: 10px; float: left;">

				<div style="margin-top:10px;">

				<div style="height: 30px;">
				<span>量程 100KΩ-1500KΩ</span>


	</div>

		<div style="height: 30px;">

<span> 阻值   <b id="zuzhixianshi4">100KΩ</b></span>
	<input name="zuzhitype4" id="zuzhitype4"  type="hidden"     value="3"  />


	<input name="dianzu4"  id="dianzu4"   type="hidden"     value="1" type="number" min="1" max="15"    />


<button type="button" style="width: 25px; height: 25px  ;"  class="" onclick="jiadianzu4()" > +</button>
<button type="button"  style="width: 25px; height: 25px  ;"   class="" onclick="jiandianzu4()" > -</button>

	</div>

		</div>

						</div>

















						<!--	<div id="caijibx" style="margin-left: 20px; height: 80px;  display: inline-block; width: 10%;  float: left;">

								<button style=" margin-left: 10px; margin-top: 10px;" type="button" class="btn btn-success" onclick="huoqubo()" height: 100px> 采集波形</button>
							</div>-->

							<div id="dianping" style=" margin-left: 20px;  height: 80px;  display: inline-block;   float: left;">

							</div>






							<div id="quanping" style=" margin-left: 5px;  height: 80px;  display: inline-block;  float: right;">

							  <img    style=" cursor: pointer; margin-left: 10px; margin-top: 12px; width: 45px; height: 45px"   src="html/remote/img/quanping.png" onclick="quanping()" ></img>
							</div>



<div id="taolun" style=" margin-left: 5px;   height: 80px;  display: inline-block;   float: right;">

						<!--		<img style=" margin-left: 10px; margin-top: 15px; width: 40px; height: 40px"  src="html/remote/img/message.png"  onclick="taolun()" ></img>
					-->

					<button style=" margin-left: 10px; margin-top: 20px;" onclick="taolun()" type="button" class="btn btn-success">消息</button>


	</div>


<div id="guanbi" style=" margin-left: 5px;   height: 80px;  display: inline-block;   float: right;">

								<button style=" margin-left: 10px; margin-top: 20px;" onclick="guanbi()" type="button" class="btn btn-danger">关闭</button>

	</div>


						</div>

					</div>

				</div>

			</div>
		</div>
		<!-- container -->
		<script>
			var resizefunc = [];
		</script>

		<!-- jQuery  -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.min.js"></script>
		<script src="js/detect.js"></script>
		<script src="js/fastclick.js"></script>
		<script src="js/jquery.slimscroll.js"></script>
		<script src="js/jquery.blockUI.js"></script>
		<script src="js/waves.js"></script>
		<script src="js/wow.min.js"></script>
		<script src="js/jquery.nicescroll.js"></script>
		<script src="js/jquery.scrollTo.min.js"></script>

		<script src="js/jquery.core.js"></script>
		<script src="js/jquery.app.js"></script>
		<script src="plugins/layer/layer.js"></script>

		<script type="text/javascript" src="plugins/multiselect/js/jquery.multi-select.js"></script>
		<script type="text/javascript" src="plugins/jquery-quicksearch/jquery.quicksearch.js"></script>
		<!-- select2 js -->
		<script src="plugins/select2/js/select2.min.js" type="text/javascript"></script>
		<script src="plugins/select2/js/i18n/zh-CN.js"></script>
		<!-- 富文本 -->
		<script src="plugins/summernote/dist/summernote.min.js"></script>
		<script src="plugins/summernote/lang/summernote-zh-CN.js"></script>
		<!--form validation init-->
		<script type="text/javascript" src="plugins/parsleyjs/dist/parsley.min.js"></script>
		<script type="text/javascript" src="plugins/parsleyjs/src/i18n/zh_cn.js"></script>
		<script type="text/javascript" src="plugins/parsleyjs/src/i18n/zh_cn.extra.js"></script>
		<script src="js/global.js"></script>
		<script src="js/mySelectDrop.js"></script>
	<script type="text/javascript" src="/labhtml/html/remote/StatisticalChart/echarts.js"></script>

	    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>


		<script type="text/javascript">

		const BASE_URL = '/';

		var islianjie=false;


		var  pparam;





		$(function() {
				//获取路径



			var clientHeight = parseInt(window.screen.availHeight);

			//	console.log("----" + clientHeight);

			var clientWidth = parseInt(window.screen.width);


	//var clientWidthsss = parseInt(window.screen.availWidth);

	//var clientWidthssaaas = parseInt( document.body.clientWidth);











			//	var ssclientWidth = parseInt(window.screen.availWidth);
			//	var ssclientHeight = parseInt(window.screen.availHeight);

				ssclientWidth = parseInt(clientWidth * 0.6);
				ssclientHeight = parseInt(clientHeight * 0.71);

				$("#widthId").val(ssclientWidth);

				$("#heightId").val(ssclientHeight);

				$("#image").attr("width", ssclientWidth-4 + "px");
				$("#image").attr("height", ssclientHeight + "px");







			//	var yuanchengye=parseInt(clientHeight*0.85);

			var onedivye = parseInt(clientHeight * 0.71);

			//		var alldivye=parseInt(clientHeight*0.8);

			var alldivye = onedivye + 110;

			var divone = document.getElementById("imgDiv");

			divone.style.height = onedivye + "px";

			var rightpar = document.getElementById("rightpar");

			rightpar.style.height = alldivye + "px";


			var leftpar = document.getElementById("leftpar");

			leftpar.style.height = alldivye + "px";


var  mywideth= parseInt( clientWidth*0.37) ;
var  mywideth2=parseInt( clientWidth*0.6);

	leftpar.style.width = mywideth + "px";
rightpar.style.width = mywideth2 + "px";




	var uppartpar = document.getElementById("uppar");

var  mywideth33=mywideth+mywideth2+15;

	uppartpar.style.height = alldivye + "px";

uppartpar.style.width = mywideth33 + "px";



			var neirongfr = document.getElementById("neirongframe");

			neirongfr.style.height = alldivye + "px";














				var yuyueid = $.getQueryString("yuyueid");
				var id = yuyueid;

				if(id != null) {
					//	var id=arr[1];
					$("#id").val(id)
					$.ajax({
						type: "get",
						url: "/hrbust_lab_platform/remoteorder/queryCurUrlByKey?id=" + id,
						async: true,
						success: function(data) {
							if(data.status == 200) {
								if(data.remoteUrl != null) {
									$("#userCode").val(data.user.userCode);



								var  openId=	data.queryByKey.openId;

								var  projectName=	data.queryByKey.projectName;
								// 将这个放置到cookie当中
								document.cookie = "projectName=" + encodeURIComponent(data.queryByKey.projectName) + "; path=/; expires=" + new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();



									var  userId=	data.queryByKey.userId;
									var  userName=	data.queryByKey.userName;
										$("#openId").val(openId);
											$("#userId").val(userId);
												$("#userName").val(userName);

										$("#projectName").val(projectName);

									document.getElementById("pppname").innerHTML=projectName;




									//	target = "ws://************:8085/hrbust_lab_platform/tentacle/************";

									//	target = "ws://" + data.remoteUrl + "/remoteSocket"

									//	var remoteIp = id + "_" + clientWidth + "_" + clientHeight + "_" + data.user.userCode;
									//	buildConnection(remoteIp)

									//	buildConnection();

									setTishi(data.endTime);


var remoteUrl=data.remoteUrl;


	$("#remoteUrl").val(remoteUrl);



var messageurl=data.messageurl;


	$("#messageurl").val(messageurl);



	var remoteUrl2=data.remoteUrl2;

	var remotema=data.remotema;

	var param1=data.param1;


	var nowparam=data.stationIp;
	// 被控电脑的ip地址
	console.log(nowparam);

	sessionStorage.setItem('stationIp', nowparam);					



//建立连接

buildConnection(nowparam,remoteUrl2,remotema,param1);


pparam=nowparam;

		//判断链接
	//	var myTarget = setTimeOut(myDo, 5000);




								}
							} else {
								myalert(data.msg)

								var divtwo = document.getElementById("divtwo");

								divtwo.innerHTML = "";

							}
						}

					});
				}
			})




function myDo(){


	console.log("111");

	Tentacle.disconnect();




}



function chongxinlainjie(){


		//Tentacle.disconnect();
	$("#image").attr("src", "");
//$("#image").attr("src", "html/remote/img/a.png")

	islianjie=false;


		setTimeout(function() {
				Tentacle.connect(pparam);
			}, 3000);



}







			function setClose() {



					//$("#image").attr("src", "html/remote/img/b.png")

//Tentacle.disconnect();


$("#image").attr("src", "");



				var divtwo = document.getElementById("divtwo");

				divtwo.innerHTML = "";

			}
		</script>

		<script>





		 var gaugetw = document.getElementById('yibiao2');
    var myChartyibiao2 = echarts.init(gaugetw);



    var optionyb2 = {
        animation: false,

  series: [{
    name: "Indicator",
    type: "gauge",

         min: 0,
      max: 5,
       splitNumber:5,//仪表盘刻度的分割段数。
        splitLine: {
      length: 3,
        distance: 0
    },
     detail: {
      formatter: "电压{value}V",

       show: true,
      offsetCenter: ["0", "95%"],

     fontSize: 12



    },


         axisLine:{
            show:true,//是否显示仪表盘轴线。

                lineStyle:{
                           color: [ [1, "red"]],
                           width:1
                        }


      },


    data: [{
      value: 0




    }],
    axisLabel: {
      show: true,
       distance: 3

    },
     axisTick: {
      show: false
    }






  }]
    };







    myChartyibiao2.setOption(optionyb2);


			 var { width, height } = getComputedStyle(gaugetw);
    // 计算出中心点位置
    var x = parseInt(width.slice(0, -2), 0) / 2;
    var y = parseInt(height.slice(0, -2), 0) / 2;


	    // 这里使用 zrender 的事件监听可以监听到画布的所有鼠标事件.
    myChartyibiao2._zr.on('mousedown', function(event) {
        changeValue(event);

    });



















function buildConnection(param,remoteUrl2,remotema,param1){
    if(!remotema){
        alert("该工位无法远程，请联系管理员");
        return;
    }

    const ipAddress = param;
    const userIndex = ipAddress.lastIndexOf('.') + 1;
    const name = 'user'+ipAddress.substring(userIndex);
    const params = Math.floor(Math.random() * 900000) + 100000;

    const url = 'https://'+remoteUrl2+'/guacamole/#/client/'+remotema+'?'+param1+'&params='+params;

    console.log(url);
    
    // 获取 iframe 元素
    const iframe = document.getElementById('image');
    
    // 设置 iframe 属性
    iframe.setAttribute('allow', 'keyboard *');  // 允许键盘输入
    iframe.setAttribute('allowfullscreen', 'true');
    iframe.setAttribute('allowtransparency', 'true');
    iframe.setAttribute('scrolling', 'no');
    iframe.sandbox = "allow-same-origin allow-scripts allow-popups allow-forms allow-modals";  // 添加必要的权限
    
    // 添加焦点事件处理
    iframe.addEventListener('load', function() {
        // 立即聚焦iframe
        iframe.focus();
        iframe.contentWindow.focus();
        
        // 添加失焦时自动重新聚焦
        iframe.contentWindow.addEventListener('blur', function() {
            setTimeout(function() {
                iframe.contentWindow.focus();
            }, 10); // 短暂延迟后重新聚焦
        });
    });
    
    // 添加键盘事件监听，使用事件捕获模式确保iframe优先接收键盘事件
    document.addEventListener('keydown', function(e) {
        // 如果iframe已加载且有焦点
        if (iframe.contentWindow && document.activeElement === iframe) {
            try {
                // 尝试直接调用Guacamole的键盘处理函数
                if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
                    iframe.contentWindow.guac.keyboard.press(e.keyCode);
                    // 阻止事件冒泡以避免重复处理
                    e.preventDefault();
                    e.stopPropagation();
                }
            } catch (err) {
                console.log('无法直接调用Guacamole键盘处理:', err);
            }
        } else {
            // 如果iframe没有焦点，将焦点给iframe
            iframe.focus();
            if (iframe.contentWindow) {
                iframe.contentWindow.focus();
            }
        }
    }, true); // true表示在捕获阶段处理事件
    
    document.addEventListener('keyup', function(e) {
        if (iframe.contentWindow && document.activeElement === iframe) {
            try {
                if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
                    iframe.contentWindow.guac.keyboard.release(e.keyCode);
                    // 阻止事件冒泡以避免重复处理
                    e.preventDefault();
                    e.stopPropagation();
                }
            } catch (err) {
                console.log('无法直接调用Guacamole键盘处理:', err);
            }
        }
    }, true); // true表示在捕获阶段处理事件
    
    // 添加输入事件监听
    document.addEventListener('input', function(e) {
        if (iframe.contentWindow) {
            try {
                if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
                    iframe.contentWindow.guac.keyboard.type(e.data);
                }
            } catch (err) {
                console.log('无法直接调用Guacamole输入处理:', err);
            }
        }
    });
    
    // 设置 src
    iframe.src = url;
    
    // 添加点击事件，确保iframe获得焦点
    iframe.addEventListener('click', function() {
        this.focus();
        if (this.contentWindow) {
            this.contentWindow.focus();
        }
    });
    
    // 在文档点击时检查是否应该将焦点重定向到iframe
    document.addEventListener('click', function(e) {
        // 如果点击的是iframe或其子元素，确保iframe获得焦点
        if (e.target === iframe || iframe.contains(e.target)) {
            iframe.focus();
            if (iframe.contentWindow) {
                iframe.contentWindow.focus();
            }
        }
    });
}





		</script>

		<script>
			var yuyueid = $.getQueryString("yuyueid");

			var stationId = $.getQueryString("stationId");

			var quanjusybh = $.getQueryString("quanjusybh");

		var quanjuboxbh = $.getQueryString("quanjuboxbh");



			var yuanstepid = $.getQueryString("quanjuboxingResultstep");

			var stustepid = $.getQueryString("stustepid");

			var sort = $.getQueryString("sort");
			var projectId = $.getQueryString("projectId");

			var enddate = $.getQueryString("enddate");

			var isdianping = $.getQueryString("isdianping");

			var isboxing = $.getQueryString("isboxing");
var dianip = $.getQueryString("dianip");

	 $("#dianyuanip").val(dianip);





			var mstime;

			var startTime;

			var count = 0;

			var timecounter;

			var timeshow = document.getElementById("timeshow");

			var namelist = [];

			var zhuangzhiip;

			var duojieCode;

			var gaosort = 1;

			var disort = 1;








			$(function() {

				// 	yuanchengzhuomian();

				//var   contentttt=	document.getElementById("myframe");

				if(isboxing == 0) {

				/*	var caijibxdiv = document.getElementById("caijibx");

					caijibxdiv.style.display = "none";*/

				} else {

				}



console.log(quanjusybh);

    	if(quanjusybh==''){

       	         		var kebiandianyadiv = document.getElementById("kebiandianya");

					kebiandianyadiv.style.display = "none";

       	         		var kebiandianzudiv = document.getElementById("kebiandianzu1");

					kebiandianzudiv.style.display = "none";




	var kebiandian2zudiv = document.getElementById("kebiandianzu2");

					kebiandian2zudiv.style.display = "none";


	var kebiandian3zudiv = document.getElementById("kebiandianzu3");

					kebiandian3zudiv.style.display = "none";



	var kebiandian4zudiv = document.getElementById("kebiandianzu4");

					kebiandian4zudiv.style.display = "none";
       	         	}


else{









				if(isdianping == 0) {


					var dianpingdiv = document.getElementById("dianping");

					dianpingdiv.style.display = "none";


	var maichongdiv = document.getElementById("maichong");

					maichongdiv.style.display = "none";

	var kebiandianyadiv = document.getElementById("kebiandianya");

					kebiandianyadiv.style.display = "none";



	var kebiandianzudiv = document.getElementById("kebiandianzu1");

					kebiandianzudiv.style.display = "none";




	var kebiandian2zudiv = document.getElementById("kebiandianzu2");

					kebiandian2zudiv.style.display = "none";


	var kebiandian3zudiv = document.getElementById("kebiandianzu3");

					kebiandian3zudiv.style.display = "none";



	var kebiandian4zudiv = document.getElementById("kebiandianzu4");

					kebiandian4zudiv.style.display = "none";




				} else {



					$.ajax({
						type: "get",
						dataType: "json",
						url: "/hrbust_lab_platform/remoteduizhao/selectGaoDiList",
						data: {
							'sybh': quanjusybh
						},
						success: function(result) {
							//  console.log(result);//打印服务端返回的数据(调试用)
							if(result.status == 200) {

								var str = "";

								var linelist = result.result;



								if(linelist != null && linelist.length != 0) {

									//	 str+='<div  style="font-size: 20px;"   >';

									str += '<table style="margin-top: 5px;" >';

									str += '<tr><td>输入点</td>';

									$.each(linelist, function(v, it) {

										var name = it.name;



										//	   	     str+= name;

										namelist.push(name);

										str += '<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + name + '</td>';


									});

									str += '</tr>';

									str += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;  低</td>';

									$.each(linelist, function(v, it) {

									var name = it.name;

										var id1 = name+ "gao";
										var id2 = name + "di";


										namelist.push(name);

										str += '<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;       	  <input type="radio"  style="width: 15px;height: 15px;"   onchange="gaibiandianping(' + v + ',2,\'' + name + '\')"   name="' + name + '" id="' + id2 + '"   value="2"        checked="checked"   /></td>';


									});

									str += '</tr>';

									str += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;  高</td>';

									$.each(linelist, function(v, it) {

									var name = it.name;

										var id1 = name+ "gao";
										var id2 = name + "di";



										namelist.push(name);

										str += '<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;	<input type="radio"      onchange="gaibiandianping(' + v + ',1,\'' + name + '\')"       name="' + name + '"  id="' + id1 + '"   value="1"   style="width: 15px;height: 15px;"      /></td>';


									});

									str += '</tr>';
									str += '</table>';

									//    str+='</div>';

getDianPingList();


								}

								else{


						var dianpingdiv = document.getElementById("dianping");

					dianpingdiv.style.display = "none";

			}




								var allT = document.getElementById("dianping");

								allT.innerHTML = str;



							}
						},
						error: function() {
							myalert("查询参考线失败！");
						}
					});

















						$.ajax({
						type: "get",
						dataType: "json",
						url: "/hrbust_lab_platform/remoteduizhao/selectDMCList",
						data: {
							'sybh': quanjusybh
						},
						success: function(result) {
							//  console.log(result);//打印服务端返回的数据(调试用)
							if(result.status == 200) {


								var str = "";

								var linelist = result.result;

var zhengmaichong=result.extra.zhengmaichong;
var fumaichong=result.extra.fumaichong;


	if(zhengmaichong== true &&fumaichong== false) {





str+='<button style=" margin-left: 10px; margin-top: 20px;" onclick="zhengmaichong()" type="button" class="btn btn-info">正脉冲</button>';






								}


else  if(zhengmaichong== true &&fumaichong== true) {





str+='<button style=" margin-left: 10px; margin-top: 20px;" onclick="zhengmaichong()" type="button" class="btn btn-info">正脉冲</button>';

	str+='<button style=" margin-left: 10px; margin-top: 20px;" onclick="fumaichong()" type="button" class="btn btn-info">负脉冲</button>';




								}


				else  if(zhengmaichong== false &&fumaichong== true) {





str+='<button style=" margin-left: 10px; margin-top: 20px;" onclick="fumaichong()" type="button" class="btn btn-info">负脉冲</button>';




								}
									else{



	var maichongdiv = document.getElementById("maichong");

					maichongdiv.style.display = "none";


			}


								var allT = document.getElementById("maichong");

								allT.innerHTML = str;

							}
						},
						error: function() {
							myalert("查询参考线失败！");
						}
					});







						$.ajax({
						type: "get",
						dataType: "json",
						url: "/hrbust_lab_platform/remoteduizhao/selectKBDZList",
						data: {
							'sybh': quanjusybh
						},
						success: function(result) {
							//  console.log(result);//打印服务端返回的数据(调试用)
							if(result.status == 200) {


								var str = "";

								var linelist = result.result;

var dianzuone=result.extra.dianzuone;
var dianzutwo=result.extra.dianzutwo;
var dianzuthree=result.extra.dianzuthree;

var dianzufour=result.extra.dianzufour;



	if(dianzuone== true) {



	var kebiandianzudiv = document.getElementById("kebiandianzu1");

					kebiandianzudiv.style.display = "block";

}

	else{

	var kebiandianzudiv = document.getElementById("kebiandianzu1");

					kebiandianzudiv.style.display = "none";

	}

	if(dianzutwo== true) {



	var kebiandian2zudiv = document.getElementById("kebiandianzu2");

					kebiandian2zudiv.style.display = "block";

}

	else{

	var kebiandian2zudiv = document.getElementById("kebiandianzu2");

					kebiandian2zudiv.style.display = "none";

	}


	if(dianzuthree== true) {



	var kebiandian3zudiv = document.getElementById("kebiandianzu3");

					kebiandian3zudiv.style.display = "block";

}

	else{

	var kebiandian3zudiv = document.getElementById("kebiandianzu3");

					kebiandian3zudiv.style.display = "none";

	}



	if(dianzufour== true) {



	var kebiandian4zudiv = document.getElementById("kebiandianzu4");

					kebiandian4zudiv.style.display = "block";

}

	else{

	var kebiandian4zudiv = document.getElementById("kebiandianzu4");

					kebiandian4zudiv.style.display = "none";

	}








							}
						},
						error: function() {
							myalert("查询参考线失败！");
						}
					});














			$.ajax({
						type: "get",
						dataType: "json",
						url: "/hrbust_lab_platform/remoteduizhao/selectKBDYList",
						data: {
							'sybh': quanjusybh
						},
						success: function(result) {
							//  console.log(result);//打印服务端返回的数据(调试用)
							if(result.status == 200) {


								var str = "";

								var linelist = result.result;



								if(linelist != null && linelist.length != 0) {






			kebiandianya(0);






								}
									else{



	var dianyadiv = document.getElementById("kebiandianya");

					dianyadiv.style.display = "none";


			}


							//	var allT = document.getElementById("kebiandianya");

							//	allT.innerHTML = str;

							}
						},
						error: function() {
							myalert("查询参考线失败！");
						}
					});




















				}



}




				$.ajax({
					url: baseurl + 'labStudyFile/selectPdfByprojecctId',
					data: {
						projectId: projectId

					},
					type: 'post',

					success: function(res) {

						if(res.status == 200) {

							var pdfurl = res.msg;

							var pdfurl = '/hrbust_lab_platform/html/html5-online-pdf-viewer/web/viewer.html?file=' + pdfurl;

							var dianpingtttt = document.getElementById("neirongframe");

							dianpingtttt.src = pdfurl;

							//lab.openView('指导书','/hrbust_lab_platform/html/html5-online-pdf-viewer/web/viewer.html?file='+pdfurl,'100%','100%');

						} else {

							lab.error(res.msg);

						}
					},
					error: function(xhr, textStatus) {
						lab.error('真悲剧，后台抛出异常了');
					}
				});

			});



function  getMaiChongList(){






					$.ajax({
						url: baseurl + 'remoteduojo/selectMaiChongList',
						data: {
							stationId: stationId,
							sybh: quanjusybh

						},
						type: 'post',

						success: function(res) {

							if(res.status == 200) {

								var result = res.result;

								zhuangzhiip = result.zhuangzhiip;

								// 	console.log(zhuangzhiip);
								var list = result.list;

								//  	console.log(list);

								if(list.length > 1) {

									var listgao = list[0];

									duojieCode = listgao.duojieCode.split(";");

									gaosort = listgao.sort;

									var listdi = list[1];

									disort = listdi.sort;

									// 	console.log(duojieCode);

								}

							} else {

								lab.error(res.msg);

							}
						},
						error: function(xhr, textStatus) {
							lab.error('真悲剧，后台抛出异常了');
						}
					});















}





function cedianya(){

		var dianayurl = "/labhtml/html/remote/StatisticalChart/jiancedianya.html?quanjuboxbh=" + quanjuboxbh + "&quanjusybh=" + quanjusybh + "&stationId=" + stationId;

				lab.openView('电压采集', dianayurl, '100%', '100%');




}







function getDianPingList(){






						$.ajax({
						url: baseurl + 'remoteduojo/selectDianpingList',
						data: {
							stationId: stationId,
							sybh: quanjusybh

						},
						type: 'post',

						success: function(res) {

							if(res.status == 200) {

								var result = res.result;

								zhuangzhiip = result.zhuangzhiip;

								// 	console.log(zhuangzhiip);
								var list = result.list;

								//  	console.log(list);

								if(list.length > 1) {

									var listgao = list[0];

									duojieCode = listgao.duojieCode.split(";");

									gaosort = listgao.sort;

									var listdi = list[1];

									disort = listdi.sort;

									// 	console.log(duojieCode);

								}

							} else {

								lab.error(res.msg);

							}
						},
						error: function(xhr, textStatus) {
							lab.error('真悲剧，后台抛出异常了');
						}
					});






}















  function changeValue(event) {
        var x2 = event.offsetX;
        var y2 = event.offsetY;
        // 当前点击位置的角度.
        var currentAngle = Math.atan2(y - y2, x - x2) * 180 / Math.PI;




      currentAngle=currentAngle+45;


      if(currentAngle<0){

      	currentAngle=currentAngle+360;
      }





        let value=0;


        if(0<=currentAngle&&currentAngle<=27){

        	value=0;
        }

        else  if(27<currentAngle&&currentAngle<=81){


        		value=1;
        }
        else  if(81<currentAngle&&currentAngle<=135){

        	value=2;

        }
        else  if(135<currentAngle&&currentAngle<=189){

        	value=3;

        }
        else  if(189<currentAngle&&currentAngle<=243){

        	value=4;

        }
        else  if(243<currentAngle&&currentAngle<=315){

        	value=5;

        }
     else  if(315<currentAngle&&currentAngle<=360){

        	value=0;

        }



        optionyb2.series[0].data[0].value = value;



        kebiandianya(value);


        myChartyibiao2.setOption(optionyb2);
    }



			function deletemydir() {
				// 定义要发送的参数

				const cookieString = document.cookie;  // 获取浏览器中的 cookies 字符串

// 使用正则表达式匹配第一个 'userId' 的值
				const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
				const lastDirectoryMatch = cookieString.match(/(?:^|;\s*)lastDirectory=([^;]*)/);

				var stu_id = match[1];  // 示例用户名				
                var program = lastDirectoryMatch ? lastDirectoryMatch[1] : '';  // 直接使用 'lastDirectory' 的值，无需解码


				
				// 构建请求体
				var params = new URLSearchParams();
				params.append('stu_id', stu_id); // 添加 stu_id 参数
				params.append('program',program)


				// 构建请求的 URL
				const url = BASE_URL+ 'remote_server/upload/download-zip?' + params.toString();


				// 发送 POST 请求
				fetch(url, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded', // 使用表单数据的编码类型
					}
				})
						.then(response => {
							if (!response.ok) {
								throw new Error('Network response was not ok');
							}
						    return response.blob();
    })
    .then(blob => {
        // 创建一个链接并触发下载
        const downloadLink = document.createElement('a');
        const url = URL.createObjectURL(blob);  // 创建指向 Blob 的临时 URL
        downloadLink.href = url;
        downloadLink.download = 'downloaded-file.zip';  // 设置下载的文件名，可以动态生成文件名
        document.body.appendChild(downloadLink);
        downloadLink.click();  // 模拟点击下载链接
        document.body.removeChild(downloadLink);  // 下载后移除临时链接
        URL.revokeObjectURL(url);  // 释放临时 URL
    })
    .catch(error => {
        console.error('请求失败:', error);
        // alert('请求失败: ' + error.message);
    });
			}




function kebiandianya(param){



var dianip=$("#dianyuanip").val();


                    $.ajax({
                        async: false,//是否异步请求，true：异步，false：同步； 默认值: true
                        url: baseurl + "remoteduojo/kebiandianya",
                        data:{
                        	param:param,
                        	duojiip:dianip
                        },
                        type: "post",//请求方式
                        dataType: 'json',//返回数据格式
                        success: function (data) {
                            if (data.status == 200) {


                               var result=data.result;

                           if(result.optCode==1){

                           	lab.success("发送成功");

                           }else{

                           	lab.error(result.optMessage);

                           }



                            } else {






                            }
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            lab.error("系统内部错误");
                        }
                    });





}









			function setTishi(time) {

				/*	console.info(time)
					setTimeout(function(){
						setClose();
						alert("远程桌面3分钟后关闭,请提前保存文件")
					}, time*1-3*60000);
					*/

				// leftTime= parseInt( time/1000)  ;

				//	leftTime= parseInt( time/1000)  ;

				// countdown();

				startTime = new Date().getTime()

				mstime = parseInt(time / 1000);

				//   	  mstime是秒数

				if(mstime > 0) {

					var h, m, s;

					var leftTime = mstime;

					if(leftTime >= 0) {
						var min = Math.floor(leftTime % 3600);
						h = Math.floor(leftTime / 3600);

						m = Math.floor(min / 60);
						s = Math.floor(leftTime % 60);

						if(h < 10) {
							h = "0" + h;

						}

						if(m < 10) {
							m = "0" + m;

						}

						if(s < 10) {
							s = "0" + s;

						}

						//	timeshow.innerHTML =  + "时" +  + "分"+  + "秒";
						timeshow.innerHTML = '<span>剩余时间:&nbsp;</span><span>' + h + '</span>:<span>' + m + '</span>:<span>' + s + '</span>';
					}

					timecounter = setTimeout(countshout, 1000);

				}

			}



	function gaibiandianping(sort, type, name) {

	gaibiandianping1(sort, type, name);
}








			function gaibiandianping1(sort, type, name) {

				var duojihao = duojieCode[sort];

				var fanradioid;

				var iokou = 1;

				if(type == 1) {

					iokou = gaosort;

					fanradioid = name + "di";

				} else {

					iokou = disort;

					fanradioid = name + "gao";
				}

				//	console.log(duojihao);

				//		console.log(iokou);

				var layerMsg = layer.msg("正在改变电压", {
					title: false,
					icon: 16 // 加载图标h
				});

				$.ajax({
					type: "POST",
					dataType: "json",
					url: "/hrbust_lab_platform/remoteduojo/changeDianPing",
					data: {
						'duojiip': zhuangzhiip,
						'duojihao': duojihao,
						'duojiio': iokou
					},
					success: function(data) {

						if(data.status == 200) {

							var result = data.result;

							if(result.optCode == 1) {


								layer.close(layerMsg);


									Tentacle._send({
						type: 'command',
						command: 'control',
					});



							} else {

								layer.msg('操作失败', {
									icon: 2,
									time: 2000 //2秒关闭（如果不配置，默认是3秒）
								}, function() {
									//do something
								});

								$('#' + fanradioid).prop('checked', true);

								//  	lab.error(result.optMessage);

							}

						}
					},
					error: function() {

						myalert("发送指令失败，请检查实验装置是否在线！");
					}
				})

			}






	function gaibiandianping2(sort, type, name) {

				sort=sort+1;

if(type==2){

	type=0;
}



				var layerMsg = layer.msg("正在改变电压", {
					title: false,
					icon: 16 // 加载图标h
				});

				$.ajax({
					type: "POST",
					dataType: "json",
					url: "/hrbust_lab_platform/remoteduojo/kaiguanliang",
					data: {
						'duojiip': zhuangzhiip,
						'sort': sort,
						'param': type
					},
					success: function(data) {

						if(data.status == 200) {

							var result = data.result;

							if(result.optCode == 1) {


								layer.close(layerMsg);


									Tentacle._send({
						type: 'command',
						command: 'control',
					});



							} else {

								layer.msg('操作失败', {
									icon: 2,
									time: 2000 //2秒关闭（如果不配置，默认是3秒）
								}, function() {
									//do something
								});

								$('#' + fanradioid).prop('checked', true);

								//  	lab.error(result.optMessage);

							}

						}
					},
					error: function() {

						myalert("发送指令失败，请检查实验装置是否在线！");
					}
				})

			}






			function countshout() {

				count++;

				var offset = new Date().getTime() - (startTime + count * 1000);

				var nextTime = 1000 - offset;

				if(nextTime < 0) {

					nextTime = 0;

				}

				mstime = mstime - 1;

				//console.log("count--"+count);
				//console.log("offset--"+offset);

				//console.log("ms--"+ms);

				//console.log("nextTime--"+nextTime);

				if(mstime <= 0) {

				} else {

					timecounter = setTimeout(countshout, nextTime);

				}

				//获取当前时间

				//   console.log(leftTime);

				var h, m, s;

				var leftTime = mstime;

				//获取截至时间
				if(leftTime >= 0) {
					var min = Math.floor(leftTime % 3600);
					h = Math.floor(leftTime / 3600);

					m = Math.floor(min / 60);
					s = Math.floor(leftTime % 60);

					if(h < 10) {
						h = "0" + h;

					}

					if(m < 10) {
						m = "0" + m;

					}

					if(s < 10) {
						s = "0" + s;

					}

					//	timeshow.innerHTML =  + "时" +  + "分"+  + "秒";
					timeshow.innerHTML = '<span>剩余时间:&nbsp;</span><span>' + h + '</span>:<span>' + m + '</span>:<span>' + s + '</span>';
				}

				if(leftTime == 0) {

					myalert("远程实验已结束");

					setClose();
				};

				if(leftTime == 180) {

					var layerMsg = layer.alert('远程桌面3分钟后关闭,请提前保存文件', {

						icon: 5,

						area: ['350px', '200px'],

						title: "提示"

					});

				};

			}

			function yuanchengzhuomian() {

				var contentttt = document.getElementById("myframe");

				var urlzz = "/labhtml/html/remote/imgClicl2.html?id=" + yuyueid;

				contentttt.src = urlzz;

			}

			function lookZhiDao(projectId) {

				$.ajax({
					url: baseurl + 'labStudyFile/selectPdfByprojecctId',
					data: {
						projectId: projectId

					},
					type: 'post',

					success: function(res) {

						if(res.status == 200) {

							var pdfurl = res.msg;
							lab.openView('指导书', '/hrbust_lab_platform/html/html5-online-pdf-viewer/web/viewer.html?file=' + pdfurl, '100%', '100%');

						} else {

							lab.error(res.msg);

						}
					},
					error: function(xhr, textStatus) {
						lab.error('真悲剧，后台抛出异常了');
					}
				});

			}

			function fuwei() {

				//var dianip = $("#dianyuanip").val();
				var sybh = quanjusybh;

				var layerMsg = layer.msg("正在复位，请等待", {
					title: false,
					icon: 16 // 加载图标h
				});

				$.ajax({
					type: "POST",
					dataType: "json",
					url: baseurl + "/remoteduojo/fuwei",
					data: {
						'stationId': stationId
					},
					success: function(data22) {
						//  console.log(result);//打印服务端返回的数据(调试用)
						if(data22.status == 200) {

							layer.close(layerMsg);

							var result22 = data22.result;

							if(result22 == null) {

								lab.error("复位失败,请检查远程装置上是否有复位点");

							} else {

								if(result22.optCode == 1) {


			Tentacle._send({
						type: 'command',
						command: 'control',
					});





									//  	lab.success(result.optMessage);

								} else {

									lab.error(result22.optMessage);

								}

							}

						}
					},
					error: function() {
						myalert("复位失败，请手动复位！");
					}
				})

			}

			function huoqubo() {

				var boxingurl = "/labhtml/html/remote/experimental/caiji.html?yuanstepid=" + yuanstepid + "&stustepid=" + stustepid + "&sort=" + sort + "&stationId=" + stationId;

				lab.openView('波形采集', boxingurl, '80%', '80%');

			}


function taolun() {


	var openId=	$("#openId").val();
										var userId=	$("#userId").val();

											var userName=		$("#userName").val();
											var projectName=		$("#projectName").val();

						var messageurl=		$("#messageurl").val();


				var nurl = "/labhtml/html/remote/message/studentmessage.html?openId=" + openId + "&myuserId=" + userId + "&userName=" + userName + "&projectName=" + projectName+ "&messageurl=" + messageurl;

				lab.openView('实时讨论', nurl, '85%', '85%');

			}



function  guanbi(){
    try {
        
        // 原有代码继续执行
        const cookieString = document.cookie;  // 获取浏览器中的 cookies 字符串

        // 使用正则表达式匹配第一个 'userId' 的值
        const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);

        if (match) {
            var stu_id = match[1];  // 获取用户ID
        } else {
            console.error('无法获取 userId');
            return;  // 如果没有找到 userId，直接返回
        }

        // 构建请求体
        var params = new URLSearchParams();
        params.append('stu_id', stu_id);  // 添加 stu_id 参数

        // 构建请求的 URL
        const url = '/remote_server/delete';

        // 发送 POST 请求
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded', // 使用表单数据的编码类型
            },
            body: params.toString(),  // 转换为查询字符串格式
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();  // 获取响应的文本数据
            })
            .then(data => {
                console.log('响应数据:', data);  // 打印响应数据
            })
            .catch(error => {
                console.error('请求失败:', error);
            });

    } catch (error) {
        // log.error("系统错误")
        console.log("系统错误")
    }

    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);// 关闭弹出层
//  parent.window.location.reload(); // 刷新父页面
}







			function myalert(msg) {

				var layerMsg = layer.alert(msg, {

					icon: 5,

					area: ['350px', '200px'],

					title: "提示"

				});

			}








        function openpower(){



        	   		var layerMsg = layer.msg("正在打开电源", {
    title: false,
    icon: 16 // 加载图标h
});



        		var dianip=$("#dianyuanip").val();
       $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: baseurl+"/remoteStationInstrument/operationPower",
                    data: {'ip': dianip,"type":1},
                    success: function (data) {
                      //  console.log(result);//打印服务端返回的数据(调试用)
                        if (data.status == 200) {

                   layer.close(layerMsg) ;

                      var result22=data.result;

                           if(result22.optCode==1){


               lab.success("打开电源成功");

               	Tentacle._send({
						type: 'command',
						command: 'control',
					});



                           }else{

                           	lab.error("打开电源失败");


                           }

                        }
                    },
                    error: function () {
                    	          layer.close(layerMsg) ;

                        myalert("打开电源失败，请手动打开电源！");
                    }
                })


        }





        function jietu(){


        	//   var content = document.getElementById('image');

//        // 获取 iframe 元素
// var iframe = document.getElementById('image');

// // 获取 iframe 的内容窗口
// var iframeWindow = iframe.contentWindow;

// // 使用 html2canvas 对 iframe 内容窗口进行截图
// html2canvas(iframeWindow.document.body).then(function(canvas) {
//   // 将 canvas 转换为图片的 data URL
//   var dataURL = canvas.toDataURL();

//   // 创建一个新的窗口并在其中显示图片
//   var newWindow = window.open();
//   newWindow.document.write('<img src="' + dataURL + '"/>');
// });

									// 获取按钮元素
									const recordButton = document.getElementById('recordScreen');

// 禁用按钮并设置计时器
recordButton.disabled = true;
setTimeout(() => {
	recordButton.disabled = false; // 1秒后重新启用按钮
}, 1000);


									// 定义要发送的参数

									const cookieString = document.cookie;  // 获取浏览器中的 cookies 字符串

// 使用正则表达式匹配第一个 'username' 的值
const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
const lastDirectoryMatch = cookieString.match(/(?:^|;\s*)lastDirectory=([^;]*)/);


var program = lastDirectoryMatch ? lastDirectoryMatch[1] : '';  // 直接使用 'lastDirectory' 的值，无需解码



var stu_id = match[1];
var params =new URLSearchParams();
params.append('stu_id',stu_id)
params.append('program',program)
// 需要加上实验名称

fetch('http://sp.penevision.com:85/remote_server/video/screen',
   {
	   method: 'POST',
	   headers:{
		   'Content-Type': 'application/x-www-form-urlencoded', // 使用表单数据的编码类型
	   },
	   body: params.toString(),
   })
   .then(
	   response =>{if(!response.ok)
	   {
		   throw new Error('Network response was not work');

	   }
		return response.text();
	   }
   ).then(data=>{
	   console.log('请求成功'+data)
   })
   .catch(error =>{
	   console.log('请求失败')
   })




        }








            function closepower(){


        	   		var layerMsg = layer.msg("正在关闭电源", {
    title: false,
    icon: 16 // 加载图标h
});








        		var dianip=$("#dianyuanip").val();
       $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: baseurl+"/remoteStationInstrument/operationPower",
                    data: {'ip': dianip,"type":2},
                    success: function (data) {
                      //  console.log(result);//打印服务端返回的数据(调试用)
                        if (data.status == 200) {

                   layer.close(layerMsg) ;

                      var result22=data.result;

                           if(result22.optCode==1){



                          lab.success("关闭电源成功");



               	Tentacle._send({
						type: 'command',
						command: 'control',
					});

                           }else{

                           	lab.error("关闭电源失败");


                           }

                        }
                    },
                    error: function () {
                    	          layer.close(layerMsg) ;

                        myalert("关闭电源失败，请手动打开电源！");
                    }
                })


        }















function zhengmaichong(){




        	   		var layerMsg = layer.msg("正在发送正脉冲", {
    title: false,
    icon: 16 // 加载图标h
});



        		var dianip=$("#dianyuanip").val();
       $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: baseurl+"/remoteduojo/zhengmaichong",
                    data: {'duojiip': dianip},
                    success: function (data) {
                      //  console.log(result);//打印服务端返回的数据(调试用)
                        if (data.status == 200) {

                   layer.close(layerMsg) ;

                      var result22=data.result;

                           if(result22.optCode==1){



                          lab.success("发送成功");



               	Tentacle._send({
						type: 'command',
						command: 'control',
					});

                           }else{

                           	lab.error("发送失败");


                           }

                        }
                    },
                    error: function () {
                    	          layer.close(layerMsg) ;

                        myalert("连接硬件服务失败！");
                    }
                });






}



function fumaichong(){




        	   		var layerMsg = layer.msg("正在发送负脉冲", {
    title: false,
    icon: 16 // 加载图标h
});



        		var dianip=$("#dianyuanip").val();
       $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: baseurl+"/remoteduojo/fumaichong",
                    data: {'duojiip': dianip},
                    success: function (data) {
                      //  console.log(result);//打印服务端返回的数据(调试用)
                        if (data.status == 200) {

                   layer.close(layerMsg) ;

                      var result22=data.result;

                           if(result22.optCode==1){



                          lab.success("发送成功");



               	Tentacle._send({
						type: 'command',
						command: 'control',
					});

                           }else{

                           	lab.error("发送失败");


                           }

                        }
                    },
                    error: function () {
                    	          layer.close(layerMsg) ;

                        myalert("连接硬件服务失败！");
                    }
                });






}





function kebiandianzu(sort){




var zuzhitype=$("#zuzhitype"+sort).val();

var dianzu=$("#dianzu"+sort).val();


		var dianip=$("#dianyuanip").val();

//var zuzhitype1=$("#zuzhitype1").val();

//var dianzu1=$("#dianzu1").val();

                    $.ajax({
                        async: false,//是否异步请求，true：异步，false：同步； 默认值: true
                        url: baseurl + "remoteduojo/kebiandianzu",
                        data:{
                        	one:zuzhitype,
                        	duojiip:dianip,

                        	  	two:dianzu,
                        },
                        type: "post",//请求方式
                        dataType: 'json',//返回数据格式
                        success: function (data) {
                            if (data.status == 200) {


                               var result=data.result;

                           if(result.optCode==1){

                           	lab.success("发送成功");

                           }else{

                           	lab.error(result.optMessage);

                           }



                            } else {






                            }
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            lab.error("系统内部错误");
                        }
                    });





}








  function jiadianzu1(){


var dianzu1=$("#dianzu1").val();

  	dianzu1=Number(dianzu1);


  	if(eval(0)<parseInt(dianzu1)&&parseInt(dianzu1)<eval(15))


{

	console.log(dianzu1);



	  	dianzu1=dianzu1+1;

  	$("#dianzu1").val(dianzu1);

 var  xianshi=dianzu1*100+"Ω";

	  	$("#zuzhixianshi1").html(xianshi);


	  	kebiandianzu(1);


}






  }






 function jiandianzu1 (){



var dianzu1=$("#dianzu1").val();

  	dianzu1=Number(dianzu1);


  	if(eval(1)<parseInt(dianzu1)&&parseInt(dianzu1)<eval(16))


{

	console.log(dianzu1);



	  	dianzu1=dianzu1-1;

  	$("#dianzu1").val(dianzu1);

 var  xianshi=dianzu1*100+"Ω";

	  	$("#zuzhixianshi1").html(xianshi);

	  	kebiandianzu(1);
}








}





  function jiadianzu2(){


var dianzu2=$("#dianzu2").val();

  	dianzu2=Number(dianzu2);


  	if(eval(0)<parseInt(dianzu2)&&parseInt(dianzu2)<eval(15))


{

	console.log(dianzu2);



	  	dianzu2=dianzu2+1;

  	$("#dianzu2").val(dianzu2);

 var  xianshi=dianzu2*1+"KΩ";

	  	$("#zuzhixianshi2").html(xianshi);


		kebiandianzu(2);


}






  }

  function jiandianzu2(){




var dianzu2=$("#dianzu2").val();

  	dianzu2=Number(dianzu2);


    	if(eval(1)<parseInt(dianzu2)&&parseInt(dianzu2)<eval(16))


{



	  	dianzu2=dianzu2-1;

  	$("#dianzu2").val(dianzu2);

 var  xianshi=dianzu2*1+"KΩ";

	  	$("#zuzhixianshi2").html(xianshi);


	kebiandianzu(2);


}


  }



  function jiadianzu3(){


var dianzu3=$("#dianzu3").val();

  	dianzu3=Number(dianzu3);


  	if(eval(0)<parseInt(dianzu3)&&parseInt(dianzu3)<eval(15))


{

	console.log(dianzu3);



	  	dianzu3=dianzu3+1;

  	$("#dianzu3").val(dianzu3);

 var  xianshi=dianzu3*10+"KΩ";

	  	$("#zuzhixianshi3").html(xianshi);


		kebiandianzu(3);


}






  }

  function jiandianzu3(){


var dianzu3=$("#dianzu3").val();

  	dianzu3=Number(dianzu3);


    	if(eval(1)<parseInt(dianzu3)&&parseInt(dianzu3)<eval(16))


{



	  	dianzu3=dianzu3-1;

  	$("#dianzu3").val(dianzu3);

 var  xianshi=dianzu3*10+"KΩ";

	  	$("#zuzhixianshi3").html(xianshi);


	kebiandianzu(3);


}


  }


  function jiadianzu4(){




var dianzu4=$("#dianzu4").val();

  	dianzu4=Number(dianzu4);


  	if(eval(0)<parseInt(dianzu4)&&parseInt(dianzu4)<eval(15))


{



	  	dianzu4=dianzu4+1;

  	$("#dianzu4").val(dianzu4);

 var  xianshi=dianzu4*100+"KΩ";

	  	$("#zuzhixianshi4").html(xianshi);


		kebiandianzu(4);


}






  }

  function jiandianzu4(){



var dianzu4=$("#dianzu4").val();

  	dianzu4=Number(dianzu4);


    	if(eval(1)<parseInt(dianzu4)&&parseInt(dianzu4)<eval(16))


{



	  	dianzu4=dianzu4-1;

  	$("#dianzu4").val(dianzu4);

 var  xianshi=dianzu4*100+"KΩ";

	  	$("#zuzhixianshi4").html(xianshi);


	kebiandianzu(4);


}


  }




function quanping() {

				//var ddd=	document.getElementById("image");
				//	ddd.webkitRequestFullScreen();

				launchFullscreen(document.getElementById("rightpar"));

			}





			function launchFullscreen(element) {

			console.log(element);


				if(element.requestFullscreen) {

							console.log("111");
					element.requestFullscreen()
				} else if(element.mozRequestFullScreen) {

						console.log("222");

					element.webkitRequestFullScreen()
				} else if(element.msRequestFullscreen) {


						console.log("333");

					element.msRequestFullscreen()
				} else if(element.webkitRequestFullscreen) {

						console.log("444");


					element.mozRequestFullScreen()
				}
			}

			//launchFullscreen(document.documentElement) // 整个页面进入全屏
			//launchFullscreen(document.getElementById("id")) //某个元素进入全屏

			function exitFullscreen() {
				if(document.exitFullscreen) {
					document.exitFullscreen()
				} else if(document.msExitFullscreen) {
					document.msExitFullscreen()
				} else if(document.mozCancelFullScreen) {
					document.mozCancelFullScreen()
				} else if(document.webkitExitFullscreen) {
					document.webkitExitFullscreen()
				}
			}
			//exitFullscreen()

			document.addEventListener("fullscreenchange", function(e) {
				if(document.fullscreenElement) {
					//	console.log('进入全屏');

						var clientHeightoo = window.screen.height;
				var clientWidthoo = window.screen.width;




				console.log("clientHeightoo---"+clientHeightoo);

	console.log("clientWidthoo---"+clientWidthoo);

					clientHeight = parseInt(clientHeightoo * 1) - 110;

					clientWidth = parseInt(clientWidthoo * 1);

					var clientHeight2 = clientHeightoo - clientHeight;

					$("#image").attr("width", clientWidth + "px");
					$("#image").attr("height", clientHeight + "px");

					$("#widthId").val(clientWidth);
					$("#heightId").val(clientHeight);

					document.getElementById("imgDiv").style.height = clientHeight + "px";

					document.getElementById("quanping").style.display = "none";

	document.getElementById("taolun").style.display = "none";


document.getElementById("guanbi").style.display = "none";







				} else {
					//	console.log('退出全屏');


					var clientHeight = parseInt(window.screen.availHeight);

var clientWidth = parseInt(window.screen.availWidth);

					clientWidth = parseInt(clientWidth * 0.6);
					clientHeight = parseInt(clientHeight * 0.71);

					$("#image").attr("width", clientWidth-4 + "px");
					$("#image").attr("height", clientHeight + "px");
					$("#widthId").val(clientWidth);
					$("#heightId").val(clientHeight);

					document.getElementById("imgDiv").style.height = clientWidth + "px";
					document.getElementById("imgDiv").style.height = clientHeight + "px";

					document.getElementById("quanping").style.display = "block";
document.getElementById("taolun").style.display = "block";


document.getElementById("guanbi").style.display = "block";






				}
			})



			  // 创建新环境
			  function createNewEnvironment(stu_id, projectPath) {
				
        var url = BASE_URL+'remote_server/createByUserLoginName';

		
        // 构建 POST 请求的请求体
        var body = new URLSearchParams();
        body.append('stu_id', stu_id);
        body.append('dir', projectPath);
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
			body:body.toString()
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应失败');
            }
            return response.text();
        })
        .then(data => {
            console.log('成功:', data);
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
    }

	  // 创建历史环境
	  function createHistoryEnvironment(stu_id, projectPath) {

		
        var url = BASE_URL+ 'remote_server/createBuRecordDir';
        
        // 构建 POST 请求的请求体
        var body = new URLSearchParams();
        body.append('stu_id', stu_id);
        body.append('dir', projectPath);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: body.toString()  // 将请求体转换为 URL 编码格式
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应失败');
            }
            return response.text();  // 返回响应的文本内容
        })
        .then(data => {
            console.log('成功:', data);
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
    }

	    // 切换浮动框的显示状态
		function toggleFloatingBox() {
    const floatingBox = document.getElementById('floatingBox');
    const overlay = document.getElementById('overlay');
    const expandButton = document.getElementById('expandButton');
    
    if (!floatingBox.classList.contains('active')) {
        // 打开浮窗
        floatingBox.classList.add('active');
        overlay.classList.add('active');
    } else {
        // 关闭浮窗 - 同时处理全屏状态
        floatingBox.classList.remove('active');
        overlay.classList.remove('active');
        // 如果是全屏状态，先退出全屏
        if (floatingBox.classList.contains('expanded')) {
            floatingBox.classList.remove('expanded');
            expandButton.classList.remove('expanded');
        }
        floatingBox.style.width = '550px';
    }
}

    // 显示确认弹框
    function showDialog(onConfirm, onCancel) {
        const dialog = document.createElement('div');
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.padding = '20px';
        dialog.style.backgroundColor = 'white';
        dialog.style.border = '1px solid #ccc';
        dialog.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        dialog.style.textAlign = 'center';

        const message = document.createElement('p');
        message.textContent = '检查到历史备份文件，是否创建继续之前的备份文件创建环境？';
        dialog.appendChild(message);

        const confirmButton = document.createElement('button');
        confirmButton.textContent = '确定';
        confirmButton.style.marginRight = '10px';
        confirmButton.onclick = () => {
            document.body.removeChild(dialog);
            onConfirm();
        };
        dialog.appendChild(confirmButton);

        const cancelButton = document.createElement('button');
        cancelButton.textContent = '取消';
        cancelButton.onclick = () => {
            document.body.removeChild(dialog);
            onCancel();
        };
        dialog.appendChild(cancelButton);

        document.body.appendChild(dialog);
    }


	// 拖拽功能
	

		</script>

		<!-- 添加拖拽功能的代码 -->
		<script>
			let isResizing = false;
			let startX;
			let startWidth;

			// 拖拽功能
			document.getElementById('resizeHandle').addEventListener('mousedown', function(e) {
				isResizing = true;
				startX = e.clientX;
				startWidth = parseInt(getComputedStyle(document.getElementById('floatingBox')).width);
				e.stopPropagation();
			});

			document.addEventListener('mousemove', function(e) {
				if (!isResizing) return;
				
				const width = startWidth + (e.clientX - startX);
				const floatingBox = document.getElementById('floatingBox');
				const maxWidth = window.innerWidth * 0.8;
				
				if (width >= 550 && width <= maxWidth) { // 修改最小宽度
					floatingBox.style.width = width + 'px';
				}
			});

			document.addEventListener('mouseup', function() {
				isResizing = false;
			});
		</script>

		<script>
		// 添加全屏切换功能
		function toggleExpand() {
			const floatingBox = document.getElementById('floatingBox');
			const expandButton = document.getElementById('expandButton');
			const isExpanded = floatingBox.classList.contains('expanded');
			
			if (!isExpanded) {
				// 保存当前宽度，以便退出全屏时恢复
				floatingBox.setAttribute('data-original-width', floatingBox.style.width || '550px');
				floatingBox.classList.add('expanded');
				expandButton.classList.add('expanded');
			} else {
				// 恢复原始宽度
				floatingBox.style.width = floatingBox.getAttribute('data-original-width');
				floatingBox.classList.remove('expanded');
				expandButton.classList.remove('expanded');
			}
		}
		</script>

		<script>
		// ... existing code ...
		$(function() {
			// ... existing code ...
			
			// 页面加载完成后自动聚焦iframe
			setTimeout(function() {
				var iframe = document.getElementById('image');
				if (iframe) {
					iframe.focus();
					if (iframe.contentWindow) {
						iframe.contentWindow.focus();
					}
					
					// 3秒后隐藏提示
					setTimeout(function() {
						var tip = document.getElementById('remote-tip');
						if (tip) {
							tip.style.opacity = '0';
							tip.style.transition = 'opacity 1s';
							setTimeout(function() {
								tip.style.display = 'none';
							}, 1000);
						}
					}, 5000);
				}
			}, 2000);
		});
		// ... existing code ...
		</script>
	</body>

</html>
