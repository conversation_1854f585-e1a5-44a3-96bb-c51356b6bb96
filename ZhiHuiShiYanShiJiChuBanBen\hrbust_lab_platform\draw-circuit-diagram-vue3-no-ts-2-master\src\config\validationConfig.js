/**
 * 连接关系校验版本配置
 * 
 * 在这里统一控制整个系统使用的校验版本
 * 只需要修改这一个文件就能切换所有相关功能的版本
 */

// ========================================
// 🔧 版本切换配置 - 只需要修改这里！
// ========================================

/**
 * 连接关系校验版本选择
 *
 * 'v1'   - 原始版本：使用包含匹配，可能存在组件类型混淆
 * 'v1.1' - 智能匹配版本：基于组件库自动匹配，避免组件类型混淆，自动支持新器件
 */
export const VALIDATION_VERSION = 'v1.1'

/**
 * 保存模板时的连接关系版本
 * 
 * 'v1'   - 只保存原始连接关系
 * 'v1.1' - 只保存规范化连接关系  
 * 'both' - 同时保存两个版本（推荐）
 */
export const SAVE_TEMPLATE_VERSION = 'both'

/**
 * 拓扑校验方式选择
 * 
 * true  - 使用拓扑比较（基于组件类型）
 * false - 使用传统连接比较（基于完整连接描述）
 */
export const USE_TOPOLOGY_COMPARISON = true

// ========================================
// 📋 版本信息说明
// ========================================

export const VERSION_INFO = {
  v1: {
    name: 'v1 - 原始版本',
    description: '使用包含匹配进行组件类型规范化',
    pros: [
      '兼容性好',
      '逻辑简单'
    ],
    cons: [
      '可能出现组件类型混淆',
      '例如：无极性电容被识别为极性电容'
    ]
  },
  'v1.1': {
    name: 'v1.1 - 智能匹配版本',
    description: '使用智能匹配算法进行组件类型规范化，基于组件库自动支持新器件',
    pros: [
      '精确识别所有组件类型',
      '避免混淆问题',
      '自动支持新器件',
      '零维护成本',
      '保留所有重要的电路特征'
    ],
    cons: [
      '依赖组件库数据结构'
    ]
  }
}

// ========================================
// 🛠️ 辅助函数
// ========================================

/**
 * 获取当前使用的校验版本
 */
export const getCurrentValidationVersion = () => {
  return VALIDATION_VERSION
}

/**
 * 检查是否使用v1.1版本
 */
export const isUsingV1_1 = () => {
  return VALIDATION_VERSION === 'v1.1'
}

/**
 * 检查是否使用拓扑比较
 */
export const isUsingTopologyComparison = () => {
  return USE_TOPOLOGY_COMPARISON
}

/**
 * 获取保存模板的版本配置
 */
export const getSaveTemplateVersion = () => {
  return SAVE_TEMPLATE_VERSION
}

/**
 * 打印当前配置信息（用于调试）
 */
export const printCurrentConfig = () => {
  console.log('🔧 当前校验配置:')
  console.log(`   校验版本: ${VALIDATION_VERSION}`)
  console.log(`   保存版本: ${SAVE_TEMPLATE_VERSION}`)
  console.log(`   拓扑比较: ${USE_TOPOLOGY_COMPARISON ? '启用' : '禁用'}`)
  console.log(`   版本说明: ${VERSION_INFO[VALIDATION_VERSION]?.description}`)
}

// ========================================
// 📝 使用说明
// ========================================

/*
使用方法：

1. 切换校验版本：
   修改 VALIDATION_VERSION 为 'v1' 或 'v1.1'

2. 切换保存版本：
   修改 SAVE_TEMPLATE_VERSION 为 'v1'、'v1.1' 或 'both'

3. 切换拓扑比较：
   修改 USE_TOPOLOGY_COMPARISON 为 true 或 false

4. 在代码中使用：
   import { isUsingV1_1, isUsingTopologyComparison } from '@/config/validationConfig'
   
   if (isUsingV1_1()) {
     // 使用v1.1版本逻辑
   } else {
     // 使用v1版本逻辑
   }

5. 调试信息：
   import { printCurrentConfig } from '@/config/validationConfig'
   printCurrentConfig() // 打印当前配置
*/
