// ComponentPanel.vue

<template>
  <el-card class="components-panel" shadow="hover">
    <h3 class="panel-title">元器件库</h3>

    <!-- 使用el-row和el-col实现2列布局 -->
    <el-row :gutter="16">
      <!-- 增加gutter值来增大列间距 -->
      <el-col v-for="(item, index) in components" :key="index" :span="12">
        <el-card
          class="component-card"
          shadow="never"
          draggable="true"
          @dragstart="(event) => handleDragStart(event, item)">
          <div class="component-icon" v-html="item.icon"></div>
          <div class="component-label">
            {{ item.label }}
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup name="ComponentPanel">
  import { componentsData } from './data/componentsData'

  const components = componentsData

  // 处理拖拽开始事件
  const handleDragStart = (event, component) => {
    event.dataTransfer.setData('componentData', JSON.stringify(component))
  }
</script>

<style scoped>
  .components-panel {
    width: 220px; /* 增加组件库的宽度 */
    padding: 15px;
  }

  .panel-title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-top: -20px;
    margin-bottom: 10px;
  }

  /* 卡片样式 */
  .component-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 80px; /* 固定宽度 */
    height: 80px; /* 固定高度，保持正方形 */
    font-size: 12px;
    text-align: center;
    border-radius: 8px; /* 轻微的圆角效果 */
    transition:
      box-shadow 0.2s ease,
      transform 0.2s ease;
    border: 1px solid #e0e0e0; /* 增加边框 */
    background-color: #ffffff;
    margin: 5px auto; /* 使卡片在列中居中 */
  }

  .component-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 微调阴影 */
    transform: scale(1.05); /* 鼠标悬浮时稍微放大 */
  }

  /* 元器件图标样式 */
  .component-icon {
    font-size: 20px;
    margin-bottom: 4px;
  }

  /* 元器件标签样式 */
  .component-label {
    font-size: 12px;
    color: #666;
    width: 12ch;
    word-break: break-word;
  }
</style>
