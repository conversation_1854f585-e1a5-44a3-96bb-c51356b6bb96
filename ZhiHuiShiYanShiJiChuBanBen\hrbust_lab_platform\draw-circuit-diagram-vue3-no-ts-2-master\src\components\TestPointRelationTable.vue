<!-- 测点连接关系表格组件 -->
<template>
  <div class="test-point-relation-table">
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>测点连接关系表</span>
          <div class="header-controls">
            <el-button size="small" type="primary" @click="refreshData">刷新数据</el-button>
            <el-button size="small" type="success" @click="exportData">导出数据</el-button>
          </div>
        </div>
      </template>
      
      <div v-if="!relationData || relationData.testPointConnections.length === 0" class="no-data">
        暂无测点连接关系数据
      </div>
      
      <div v-else class="table-container">
        <el-table :data="relationData.testPointConnections" style="width: 100%" border stripe>
          <el-table-column label="测点名称" min-width="100">
            <template #default="scope">
              {{ scope.row.testPoint.name }} 
              <el-tag v-if="scope.row.testPoint.identifier" size="small">
                {{ scope.row.testPoint.identifier }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="测点位置" width="160">
            <template #default="scope">
              <span v-if="scope.row.testPoint.position">
                X: {{ Math.round(scope.row.testPoint.position.x) }}, 
                Y: {{ Math.round(scope.row.testPoint.position.y) }}
              </span>
              <span v-else>未知位置</span>
            </template>
          </el-table-column>
          
          <el-table-column label="连接设备" min-width="120">
            <template #default="scope">
              {{ scope.row.connectedDevice.name }}
              <el-tag v-if="scope.row.connectedDevice.identifier" size="small" type="info">
                {{ scope.row.connectedDevice.identifier }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="连接点" width="80">
            <template #default="scope">
              {{ scope.row.connectedDevice.connectionPoint || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="连接类型" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.connectionType === 'direct' ? 'success' : 'warning'">
                {{ scope.row.connectionType === 'direct' ? '直接连接' : '间接连接' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="table-footer">
          <span class="data-info">共 {{ relationData.testPointConnections.length }} 个连接关系</span>
          <span class="timestamp">更新时间: {{ formatTime(relationData.timestamp) }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCurrentTestPointRelations } from '@/utils/testPointManager'

// 连接关系数据
const relationData = ref(null)

// 获取数据
const loadData = () => {
  const data = getCurrentTestPointRelations()
  if (data) {
    relationData.value = data
  } else {
    ElMessage.warning('获取测点连接关系数据失败')
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
  ElMessage.success('测点连接关系数据已刷新')
}

// 导出数据
const exportData = () => {
  if (!relationData.value) {
    ElMessage.warning('没有可导出的数据')
    return
  }
  
  try {
    // 创建Blob对象
    const jsonStr = JSON.stringify(relationData.value, null, 2)
    const blob = new Blob([jsonStr], { type: 'application/json' })
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `测点连接关系_${new Date().toISOString().slice(0, 16).replace('T', '_')}.json`
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据时出错:', error)
    ElMessage.error('导出数据失败')
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  
  try {
    const date = new Date(timestamp)
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
  } catch (error) {
    return timestamp
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.test-point-relation-table {
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.header-controls {
  display: flex;
  gap: 8px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.table-container {
  overflow-x: auto;
}

.table-footer {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 12px;
}
</style> 