/**
 * 校验该页面的访问来源
 * 1. 从浏览器标签地址栏访问，就提示错误
 * 2. 从浏览器刷新访问，就提示错误
 */
import { ElMessageBox } from 'element-plus'

export default function validateURLReference() {
  // 判断是否从浏览器标签地址栏访问
  if (document.referrer === '') {
    console.error('该页面不支持从地址栏直接访问，请从系统内的连线按钮跳转过来，方可使用！')
    ElMessageBox.alert(
      '该页面不支持从地址栏直接访问，请从系统内的连线按钮跳转过来，方可使用！ <br> ' +
        '点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！',
      '警告',
      {
        confirmButtonText: '收到',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        callback: () => {
          window.close()
        },
      },
    )
  }

  // 判断页面是否刷新
  const isRefresh = sessionStorage.getItem('isRefreshing')
  if (isRefresh === 'true') {
    console.error('该页面不支持刷新，请从系统内的连线按钮跳转过来，方可使用！')
    ElMessageBox.alert(
      '该页面不支持刷新，请从系统内的连线按钮跳转过来，方可使用！ <br> ' +
        '点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！',
      '警告',
      {
        confirmButtonText: '收到',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        callback: () => {
          window.close()
        },
      },
    )
    sessionStorage.removeItem('isRefreshing')
  }

  window.addEventListener('beforeunload', () => {
    sessionStorage.setItem('isRefreshing', 'true')
  })

  window.addEventListener('load', () => {
    const isRefreshing = sessionStorage.getItem('isRefreshing')
    if (isRefreshing === 'true') {
      console.error('该页面不支持刷新，请从系统内的连线按钮跳转过来，方可使用！')
      ElMessageBox.alert(
        '该页面不支持刷新，请从系统内的连线按钮跳转过来，方可使用！ <br> ' +
          '点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！',
        '警告',
        {
          confirmButtonText: '收到',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          callback: () => {
            window.close()
          },
        },
      )
      sessionStorage.removeItem('isRefreshing')
    }
  })
}
