import { storeToRefs } from 'pinia'
import { useCanvasInfoStore } from '@/store/canvasInfo'
import { useComponentInfoStore } from '@/store/componentInfo'
import { useTextBoxStore } from '@/store/textBox'
import { nanoid } from 'nanoid'
import componentCounters from '@/layout/ComponentsPanel/data/componentCounters.js'
import { watchEffect, watch } from 'vue'
import { useComponentsInfoStore } from '@/store/componentsInfo'

export default function useDropComponent(emit) {
  // store: 获取 useCanvasInfoStore 实例，并解构
  const canvasInfoStore = useCanvasInfoStore()
  const { getClosestGridPoint } = canvasInfoStore

  // store: 获取 useComponentInfoStore 实例，并解构
  const { DEFAULT_SCALE } = storeToRefs(useComponentInfoStore())

  // store
  const componentsInfoStore = useComponentsInfoStore()
  const { components } = storeToRefs(componentsInfoStore)

  // store: 获取 useTextBoxStore 实例，并解构
  const textBoxStore = useTextBoxStore()
  const {
    textBoxs,
    TOP_DEFAULT_OFFSET_X,
    TOP_DEFAULT_OFFSET_Y,
    BOTTOM_DEFAULT_OFFSET_X,
    BOTTOM_DEFAULT_OFFSET_Y,
    CUSTOM_DEFAULT_OFFSET_X,
    CUSTOM_DEFAULT_OFFSET_Y,
  } = storeToRefs(textBoxStore)
  const { generateTextBoxeToComponent } = textBoxStore

  /**
   * 处理组件放置逻辑
   */
  const handleDrop = (event) => {
    const componentData = JSON.parse(event.dataTransfer.getData('componentData'))

    // 获取鼠标的放置位置
    const dropX = event.offsetX
    const dropY = event.offsetY

    // 计算最近的网格点
    const closestPoint = getClosestGridPoint(dropX, dropY)

    // 根据类型获取当前组件的计数器并更新
    if (componentCounters[componentData.type] === undefined) {
      // 如果该类型没有计数器，初始化为0
      componentCounters[componentData.type] = 0
    }
    const counter = componentCounters[componentData.type]
    componentCounters[componentData.type] += 1
    // 生成 [组件 ID]
    const _componentId = `${Date.now()}_${nanoid()}`
    // 生成 [唯一标识符]，例如 R1, C2 等
    let identifier;
    if (componentData.type === 'testPoint') {
      // 🔧 对于测点组件，确保标识符唯一性
      // 获取当前所有测点的标识符
      const existingTestPoints = componentsInfoStore.components.filter(comp =>
        comp.type === 'testPoint' || comp.isTestPoint
      );
      const existingIdentifiers = existingTestPoints.map(comp =>
        parseInt(comp.identifier) || 0
      ).filter(id => !isNaN(id));

      // 找到最大的标识符数字，然后+1
      const maxId = existingIdentifiers.length > 0 ? Math.max(...existingIdentifiers) : 0;
      identifier = `${maxId + 1}`;

      console.log('🔧 测点标识符生成:', {
        type: componentData.type,
        existingIdentifiers: existingIdentifiers,
        maxId: maxId,
        newIdentifier: identifier,
        componentCounters: componentCounters
      });
    } else {
      identifier = `${componentData.symbol}${counter + 1}` || null;
    }
    // 生成 [新的组件数据]
    const newComponentData = {
      ...componentData,
      componentId: _componentId,
      x: closestPoint.x,
      y: closestPoint.y,
      rotation: 0,
      scale: componentData.defaultScale || DEFAULT_SCALE.value,
      identifier: identifier, // 添加唯一标识符: 例如R1、R2、C1、C2等等。
      value: componentData.defaultValue || '', // 初始值为空字符串
      unit: componentData.defaultUnit || '', // 设置默认单位
      isEditingValue: false, // 初始状态下不处于编辑状态
    }

    // 🔧 添加调试日志 - 组件创建完成
    if (componentData.type === 'testPoint') {
      console.log('🔧 测点组件创建完成:', {
        type: newComponentData.type,
        identifier: newComponentData.identifier,
        componentId: newComponentData.componentId,
        hasIdentifier: !!newComponentData.identifier,
        fullData: newComponentData
      });
    }

    // 触发 [添加组件] 事件
    emit('add-component', newComponentData)

    // 在放置组件时，自动生成上、下两个文本框（测点除外）
    if (componentData.type !== 'testPoint') {
      const topTextBox = generateTextBoxeToComponent(
        newComponentData.componentId,
        'top',
        `${newComponentData.identifier}`,
        closestPoint.x + TOP_DEFAULT_OFFSET_X.value,
        closestPoint.y + TOP_DEFAULT_OFFSET_Y.value,
      )

      const bottomTextBox = generateTextBoxeToComponent(
        newComponentData.componentId,
        'bottom',
        `${newComponentData.value} ${newComponentData.unit}`,
        closestPoint.x + BOTTOM_DEFAULT_OFFSET_X.value,
        closestPoint.y + BOTTOM_DEFAULT_OFFSET_Y.value,
      )
    }

    console.log('@@@ 所有的文本框', textBoxs.value)
  }

  return { handleDrop }
}
