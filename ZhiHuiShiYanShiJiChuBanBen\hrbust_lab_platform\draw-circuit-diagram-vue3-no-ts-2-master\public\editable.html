<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>实验报告</title>
  <link href="wangeditor/style.css" rel="stylesheet">
  <style>
    /* 编辑器容器样式 */
    #editor-wrapper {
      border: 1px solid #ccc;
      z-index: 100;
      margin: 20px 0;
    }

    #toolbar-container {
      border-bottom: 1px solid #ccc;
      background-color: #fff;
      padding: 5px;
    }

    #editor-container {
      height: 500px;
      overflow-y: auto;
      background-color: #fff;
      padding: 10px;
    }

    /* 工具栏按钮样式 */
    .w-e-bar-item button {
      padding: 3px 10px !important;
      margin: 0 2px !important;
      border: none !important;
      background-color: transparent !important;
      border-radius: 3px !important;
    }

    .w-e-bar-item button:hover {
      background-color: #f1f1f1 !important;
    }

    .w-e-bar-item button.active {
      background-color: #e6f3ff !important;
    }

    /* 编辑器内容区域样式 */
    .w-e-text {
      padding: 10px !important;
      min-height: 500px !important;
    }

    /* 确保编辑器内容区域有合适的内边距 */
    .w-e-text-container {
      min-height: 500px !important;
      background-color: #fff !important;
    }

    /* 工具栏分组样式 */
    .w-e-bar-divider {
      margin: 0 5px !important;
    }

    /* 下拉菜单样式 */
    .w-e-drop-panel {
      border: 1px solid #ccc !important;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }

    .action-buttons {
      margin: 15px 0;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    .custom-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      border: none;
      border-radius: 50px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      color: #666;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      min-width: 120px;
      text-align: center;
    }

    .custom-button i {
      margin-right: 8px;
      font-size: 16px;
      display: inline-flex;
      align-items: center;
    }

    .screenshot-btn {
      background: linear-gradient(145deg, #e8f5e9, #c8e6c9);
      border: 1px solid #a5d6a7;
      color: #2e7d32;
    }

    .video-btn {
      background: linear-gradient(145deg, #e3f2fd, #bbdefb);
      border: 1px solid #90caf9;
      color: #1565c0;
    }

    .submit-btn {
      background: linear-gradient(145deg, #fff3e0, #ffe0b2);
      border: 1px solid #ffb74d;
      color: #e65100;
    }

    .custom-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      filter: brightness(1.05);
    }

    .custom-button:active {
      transform: translateY(0);
      filter: brightness(0.95);
    }

    .custom-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    @keyframes recording {
      0% { box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(255, 82, 82, 0); }
      100% { box-shadow: 0 0 0 0 rgba(255, 82, 82, 0); }
    }

    .recording {
      animation: recording 2s infinite;
      background: linear-gradient(145deg, #ffebee, #ffcdd2) !important;
      border: 1px solid #ef9a9a !important;
      color: #c62828 !important;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
  </div>

  <div id="editor-wrapper">
    <div id="toolbar-container"><!-- 工具栏 --></div>
    <div id="editor-container"><!-- 编辑器 --></div>
  </div>
  
  <div class="action-buttons">
    <button id="insert-image-btn" class="custom-button screenshot-btn">
      插入截图
    </button>
    <button id="insert-video-btn" class="custom-button video-btn">
      插入视频
    </button>
    <button id="submit-btn" class="custom-button submit-btn">
      保存
    </button>
  </div>

  <div id="video-container" style="margin-top: 20px; border: 1px solid #ccc; padding: 10px; border-radius: 5px; background: #f9f9f9;">
    <p style="margin: 0 0 10px 0; color: #666;"><i class="fas fa-play-circle"></i> 视频显示区域：</p>
  </div>

  <!-- 注释掉或删除原来的CDN引用 -->
  <!-- <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script> -->
  <!-- 添加本地文件引用 -->
  <script src="wangeditor/axios.min.js"></script>
  <script src="wangeditor/index.js"></script>
  <script>
    // 确保 wangEditor 加载完成
    if (typeof window.wangEditor === 'undefined') {
      console.error('wangEditor 加载失败，请检查网络连接');
    } else {
      const E = window.wangEditor;

      // 初始化富文本编辑器
      window.editor = E.createEditor({
        selector: '#editor-container',
        config: {
          placeholder: '请输入内容...',
          onChange(editor) {
            console.log('内容变化:', editor.getHtml());
          },
          MENU_CONF: {
            uploadImage: {
              server: '/upload',
              fieldName: 'file',
              maxFileSize: 10 * 1024 * 1024,
              allowedFileTypes: ['image/*'],
              customInsert(res, insertFn) {
                insertFn(res.data.url, res.data.alt, res.data.href);
              },
            },
          },
          scroll: true,
          autoFocus: true,
          readOnly: false,
        },
        mode: 'default',
        html: '<h1>标题</h1>',
      });

      window.toolbar = E.createToolbar({
        editor: window.editor,
        selector: '#toolbar-container',
        config: {
          toolbarKeys: [
            'headerSelect',
            'bold',
            'italic',
            'underline',
            'through',
            'color',
            'bgColor',
            'fontSize',
            'fontFamily',
            'lineHeight',
            'bulletedList',
            'numberedList',
            'todo',
            'justifyLeft',
            'justifyCenter',
            'justifyRight',
            'insertLink',
            'insertTable',
            'codeBlock',
            'blockquote',
          ],
          insertKeys: {
            index: 0,
            keys: [],
          },
        },
      });

      // 加载模板方法
      async function loadTemplate() {
        try {
          const courseName = localStorage.getItem('courseName');
          const expName = localStorage.getItem('experimentName');
          if (!courseName || !expName) {
            console.error('未找到课程名称或实验名称');
            return;
          }

          const response = await axios.get(`/template/project/selectByName?experimentName=${encodeURIComponent(expName)}&courseName=${encodeURIComponent(courseName)}`);
          const template = response.data[0];
       
          if (template && template.content) {
            console.log('加载模板内容:', template.content);
            
            // 确保编辑器已经完全初始化
            setTimeout(() => {
              // 先让编辑器获取焦点
              if (!window.editor.isFocused()) {
                window.editor.focus();
              }
              
              // 清空当前内容
              window.editor.select([]);
              window.editor.deleteFragment();
              
              // 插入新内容
              window.editor.dangerouslyInsertHtml(template.content);
              
              // console.log('内容已插入');
            }, 500);
          } else {
            console.log('未找到模板数据');
          }
        } catch (error) {
          console.error('获取模板失败', error);
        }
      }

      // 确保编辑器初始化完成后再加载模板
      setTimeout(() => {
        loadTemplate();
      }, 500);

      // 插入图片方法
      function insertImageToEditor(base64Image) {
        if (window.editor) {
          const htmlContent = `<img src="${base64Image}" alt="Base64 Image" />`;
          editor.dangerouslyInsertHtml(htmlContent);
          } else {
            console.error('编辑器未初始化');
        }
      }

      // 获取cookie值的辅助函数
      function getCookieValue(name) {
        const cookies = document.cookie.split('; ');
        for (let cookie of cookies) {
          const [key, value] = cookie.split('=');
          if (key === name) {
            return decodeURIComponent(value);
          }
        }
        return null;
      }

      // 添加表格样式的辅助函数
      function addTableStyles(content) {
        if (!content) return '';
        return content
          .replace(
            /<table/g,
            '<table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;"'
          )
          .replace(
            /<td/g,
            '<td style="border: 1px solid #ddd; padding: 8px; text-align: center;"'
          )
          .replace(
            /<th/g,
            '<th style="border: 1px solid #ddd; padding: 8px; text-align: center;"'
          );
      }

      // 插入图片按钮事件
    /*  document.getElementById('insert-image-btn').addEventListener('click', () => {
        const cookieString = document.cookie;
        const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
        const lastDirectoryMatch = cookieString.match(/(?:^|;\s*)lastDirectory=([^;]*)/);

        const program = lastDirectoryMatch ? lastDirectoryMatch[1] : '';
        const stu_id = match[1];
        const params = new URLSearchParams();
        params.append('stu_id', stu_id);
        params.append('program', program);

        axios.post(`/../remote_server/video/screen`, null, {
          params: params
        })
          .then(response => {
            const base64Image = response.data;
            if (base64Image.startsWith('data:image/png;base64,')) {
              insertImageToEditor(base64Image);
            } else {
              console.error('返回格式错误:', base64Image);
            }
          })
          .catch(error => console.error('请求出错:', error));
      });*/

      // 插入视频按钮事件
/*      document.getElementById('insert-video-btn').addEventListener('click', () => {
        const recordButton = document.getElementById('insert-video-btn');
        recordButton.disabled = true;
        recordButton.classList.add('recording');
        recordButton.textContent = '录制中(10s)';
        
        let countdown = 10;
        const timer = setInterval(() => {
          countdown--;
          recordButton.textContent = `录制中(${countdown}s)`;
          if (countdown <= 0) {
            clearInterval(timer);
            recordButton.textContent = '插入视频';
            recordButton.classList.remove('recording');
            recordButton.disabled = false;
          }
        }, 1000);

        const cookieString = document.cookie;
        const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
        const lastDirectoryMatch = cookieString.match(/(?:^|;\s*)lastDirectory=([^;]*)/);

        const stu_id = match[1];
        const program = lastDirectoryMatch ? lastDirectoryMatch[1] : '';

        const params = new URLSearchParams();
        params.append('stu_id', stu_id);
        params.append('program', program);

        axios.post(`/../remote_server/video/record`, null, {
          params: params
        })
          .then(response => {
            const base64Video = response.data;
            if (base64Video.startsWith('data:video/mp4;base64,')) {
              displayVideoOnPage(base64Video);
            } else {
              console.error('返回格式错误:', base64Video);
            }
          })
          .catch(error => console.error('请求出错:', error));
      });
*/





      // 显示视频到网页方法
      function displayVideoOnPage(base64Video) {
        const videoContainer = document.getElementById('video-container');
        const videoElement = document.createElement('video');
        videoElement.controls = true;
        videoElement.src = base64Video;
        videoElement.style.width = '100%';
        videoElement.style.maxHeight = '500px';
        videoContainer.innerHTML = '';
        videoContainer.appendChild(videoElement);
        console.log('视频已插入到网页:', base64Video);
      }

      // 修改按钮文本
      document.getElementById('submit-btn').textContent = '保存';

      // 修改提交按钮事件处理
      document.getElementById('submit-btn').addEventListener('click', async () => {
        const submitButton = document.getElementById('submit-btn');
        
        try {
          // 禁用提交按钮
          submitButton.disabled = true;
          submitButton.style.opacity = '0.6';
          submitButton.textContent = '提交中...';

          const editorContent = window.editor.getHtml();
          const videoContainer = document.getElementById('video-container');
          const videoElement = videoContainer.querySelector('video');
          const videoSrc = videoElement ? videoElement.src : '';
          
          const cookieString = document.cookie;
          const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
          
          if (!match || !match[1]) {
            throw new Error('未找到用户ID');
          }
          
          // 修正时间，加上8小时
          const now = new Date();
          now.setHours(now.getHours() + 8);
          const submitTime = now.toISOString().replace('T', ' ').substring(0, 19);
          
          const projectName = getCookieValue('projectName');
          if (!projectName) {
            throw new Error('未找到项目名称');
          }
          
          const submitData = {
            stuId: match[1],
            projectName: projectName,
            content: editorContent,
            submitTime: submitTime,
            videoUrl: videoSrc || '',
            userId: match[1]
          };

          console.log('Submitting report:', submitData);

          const response = await axios.post(
            `/studentReportTemplate/add`, 
            submitData,
            {
              params: {
                userId: match[1]
              }
            }
          );
          
          if (response.data) {
            submitButton.textContent = '已保存';
            submitButton.disabled = true;
            submitButton.style.cursor = 'not-allowed';
            
            // 存储当前实验的提交状态，并设置有效期为1小时
            const submitKey = `reportSubmitted_${projectName}_${match[1]}`;
            
            // 创建包含值和过期时间的对象
            const submitData = {
              value: 'true',
              expiry: new Date().getTime() + 60 * 60 * 1000 // 当前时间 + 1小时（毫秒）
            };
            
            // 将对象转换为JSON字符串并存储
            localStorage.setItem(submitKey, JSON.stringify(submitData));
            
            return;
          }
          
          submitButton.disabled = false;
          submitButton.style.opacity = '1';
          submitButton.textContent = '提交';
          alert('提交失败：未收到服务器响应');

        } catch (error) {
          console.error('提交报告时出错:', error);
          submitButton.disabled = false;
          submitButton.style.opacity = '1';
          submitButton.textContent = '提交';
          alert(error.message || '提交失败，请检查网络连接后重试');
        }
      });

      // 从localStorage获取带有过期时间的值的辅助函数
      function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key);
        
        // 如果没有找到项目，返回null
        if (!itemStr) {
          return null;
        }
        
        try {
          const item = JSON.parse(itemStr);
          const now = new Date();
          
          // 比较当前时间和过期时间
          if (now.getTime() > item.expiry) {
            // 如果已过期，删除项目并返回null
            localStorage.removeItem(key);
            return null;
          }
          return item.value;
        } catch (e) {
          // 如果项目不是有效的JSON，可能是旧格式，直接返回项目
          return itemStr;
        }
      }

      // 页面加载时检查当前实验是否已提交
      document.addEventListener('DOMContentLoaded', () => {
        const userId = getCookieValue('userId');
        const projectName = getCookieValue('projectName');
        const submitKey = `reportSubmitted_${projectName}_${userId}`;
        
        const isSubmitted = getWithExpiry(submitKey);
        
        if (isSubmitted === 'true') {
          const submitButton = document.getElementById('submit-btn');
          submitButton.disabled = true;
          submitButton.style.opacity = '0.6';
          submitButton.style.cursor = 'not-allowed';
          submitButton.textContent = '已提交';
        }
      });
    }
  </script>
</body>
</html>
