import { useVerifyStore } from '@/store/verify'
import { getRolesByUserId } from '@/api/sys-user'
import { addOrUpdate } from '@/api/circuitDataTemplate'
import { ElNotification } from 'element-plus'
import { useCurrentUserCircuitDataStore } from '@/store/currentUserCircuitData'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { storeToRefs } from 'pinia'
import { ref, onMounted } from 'vue'
import { exportTestPointRelations, findSurroundingComponents } from '@/utils/canvasDataManager'
import { isUsingV1_1, getSaveTemplateVersion, printCurrentConfig } from '@/config/validationConfig'
import { componentsData } from '@/layout/ComponentsPanel/data/componentsData.js'

/**
 * 保存连接关系为模板 hooks
 * @returns
 */
export default function () {
  // 校验 store : { 生成连接关系 }
  const verifyStore = useVerifyStore()
  const { generateConnectionRelationships } = verifyStore

  // store: 当前用户电路图数据
  const currentUserCircuitDataStore = useCurrentUserCircuitDataStore()
  const { currentUserCircuitData } = storeToRefs(currentUserCircuitDataStore)

  /**
   * 保存连接关系为模板
   */
  const saveConnectionToTemplate = (courseName, experimentName) => {
    // 🔧 修复：安全获取连接关系，防止状态更新时的错误
    let connections = []
    try {
      connections = generateConnectionRelationships() || []
    } catch (error) {
      console.error('❌ 获取连接关系时出错:', error)
      connections = []
    }
    console.log('@@@ 两个名称', courseName, experimentName)

    // 将连接关系保存到txt并下载 - 暂时注释掉
    // saveToTxtAndDownload(courseName, experimentName, connections)

    // 将连接关系&电路图数据 保存到数据库
    saveToDB(courseName, experimentName, connections)
  }

  /**
   * 将连接关系保存到txt并下载 - 暂时注释掉
   */
  // const saveToTxtAndDownload = (courseName, experimentName, connections) => {
  //   // 拼接文件名，使用 courseName 和 experimentName
  //   const fileName = `${courseName}_${experimentName}.json`
  //   // 将 connections 转换为 JSON 字符串
  //   const jsonData = JSON.stringify(connections, null, 2)
  //   // 创建一个 Blob 对象，类型为 application/json
  //   const blob = new Blob([jsonData], { type: 'application/json' })
  //   // 创建一个指向 Blob 对象的 URL
  //   const url = URL.createObjectURL(blob)
  //   // 创建一个临时的下载链接
  //   const a = document.createElement('a')
  //   a.href = url
  //   a.download = fileName // 设置下载的文件名
  //   // 触发下载
  //   a.click()
  //   // 释放 URL 对象
  //   URL.revokeObjectURL(url)
  //   console.log(`文件已成功保存: ${fileName}`)
  // }

  /**
   * 收集当前电路数据
   */
  const collectData = () => {
    // 🔧 修复：安全获取连接关系，防止状态更新时的错误
    let connections = []
    try {
      connections = generateConnectionRelationships() || []
    } catch (error) {
      console.error('❌ 收集数据时获取连接关系出错:', error)
      connections = []
    }

    // 🔧 修复：安全获取电路图数据，添加空值检查
    let circuitData = null
    try {
      if (currentUserCircuitData && currentUserCircuitData.value) {
        circuitData = currentUserCircuitData.value
      } else {
        console.warn('⚠️ currentUserCircuitData 未初始化，使用空数据')
        circuitData = {
          components: [],
          fixedLines: [],
          intersections: [],
          textBoxs: []
        }
      }
    } catch (error) {
      console.error('❌ 获取电路数据时出错:', error)
      circuitData = {
        components: [],
        fixedLines: [],
        intersections: [],
        textBoxs: []
      }
    }

    console.log('🔍 收集的电路数据:', circuitData)
    console.log('🔍 组件数量:', circuitData?.components?.length || 0)
    console.log('🔍 连接关系数量:', connections?.length || 0)

    return { circuitData, connections }
  }

  /**
   * 生成v1.1版本的规范化连接关系
   */
  const generateNormalizedConnections = (originalConnections) => {
    // 组件类型规范化函数 - v1版本（原始版本，保留兼容性）
    const normalizeComponentTypeV1 = (type) => {
      if (!type) return type

      // 保持电容的极性区分 - 注意顺序！先判断无极性电容
      if (type.includes('无极性电容')) return '无极性电容'
      if (type.includes('极性电容')) return '极性电容'

      // 电阻类型规范化
      if (type.includes('电阻')) {
        if (type.includes('可变')) return '可变电阻'
        return '电阻'
      }

      // 示波器类型规范化
      if (type.includes('示波器')) return '示波器'

      // 测点类型规范化
      if (type.includes('测点')) return '测点'

      // 二极管类型规范化
      if (type.includes('二极管')) return '二极管'

      // 其他组件保持原样
      return type
    }

    // 组件类型规范化函数 - v1.1版本（智能匹配算法，自动支持新器件）
    const normalizeComponentTypeV1_1 = (type) => {
      if (!type) return type

      // 🚀 智能匹配算法：基于组件库自动匹配，支持新器件
      const typeStr = type.trim()

      // 获取所有组件类型，按长度降序排列（长词优先匹配，避免混淆）
      const componentTypes = componentsData
        .map(comp => comp.label)
        .filter(label => label && label.trim()) // 过滤空标签
        .sort((a, b) => b.length - a.length) // 长度降序：确保"无极性电容"优先于"极性电容"

      // 特殊处理：NPN/PNP 三极管的标准化
      if (typeStr === 'NPN') return 'NPN'
      if (typeStr === 'PNP') return 'PNP'

      // 智能匹配：找到第一个包含的组件类型
      for (const compType of componentTypes) {
        if (typeStr.includes(compType)) {
          return compType
        }
      }

      // 如果没有找到匹配，返回原始类型
      return type
    }

    // 统一的规范化组件类型函数 - 根据配置选择版本
    const normalizeComponentType = (type) => {
      // 根据配置选择使用哪个版本
      if (isUsingV1_1()) {
        return normalizeComponentTypeV1_1(type)
      } else {
        return normalizeComponentTypeV1(type)
      }
    }

    // 规范化连接字符串 - 保留组件类型、连接点、极性，去掉ID和参数
    const normalizeConnectionString = (connectionDesc) => {
      if (!connectionDesc) return ''

      // 连接描述格式: "组件类型 - 组件ID - 参数 - 连接点 - 极性"
      const parts = connectionDesc.split(' - ')

      if (parts.length >= 5) {
        const componentType = normalizeComponentType(parts[0].trim())
        const connectionPoint = parts[3].trim() // 连接点信息
        const polarity = parts[4].trim() // 极性信息

        // 重新组合：组件类型 - 连接点 - 极性
        return `${componentType} - ${connectionPoint} - ${polarity}`
      } else if (parts.length === 4) {
        // 处理4段格式，可能是参数为空的情况
        const componentType = normalizeComponentType(parts[0].trim())
        const connectionPoint = parts[2].trim() // 连接点信息
        const polarity = parts[3].trim() // 极性信息

        return `${componentType} - ${connectionPoint} - ${polarity}`
      } else if (parts.length === 3) {
        // 处理3段格式
        const componentType = normalizeComponentType(parts[0].trim())
        const connectionPoint = parts[1].trim() // 连接点信息
        const polarity = parts[2].trim() // 极性信息

        return `${componentType} - ${connectionPoint} - ${polarity}`
      } else if (parts.length >= 1) {
        // 如果格式不标准，至少保留组件类型
        const componentType = normalizeComponentType(parts[0].trim())
        return `${componentType} - 连接点 - null`
      }

      return connectionDesc
    }

    // 转换为规范化连接关系 - 保留连接点和极性信息
    return originalConnections.map(conn => ({
      from: normalizeConnectionString(conn.from),
      to: normalizeConnectionString(conn.to)
    }))
  }

  /**
   * 将连接关系&电路图数据 保存到数据库
   */
  const saveToDB = (courseName, experimentName, connections) => {
    // 将【电路图数据、连接关系以及测点关系】保存进数据库作为模板（新增或者修改）
    // 获取电路图数据
    const { circuitData } = collectData()

    // 🔧 修复：使用与普通保存相同的测点数据生成逻辑
    console.log('🔍 开始分析测点连接关系...')
    console.log('🔍 尝试使用 exportTestPointRelations() 函数...')

    let testPointRequirements = {}
    try {
      // 首先尝试使用与普通保存相同的函数
      const testPointRelationsData = exportTestPointRelations()
      console.log('🔍 exportTestPointRelations 结果:', testPointRelationsData)

      if (testPointRelationsData && typeof testPointRelationsData === 'string') {
        // 如果返回的是字符串，尝试解析
        const parsed = JSON.parse(testPointRelationsData)

        // 🔧 修复：从标准JSON格式转换为测点要求格式
        if (parsed.groupedByTestPoint && Object.keys(parsed.groupedByTestPoint).length > 0) {
          testPointRequirements = parsed.groupedByTestPoint
          console.log('🔍 从 groupedByTestPoint 提取数据:', testPointRequirements)
        } else {
          console.warn('⚠️ exportTestPointRelations 返回空的 groupedByTestPoint，使用备用方法')
          testPointRequirements = analyzeConnections()
        }
      } else if (testPointRelationsData && typeof testPointRelationsData === 'object') {
        // 如果返回的是对象，检查格式
        if (testPointRelationsData.groupedByTestPoint && Object.keys(testPointRelationsData.groupedByTestPoint).length > 0) {
          testPointRequirements = testPointRelationsData.groupedByTestPoint
          console.log('🔍 从对象的 groupedByTestPoint 提取数据:', testPointRequirements)
        } else {
          console.warn('⚠️ 对象格式的数据中 groupedByTestPoint 为空，使用备用方法')
          testPointRequirements = analyzeConnections()
        }
      } else {
        console.warn('⚠️ exportTestPointRelations 返回无效数据，使用备用方法')
        testPointRequirements = analyzeConnections()
      }

      console.log('🔍 转换后的测点要求:', testPointRequirements)
    } catch (error) {
      console.warn('⚠️ exportTestPointRelations 失败，尝试备用方法:', error)
      // 如果失败，使用备用的 analyzeConnections 方法
      testPointRequirements = analyzeConnections()
    }

    console.log('🔍 最终测点要求结果:', testPointRequirements)
    console.log('🔍 测点要求是否为空:', Object.keys(testPointRequirements).length === 0)

    // 生成v1.1版本的规范化连接关系
    const normalizedConnections = generateNormalizedConnections(connections)

    console.log('📋 v1版本连接关系 (原始):', connections)
    console.log('📋 v1.1版本连接关系 (规范化):', normalizedConnections)

    // 构造模板数据 - 包含两个版本的连接关系（用于本地存储）
    const templateData = {
      circuitData: circuitData,
      connections: connections, // v1版本：原始连接关系（仅用于本地存储）
      connectionsV1_1: normalizedConnections, // v1.1版本：规范化连接关系
      testPointRequirements: testPointRequirements // 测点要求（与浏览器存储模板格式一致）
    }

    // 🚨 重要说明：生产环境数据库保存策略
    // 1. 本地localStorage：保存两个版本（v1和v1.1）用于调试和兼容性
    // 2. 数据库：只保存v1.1版本，确保生产环境使用精确匹配逻辑
    // 3. 这样可以避免组件类型混淆问题，提高校验准确性

   
    // 🚨 生产环境修改：数据库保存使用v1.1版本连接关系
    // 构造请求体 - 数据库保存使用v1.1规范化连接关系
    const body = {
      courseName,
      experimentName,
      circuitData: JSON.stringify(circuitData),
      connections: JSON.stringify(normalizedConnections), // 🔧 修改：使用v1.1版本连接关系
      templateData: JSON.stringify({
        ...templateData,
        connections: normalizedConnections // 🔧 修改：模板数据中也使用v1.1版本
      }),
      testPointRelations: JSON.stringify(testPointRequirements) // 独立的测点关系数据（testPointRequirements格式）
    }

    // 发送到后端数据库
    addOrUpdate(body)
      .then((res) => {
        console.log('✅ 后端响应:', res.data)
        const response = res.data // 获取响应内容
        if (response.code === 200) {
          ElNotification({
            title: '模板保存成功',
            message: `模板已保存到数据库（使用v1.1版本连接关系）`,
            type: 'success'
          })
          console.log('✅ 模板保存成功！')
          console.log('✅ 🚨 生产环境：数据库使用v1.1版本连接关系')
          console.log('✅ 包含测点数据:', Object.keys(templateData.testPointRequirements).length > 0 ? '是' : '否')
          console.log('✅ 测点分组数量:', Object.keys(templateData.testPointRequirements).length)
          console.log('✅ 保存到数据库的连接数量:', normalizedConnections.length)
          console.log('✅ 本地保留的v1版本连接数量:', connections.length)
          console.log('✅ 本地保留的v1.1版本连接数量:', normalizedConnections.length)
        } else {
          console.error('❌ 后端返回错误:', response)
          ElNotification({
            title: '保存失败',
            message: `保存失败: ${response.msg}`,
            type: 'error'
          })
        }
      })
      .catch((err) => {
        console.error('❌ 保存失败:', err)
        ElNotification({
          title: '保存失败',
          message: '数据库连接失败，请检查网络连接',
          type: 'error'
        })
      })
  }

  /**
   * 使用与调试按钮相同的逻辑进行测点分组
   * @returns {Object} 测点要求格式
   */
  const analyzeConnections = () => {
    try {
      console.log('🚀 使用与调试按钮相同的测点分析逻辑')

      // 🔧 修复：安全获取组件数据，添加空值检查
      const componentsStore = useComponentsInfoStore()
      const { components } = storeToRefs(componentsStore)

      console.log('🔍 组件store状态:', {
        storeExists: !!componentsStore,
        componentsExists: !!components,
        componentsValue: components?.value,
        componentsLength: components?.value?.length || 0
      })

      // 🔧 修复：安全获取组件数据，防止 undefined.value 错误
      let componentsToUse = null
      try {
        componentsToUse = components?.value || []
      } catch (error) {
        console.warn('⚠️ 获取组件store数据时出错:', error)
        componentsToUse = []
      }
      if (!componentsToUse || componentsToUse.length === 0) {
        console.warn('⚠️ 组件store为空，尝试从其他地方获取组件数据...')

        // 🔧 修复：安全获取当前用户电路数据
        try {
          const circuitData = currentUserCircuitData?.value
          if (circuitData && circuitData.components) {
            console.log('🔍 从电路数据中获取组件:', circuitData.components.length)
            componentsToUse = circuitData.components
          }
        } catch (error) {
          console.warn('⚠️ 从电路数据获取组件时出错:', error)
        }

        // 如果还是没有，尝试从localStorage获取
        if (!componentsToUse || componentsToUse.length === 0) {
          try {
            const storedData = localStorage.getItem('currentUserCircuitData')
            if (storedData) {
              const parsed = JSON.parse(storedData)
              if (parsed.components) {
                console.log('🔍 从localStorage获取组件:', parsed.components.length)
                componentsToUse = parsed.components
              }
            }
          } catch (e) {
            console.warn('⚠️ 从localStorage获取组件数据失败:', e)
          }
        }
      }

      // 获取所有测点组件 - 与调试按钮相同的逻辑
      const testPoints = []
      console.log('🔍 使用的组件数据长度:', componentsToUse?.length || 0)

      componentsToUse?.forEach((component, index) => {
        console.log(`🔍 检查组件 ${index}:`, {
          type: component.type,
          isTestPoint: component.isTestPoint,
          id: component.id,
          componentId: component.componentId,
          label: component.label,
          identifier: component.identifier
        })

        if (component.type === 'testPoint' || component.isTestPoint) {
          testPoints.push({
            id: component.id || component.componentId,
            name: component.label || component.name || component.identifier || '测点',
            identifier: component.identifier || '',
            x: component.x,
            y: component.y,
            component: component
          })
          console.log(`✅ 找到测点:`, component)
        }
      })

      console.log('📍 找到测点数量:', testPoints.length)
      console.log('📍 测点详情:', testPoints)

      if (testPoints.length === 0) {
        console.warn('⚠️ 没有找到任何测点')
        console.log('🔍 所有组件类型:', componentsToUse?.map(c => ({ type: c.type, isTestPoint: c.isTestPoint })))
        return {}
      }

      // 使用与调试按钮完全相同的逻辑分析每个测点
      const testPointRequirements = {}
      let testPointCounter = 0

      testPoints.forEach((testPoint, index) => {
        console.log(`🔍 分析测点 ${index + 1}/${testPoints.length}:`, testPoint)

        // 调用与调试按钮相同的函数
        const surroundingComponents = findSurroundingComponents(testPoint.component, componentsToUse)

        console.log(`🔍 测点 ${index + 1} 周围组件:`, surroundingComponents)

        if (surroundingComponents && surroundingComponents.length > 0) {
          testPointCounter++
          const identifier = testPointCounter.toString()

          console.log(`📋 测点${identifier} (${testPoint.x}, ${testPoint.y}): 连接了 ${surroundingComponents.length} 个器件`)

          testPointRequirements[identifier] = {
            connectedDevices: surroundingComponents.map(comp => ({
              typeName: comp.typeName,
              identifier: comp.identifier
            }))
          }

          // 输出详细信息
          console.log(`  器件列表: ${surroundingComponents.map(comp => `${comp.typeName}(${comp.identifier})`).join(', ')}`)
        } else {
          console.warn(`⚠️ 测点 ${index + 1} 没有找到周围组件`)
        }
      })

      console.log('📊 最终的测点分组结果:', testPointRequirements)
      console.log('📊 测点分组数量:', Object.keys(testPointRequirements).length)
      return testPointRequirements
    } catch (error) {
      console.error('❌ 分析连接关系时出错:', error)
      console.error('❌ 错误堆栈:', error.stack)
      return {}
    }
  }
  
  // analyzeConnections 函数已删除，现在使用统一的测点分组逻辑

  const canShowButton = ref(false) // 用来存储按钮是否显示的状态
  const roleList = ref([]) // 用来存储当前用户角色列表

  onMounted(() => {
    const userId = localStorage.getItem('userId')

    // 临时修改：如果是admin用户，直接显示按钮
    if (userId === 'admin' || userId === 'Admin' || userId === 'ADMIN') {
      canShowButton.value = true
      return
    }

    if (!userId) {
      canShowButton.value = true // 临时允许显示，方便调试
      return
    }

    hasRole(['系统超管', '教师', '管理员', 'admin', 'teacher', 'Admin', 'ADMIN'])
  })

  /**
   * 根据 用户ID 获取角色列表
   * @param {Array} roles 需要判断的 角色们
   */
  const hasRole = (roles) => {
    const userId = localStorage.getItem('userId')

    // 获取角色列表
    getRolesByUserId(userId)
      .then((res) => {
        const response = res.data // 获取响应内容
        if (response.code === 200) {
          roleList.value = response.data // 更新角色列表
          // 判断用户是否具有指定角色
          const hasPermission = roles.some((role) => roleList.value.includes(role))
          canShowButton.value = hasPermission
        } else {
          canShowButton.value = false
        }
      })
      .catch((error) => {
        canShowButton.value = true // 临时允许显示，方便调试
      })
  }



  // 调试函数 - 可以在浏览器控制台中调用
  const debugAnalyzeConnections = () => {
    console.log('🐛 开始调试测点分析...')
    const result = analyzeConnections()
    console.log('🐛 调试结果:', result)
    return result
  }

  // 将调试函数暴露到全局，方便在控制台调用
  if (typeof window !== 'undefined') {
    window.debugAnalyzeConnections = debugAnalyzeConnections
  }

  return {
    canShowButton,
    saveConnectionToTemplate,
    hasRole,
    debugAnalyzeConnections, // 导出调试函数
  }
}
