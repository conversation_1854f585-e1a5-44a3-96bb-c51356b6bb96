/* 紧凑型电路仪表组件样式 - 示波器大小风格 */

/* 公共容器样式 */
.meter-container {
  position: absolute;
  width: 180px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  z-index: 900;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  border: 1px solid rgba(0,0,0,0.1);
}

.meter-container:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.meter-container.dragging {
  opacity: 0.92;
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
  z-index: 999;
}

/* 电流表样式 */
.ammeter-container {
  top: 50px;
  right: 140px;
  border-top: 2px solid #2979ff;
}

/* 电压表样式 */
.voltmeter-container {
  top: 50px;
  right: 10px;
  border-top: 2px solid #00c853;
}

/* 表头样式 */
.meter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: #fafafa;
  cursor: move;
  user-select: none;
  position: relative;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.ammeter-container .meter-header {
  background: linear-gradient(120deg, rgba(235,245,255,0.6) 0%, #fafafa 100%);
}

.voltmeter-container .meter-header {
  background: linear-gradient(120deg, rgba(235,255,240,0.6) 0%, #fafafa 100%);
}

.meter-title {
  font-weight: 600;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 3px;
  color: #333;
}

.meter-icon {
  font-size: 12px !important;
  border-radius: 50%;
  padding: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ammeter-icon {
  color: #2979ff;
  background: rgba(41, 121, 255, 0.1);
}

.voltmeter-icon {
  color: #00c853;
  background: rgba(0, 200, 83, 0.1);
}

.control-buttons {
  display: flex;
  align-items: center;
}

.drag-handle {
  font-size: 12px !important;
  color: #9e9e9e;
  cursor: grab;
  padding: 1px;
}

.drag-handle:hover {
  color: #424242;
}

/* 主体内容 */
.meter-body {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 连接点样式 */
.connection-points {
  display: flex;
  justify-content: space-between;
  padding: 2px 25px;
  position: relative;
  height: 20px;
}

.connection-points::before {
  content: "";
  position: absolute;
  left: 20px;
  right: 20px;
  top: 50%;
  height: 1px;
  background: rgba(0,0,0,0.07);
  z-index: 1;
}

.ammeter-container .connection-points::before {
  background: rgba(41,121,255,0.1);
}

.voltmeter-container .connection-points::before {
  background: rgba(0,200,83,0.1);
}

.connection-point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #666;
  background: white;
}

.connection-point.positive {
  background: #ff5722;
}

.connection-point.negative {
  background: #2196f3;
}

.connection-point:hover, 
.connection-point.hover {
  transform: scale(1.1);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.ammeter-container .connection-point:hover {
  border-color: rgba(41,121,255,0.8);
  background: rgba(41,121,255,0.05);
}

.voltmeter-container .connection-point:hover {
  border-color: rgba(0,200,83,0.8);
  background: rgba(0,200,83,0.05);
}

.connection-point.connected {
  background: #ffffff;
  box-shadow: 0 0 0 1px rgba(255,87,34,0.4);
  border-color: #ff5722;
}

.connection-label {
  position: absolute;
  font-size: 8px;
  font-weight: 600;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  color: #424242;
  white-space: nowrap;
  background-color: #ffffff;
  padding: 0px 3px;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0,0,0,0.05);
}

/* 数值显示 */
.meter-display {
  padding: 8px;
  text-align: center;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  border-radius: 2px;
  margin: 4px 0;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.meter-value {
  font-size: 18px;
  font-weight: bold;
  transition: transform 0.2s;
}

.meter-value.updating {
  transform: scale(1.05);
}

.meter-unit {
  font-size: 9px;
  color: #666;
  margin-top: 2px;
}

/* 底部信息 */
.meter-footer {
  font-size: 8px;
  color: #999;
  text-align: center;
  padding: 2px 0;
}

.meter-id {
  font-weight: 600;
  color: #616161;
  background: rgba(0,0,0,0.03);
  padding: 0px 3px;
  border-radius: 2px;
}

.ammeter-container .meter-id {
  background: rgba(41,121,255,0.07);
  color: #1565c0;
}

.voltmeter-container .meter-id {
  background: rgba(0,200,83,0.07);
  color: #2e7d32;
}

.meter-range {
  display: inline-block;
  padding: 1px 4px;
  border-radius: 2px;
  background: #f0f0f0;
}

.ammeter-container .meter-range {
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.voltmeter-container .meter-range {
  border: 1px solid rgba(40, 167, 69, 0.2);
}

/* 更新动画 */
@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.9; transform: scale(1.03); }
  100% { opacity: 1; transform: scale(1); }
}

.updating {
  animation: pulse 0.5s ease;
}

/* 连接线 */
.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(to right, #ff7043, #ff5722);
  transform-origin: 0 0;
  z-index: 800;
  pointer-events: none;
  box-shadow: 0 1px 1px rgba(255,87,34,0.2);
  border-radius: 1px;
}

/* 连接状态视觉反馈 */
.connection-in-progress .connection-point:not(.connected):hover {
  border-color: #ff5722;
  box-shadow: 0 0 0 1px rgba(255,87,34,0.2);
}

/* 通知样式 */
.notification {
  position: fixed;
  bottom: 12px;
  right: 12px;
  padding: 6px 10px 6px 8px;
  background: #ffffff;
  color: #333333;
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  z-index: 1000;
  font-size: 11px;
  transform: translateY(50px);
  opacity: 0;
  transition: all 0.25s ease;
  border-left: 2px solid #2979ff;
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 220px;
}

.notification.visible {
  transform: translateY(0);
  opacity: 1;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon .material-icons-round {
  font-size: 14px;
  color: #2979ff;
}

.notification.success {
  border-left-color: #00c853;
}

.notification.success .notification-icon .material-icons-round {
  color: #00c853;
}

.notification.warning {
  border-left-color: #ff9800;
}

.notification.warning .notification-icon .material-icons-round {
  color: #ff9800;
}

.notification.error {
  border-left-color: #f44336;
}

.notification.error .notification-icon .material-icons-round {
  color: #f44336;
}

/* 添加响应式模式 */
@media (max-width: 768px) {
  .meter-container {
    width: 110px;
  }
  
  .meter-value {
    font-size: 14px;
  }
}

/* 进度条 */
.meter-progress {
  height: 2px;
  margin: 2px 0;
  background: #eee;
  border-radius: 1px;
  overflow: hidden;
}

.meter-progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.ammeter-container .meter-progress-bar {
  background-color: #007bff;
}

.voltmeter-container .meter-progress-bar {
  background-color: #28a745;
} 