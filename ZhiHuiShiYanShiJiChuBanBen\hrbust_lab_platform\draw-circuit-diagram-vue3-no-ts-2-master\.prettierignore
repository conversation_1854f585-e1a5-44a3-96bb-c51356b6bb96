# 忽略构建相关的文件夹和文件
build
dist
out
.nyc_output

# 忽略依赖文件夹
node_modules
yarn.lock
package-lock.json

# 忽略特定的配置文件（如果不想被Prettier处理）
*.config.js
*.config.cjs
*.config.ts
*.env
*.env.*

# 忽略日志文件
*.log
*.log.*

# 忽略操作系统相关的隐藏文件和文件夹
.DS_Store
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 忽略数据库相关文件（如果有）
*.db
*.sqlite
*.sqlite3

# 忽略测试相关文件
*.test.js
*.test.jsx
*.test.ts
*.test.tsx
*.spec.js
*.spec.jsx
*.spec.ts
*.spec.tsx
test-results.xml
jest.config.js
jest.config.cjs
jest.config.ts

# 忽略文档文件（如果不想被Prettier处理）
*.md
*.markdown
*.txt
LICENSE
README.*

# 忽略本地化相关文件（如果有）
*.po
*.mo

# 忽略二进制文件和特定类型的资源文件
*.bin
*.woff
*.woff2
*.ttf
*.eot
*.otf
*.pdf
*.psd
*.ai
*.svg
*.ico
*.png
*.jpg
*.jpeg
*.gif
*.mp4
*.mov
*.avi
*.mp3
*.wav
*.ogg