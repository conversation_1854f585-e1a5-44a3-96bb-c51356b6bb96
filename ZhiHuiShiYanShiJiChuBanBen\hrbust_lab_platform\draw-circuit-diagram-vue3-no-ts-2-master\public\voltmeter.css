/* 电压表组件样式 - 现代扁平化设计 */
.voltmeter-component {
  width: 200px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
  border-left: 4px solid #28a745;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: all 0.3s ease;
}

.voltmeter-component:hover {
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.25);
  transform: translateY(-2px);
}

/* 头部样式 */
.voltmeter-header {
  background: linear-gradient(to right, #e6fff0, #f8fff8);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6fff0;
}

.voltmeter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #156829;
}

.voltmeter-icon {
  color: #28a745;
  font-size: 16px;
}

.voltmeter-drag-handle {
  color: #8cd98e;
  cursor: move;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.voltmeter-drag-handle:hover {
  background-color: rgba(40, 167, 69, 0.1);
}

/* 主体内容样式 */
.voltmeter-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.voltmeter-display {
  width: 100%;
  background-color: #f8fff8;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
  text-align: center;
  border: 1px solid #e6fff0;
}

.voltmeter-value {
  font-size: 28px;
  font-weight: 700;
  color: #28a745;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.voltmeter-unit {
  color: #2fb750;
  font-size: 14px;
  margin-top: 5px;
}

.voltmeter-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6c757d;
  margin-top: 10px;
}

.voltmeter-range {
  background-color: rgba(40, 167, 69, 0.08);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.voltmeter-label {
  font-style: italic;
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

.voltmeter-value.updating {
  animation: pulse 1.5s infinite;
} 