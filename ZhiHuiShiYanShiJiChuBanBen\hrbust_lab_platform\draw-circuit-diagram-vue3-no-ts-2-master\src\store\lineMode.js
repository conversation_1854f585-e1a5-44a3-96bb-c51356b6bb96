import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLineModeStore = defineStore('lineMode', () => {
  let lineMode = ref('disabled') // 默认模式
  const lineModeText = ref('关闭连线模式') // 默认文字

  const setLineMode = (command) => {
    lineMode.value = command
    if (command === 'vertical-first') {
      lineModeText.value = '垂直优先'
    } else if (command === 'horizontal-first') {
      lineModeText.value = '水平优先'
    } else if (command === 'smart') {
      lineModeText.value = '智能画线'
    } else if (command === 'disabled') {
      lineModeText.value = '关闭连线模式'
    }
  }

  const isLineDrawing = computed(() => lineMode.value !== 'disabled')

  return { lineMode, lineModeText, isLineDrawing, setLineMode }
})
