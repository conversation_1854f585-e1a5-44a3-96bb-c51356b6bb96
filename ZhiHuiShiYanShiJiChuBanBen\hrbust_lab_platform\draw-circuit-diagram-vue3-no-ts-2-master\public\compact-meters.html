<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能电路仪表 - 现代化测量界面</title>
  <link rel="stylesheet" href="compact-meters.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="meters-drag.js"></script>
  <style>
    /* 现代设计风格 - 浅色格调 */
    * {
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      padding: 24px;
      min-height: 100vh;
      margin: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      position: relative;
      color: #334155;
    }

    .workspace {
      width: 100%;
      height: 100vh;
      position: relative;
      background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%);
    }

    /* 现代化仪表设计 */
    .meter-container {
      position: absolute;
      z-index: 100;
    }

    .ammeter-component, .voltmeter-component {
      width: 180px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: absolute;
    }

    .ammeter-component:hover, .voltmeter-component:hover {
      transform: translateY(-2px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    /* 头部样式 */
    .ammeter-header, .voltmeter-header {
      padding: 10px 15px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid rgba(226, 232, 240, 0.6);
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
    }

    .ammeter-title, .voltmeter-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 600;
      font-size: 13px;
      color: #475569;
    }

    .ammeter-icon, .voltmeter-icon {
      font-size: 16px !important;
      color: #3b82f6;
    }

    .voltmeter-icon {
      color: #10b981 !important;
    }

    .ammeter-drag-handle, .voltmeter-drag-handle {
      font-size: 14px !important;
      color: #94a3b8;
      opacity: 0.6;
      transition: opacity 0.2s ease;
    }

    .ammeter-header:hover .ammeter-drag-handle,
    .voltmeter-header:hover .voltmeter-drag-handle {
      opacity: 1;
    }

    /* 内容区域 */
    .ammeter-content, .voltmeter-content {
      padding: 15px;
    }

    .ammeter-display, .voltmeter-display {
      text-align: center;
      margin-bottom: 12px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 8px;
      padding: 16px 12px;
      border: 1px solid rgba(226, 232, 240, 0.6);
      position: relative;
      overflow: hidden;
    }

    .ammeter-display::before, .voltmeter-display::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #3b82f6, #10b981);
      opacity: 0.6;
    }

    .voltmeter-display::before {
      background: linear-gradient(90deg, #10b981, #3b82f6);
    }

    .ammeter-value, .voltmeter-value {
      font-size: 28px;
      font-weight: 700;
      font-family: 'Roboto Mono', monospace;
      color: #1e293b;
      margin-bottom: 6px;
      transition: all 0.3s ease;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .ammeter-value.updating, .voltmeter-value.updating {
      transform: scale(1.05);
      color: #3b82f6;
    }

    .ammeter-unit, .voltmeter-unit {
      font-size: 11px;
      font-weight: 600;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.8px;
      opacity: 0.8;
    }

    .ammeter-info, .voltmeter-info {
      border-top: 1px solid rgba(226, 232, 240, 0.6);
      padding-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .ammeter-label, .voltmeter-label {
      font-size: 10px;
      font-weight: 600;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      flex: 1;
    }

    .ammeter-range, .voltmeter-range {
      font-size: 9px;
      color: #cbd5e1;
      font-weight: 400;
      opacity: 0.7;
      /* 可选：完全隐藏量程显示 */
      /* display: none; */
    }

    /* 特定颜色主题 */
    .ammeter-component {
      border-left: 3px solid #3b82f6;
    }

    .voltmeter-component {
      border-left: 3px solid #10b981;
    }

    /* 标识符样式 */
    .ammeter-title small, .voltmeter-title small {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6 !important;
      padding: 1px 4px;
      border-radius: 4px;
      font-size: 9px !important;
      font-weight: 600;
      margin-left: 6px !important;
    }

    .voltmeter-title small {
      background: rgba(16, 185, 129, 0.1) !important;
      color: #10b981 !important;
    }

    /* 接入点样式 */
    .connection-points {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 12px;
      background: rgba(248, 250, 252, 0.8);
      border-top: 1px solid rgba(226, 232, 240, 0.6);
      margin-top: 6px;
    }

    .connection-point {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      border: 2px solid #64748b;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }

    .connection-point:hover {
      transform: scale(1.2);
      border-color: #3b82f6;
      box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
    }

    .connection-point.positive {
      border-color: #dc2626;
      background: #fef2f2;
    }

    .connection-point.negative {
      border-color: #1f2937;
      background: #f9fafb;
    }

    .connection-point.positive:hover {
      border-color: #dc2626;
      box-shadow: 0 0 8px rgba(220, 38, 38, 0.3);
    }

    .connection-point.negative:hover {
      border-color: #1f2937;
      box-shadow: 0 0 8px rgba(31, 41, 55, 0.3);
    }

    .connection-label {
      font-size: 9px;
      color: #64748b;
      font-weight: 500;
      margin-top: 3px;
      text-align: center;
    }

    .connection-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
    }

    /* 连接线样式 */
    .connection-wire {
      position: absolute;
      height: 2px;
      background: linear-gradient(90deg, #3b82f6, #10b981);
      border-radius: 1px;
      z-index: 50;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .connection-wire.active {
      opacity: 1;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.6; }
      50% { opacity: 1; }
    }

    /* 悬浮提示样式 */
    .connection-tooltip {
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
      z-index: 10000;
      pointer-events: none;
      opacity: 0;
      transform: translateY(-5px);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .connection-tooltip.show {
      opacity: 1;
      transform: translateY(0);
    }

    .connection-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 3px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.9);
    }

    /* 正负极特定提示样式 */
    .connection-tooltip.positive {
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.95), rgba(185, 28, 28, 0.95));
      border-color: rgba(220, 38, 38, 0.3);
    }

    .connection-tooltip.positive::after {
      border-top-color: rgba(220, 38, 38, 0.95);
    }

    .connection-tooltip.negative {
      background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(17, 24, 39, 0.95));
      border-color: rgba(31, 41, 55, 0.3);
    }

    .connection-tooltip.negative::after {
      border-top-color: rgba(31, 41, 55, 0.95);
    }

    /* 示波器样式 - 参考syhj页面 */
    .oscilloscope {
      position: absolute;
      width: 180px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .oscilloscope:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    }

    .oscilloscope-header {
      padding: 10px 15px;
      background: #f5f5f5;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    }

    .oscilloscope-title {
      font-weight: 600;
      font-size: 13px;
      color: #475569;
    }

    .oscilloscope-content {
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .oscilloscope-channel {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 0;
    }

    .probe {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid #64748b;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }

    .probe:hover {
      transform: scale(1.2);
      border-color: #3b82f6;
      box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
    }

    .probe-1 {
      border-color: #ef4444;
      background: #fef2f2;
    }

    .probe-2 {
      border-color: #3b82f6;
      background: #eff6ff;
    }

    .probe-0 {
      border-color: #1f2937;
      background: #f9fafb;
    }

    .channel-title {
      font-size: 12px;
      font-weight: 500;
      color: #64748b;
      min-width: 50px;
    }

    .channel-value {
      font-size: 11px;
      color: #94a3b8;
      font-weight: 500;
      flex: 1;
    }

    /* 悬浮提示样式 */
    .connection-tooltip {
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 6px 10px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
      z-index: 10000;
      pointer-events: none;
      opacity: 0;
      transform: translateY(-5px);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .connection-tooltip.show {
      opacity: 1;
      transform: translateY(0);
    }

    .connection-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.9);
    }

    /* 正负极特定提示样式 */
    .connection-tooltip.positive {
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.95), rgba(185, 28, 28, 0.95));
      border-color: rgba(220, 38, 38, 0.3);
    }

    .connection-tooltip.positive::after {
      border-top-color: rgba(220, 38, 38, 0.95);
    }

    .connection-tooltip.negative {
      background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(17, 24, 39, 0.95));
      border-color: rgba(31, 41, 55, 0.3);
    }

    .connection-tooltip.negative::after {
      border-top-color: rgba(31, 41, 55, 0.95);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .ammeter-component, .voltmeter-component, .oscilloscope {
        width: 160px;
      }

      body {
        padding: 16px;
      }

      .ammeter-value, .voltmeter-value {
        font-size: 22px;
      }

      .ammeter-display, .voltmeter-display {
        padding: 12px 8px;
      }
    }
  </style>
</head>
<body>
  <div class="workspace" id="workspace">
    <!-- 现代化电流表 -->
    <div class="ammeter-component" id="ammeter-1" style="top: 120px; left: 120px;">
      <div class="ammeter-header" id="ammeter-header-1">
        <div class="ammeter-title">
          <span class="material-icons-round ammeter-icon">electric_bolt</span>
          <span>电流表</span>
          <small>A1</small>
        </div>
        <span class="material-icons-round ammeter-drag-handle">drag_indicator</span>
      </div>

      <div class="ammeter-content">
        <div class="ammeter-display">
          <div class="ammeter-value" id="ammeter-value-1">0.09</div>
          <div class="ammeter-unit">安培 (A)</div>
        </div>

        <div class="ammeter-info">
          <div class="ammeter-label">电流测量</div>
          <div class="ammeter-range">量程: 0-10A</div>
        </div>

        <!-- 电流表接入点 -->
        <div class="connection-points">
          <div class="connection-group">
            <div class="connection-point negative" id="ammeter-negative" data-type="ammeter" data-polarity="negative"></div>
            <div class="connection-label">负极</div>
          </div>
          <div class="connection-group">
            <div class="connection-point positive" id="ammeter-positive" data-type="ammeter" data-polarity="positive"></div>
            <div class="connection-label">正极</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化电压表 -->
    <div class="voltmeter-component" id="voltmeter-1" style="top: 120px; right: 120px;">
      <div class="voltmeter-header" id="voltmeter-header-1">
        <div class="voltmeter-title">
          <span class="material-icons-round voltmeter-icon">bolt</span>
          <span>电压表</span>
          <small>V1</small>
        </div>
        <span class="material-icons-round voltmeter-drag-handle">drag_indicator</span>
      </div>

      <div class="voltmeter-content">
        <div class="voltmeter-display">
          <div class="voltmeter-value" id="voltmeter-value-1">10.13</div>
          <div class="voltmeter-unit">伏特 (V)</div>
        </div>

        <div class="voltmeter-info">
          <div class="voltmeter-label">电压测量</div>
          <div class="voltmeter-range">量程: 0-30V</div>
        </div>

        <!-- 电压表接入点 -->
        <div class="connection-points">
          <div class="connection-group">
            <div class="connection-point negative" id="voltmeter-negative" data-type="voltmeter" data-polarity="negative"></div>
            <div class="connection-label">负极</div>
          </div>
          <div class="connection-group">
            <div class="connection-point positive" id="voltmeter-positive" data-type="voltmeter" data-polarity="positive"></div>
            <div class="connection-label">正极</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 示波器组件 - 参考syhj页面 -->
    <div class="oscilloscope" id="oscilloscope" style="top: 120px; left: 400px;">
      <div class="oscilloscope-header" id="oscilloscope-header">
        <div class="oscilloscope-title">示波器</div>
        <span class="material-icons-round" style="font-size: 14px; color: #94a3b8;">drag_indicator</span>
      </div>

      <div class="oscilloscope-content">
        <div class="oscilloscope-channel">
          <div class="probe probe-1" id="probe-1" data-channel="1"></div>
          <div class="channel-title">通道 1</div>
          <div class="channel-value" id="channel-1-value">未连接</div>
        </div>

        <div class="oscilloscope-channel">
          <div class="probe probe-2" id="probe-2" data-channel="2"></div>
          <div class="channel-title">通道 2</div>
          <div class="channel-value" id="channel-2-value">未连接</div>
        </div>

        <div class="oscilloscope-channel">
          <div class="probe probe-0" id="probe-0" data-channel="0"></div>
          <div class="channel-title">地</div>
          <div class="channel-value" id="channel-0-value">未连接</div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // 现代化仪表管理系统
    class MeterManager {
      constructor() {
        this.connections = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        this.connectionWires = [];
        this.selectedConnection = null;
        this.isConnecting = false;
      }

      // 平滑更新仪表值
      updateMeterValue(type, value, animated = true) {
        const valueId = `${type}-value-1`;
        const valueElement = document.getElementById(valueId);

        if (!valueElement) return;

        if (animated) {
          // 添加现代化动画效果
          valueElement.style.transform = 'scale(1.05)';
          valueElement.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

          setTimeout(() => {
            valueElement.textContent = value;
            valueElement.style.transform = 'scale(1)';

            // 添加闪烁效果
            valueElement.style.color = type === 'ammeter' ? '#3b82f6' : '#10b981';
            setTimeout(() => {
              valueElement.style.color = '#1e293b';
            }, 500);
          }, 150);
        } else {
          valueElement.textContent = value;
        }
      }

      // 生成真实感数据
      generateRealisticData(type) {
        const baseValues = {
          ammeter: { min: 0.01, max: 8.5, precision: 3 },
          voltmeter: { min: 0.1, max: 24.8, precision: 2 }
        };

        const config = baseValues[type];
        const value = (Math.random() * (config.max - config.min) + config.min);
        return value.toFixed(config.precision);
      }

      // 初始化现代化交互
      initModernInteractions() {
        // 添加悬停效果
        document.querySelectorAll('.ammeter-component, .voltmeter-component').forEach(meter => {
          meter.addEventListener('mouseenter', () => {
            meter.style.transform = 'translateY(-2px) scale(1.02)';
          });

          meter.addEventListener('mouseleave', () => {
            meter.style.transform = 'translateY(0) scale(1)';
          });
        });

        // 添加点击波纹效果
        document.querySelectorAll('.ammeter-header, .voltmeter-header').forEach(header => {
          header.addEventListener('click', (e) => {
            this.createRippleEffect(e, header);
          });
        });

        // 初始化接入点交互
        this.initConnectionPoints();

        // 初始化示波器拖拽
        this.initOscilloscopeDrag();
      }

      // 初始化接入点功能
      initConnectionPoints() {
        const connectionPoints = document.querySelectorAll('.connection-point');

        connectionPoints.forEach(point => {
          // 点击接入点
          point.addEventListener('click', (e) => {
            e.stopPropagation();
            this.handleConnectionClick(point);
          });

          // 悬停效果和提示
          point.addEventListener('mouseenter', (e) => {
            if (!this.isConnecting) {
              point.style.transform = 'scale(1.3)';
              point.style.zIndex = '1000';
            }
            this.showConnectionTooltip(e, point);
          });

          point.addEventListener('mouseleave', () => {
            if (!this.isConnecting) {
              point.style.transform = 'scale(1)';
              point.style.zIndex = 'auto';
            }
            this.hideConnectionTooltip();
          });
        });

        console.log('🔌 接入点系统初始化完成');
      }

      // 处理接入点点击
      handleConnectionClick(point) {
        const type = point.dataset.type;
        const polarity = point.dataset.polarity;
        const pointId = point.id;

        if (!this.isConnecting) {
          // 开始连接
          this.selectedConnection = { point, type, polarity, pointId };
          this.isConnecting = true;
          point.style.transform = 'scale(1.4)';
          point.style.boxShadow = '0 0 12px rgba(59, 130, 246, 0.6)';
          console.log(`🔌 选择了 ${type} 的 ${polarity} 接入点`);

          // 显示连接提示
          this.showConnectionHint('请点击另一个接入点完成连接');
        } else {
          // 完成连接
          if (this.selectedConnection && this.selectedConnection.pointId !== pointId) {
            this.createConnection(this.selectedConnection, { point, type, polarity, pointId });
          }
          this.resetConnectionState();
        }
      }

      // 创建连接
      createConnection(from, to) {
        const connectionId = `${from.pointId}-${to.pointId}`;

        // 检查是否已存在连接
        if (this.connections.has(connectionId)) {
          this.showConnectionHint('此连接已存在');
          return;
        }

        // 创建连接线
        const wire = this.createConnectionWire(from.point, to.point);

        // 保存连接信息
        this.connections.set(connectionId, {
          from: from,
          to: to,
          wire: wire,
          active: true
        });

        console.log(`✅ 创建连接: ${from.type}(${from.polarity}) -> ${to.type}(${to.polarity})`);
        this.showConnectionHint(`连接成功: ${from.type} -> ${to.type}`);

        // 更新连接状态显示
        this.updateConnectionStatus();
      }

      // 创建连接线
      createConnectionWire(fromPoint, toPoint) {
        const wire = document.createElement('div');
        wire.className = 'connection-wire active';

        // 计算连接线位置和长度
        const fromRect = fromPoint.getBoundingClientRect();
        const toRect = toPoint.getBoundingClientRect();
        const workspaceRect = document.getElementById('workspace').getBoundingClientRect();

        const fromX = fromRect.left + fromRect.width / 2 - workspaceRect.left;
        const fromY = fromRect.top + fromRect.height / 2 - workspaceRect.top;
        const toX = toRect.left + toRect.width / 2 - workspaceRect.left;
        const toY = toRect.top + toRect.height / 2 - workspaceRect.top;

        const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
        const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;

        wire.style.cssText = `
          left: ${fromX}px;
          top: ${fromY}px;
          width: ${length}px;
          transform: rotate(${angle}deg);
          transform-origin: 0 50%;
        `;

        document.getElementById('workspace').appendChild(wire);
        this.connectionWires.push(wire);

        return wire;
      }

      // 重置连接状态
      resetConnectionState() {
        if (this.selectedConnection) {
          this.selectedConnection.point.style.transform = 'scale(1)';
          this.selectedConnection.point.style.boxShadow = '';
        }
        this.selectedConnection = null;
        this.isConnecting = false;
      }

      // 显示连接提示
      showConnectionHint(message) {
        // 创建提示元素
        const hint = document.createElement('div');
        hint.textContent = message;
        hint.style.cssText = `
          position: fixed;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(59, 130, 246, 0.9);
          color: white;
          padding: 8px 16px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          z-index: 10000;
          backdrop-filter: blur(10px);
          animation: fadeInOut 3s ease-in-out;
        `;

        // 添加动画样式
        if (!document.getElementById('hint-style')) {
          const style = document.createElement('style');
          style.id = 'hint-style';
          style.textContent = `
            @keyframes fadeInOut {
              0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
              20%, 80% { opacity: 1; transform: translateX(-50%) translateY(0); }
              100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
            }
          `;
          document.head.appendChild(style);
        }

        document.body.appendChild(hint);

        setTimeout(() => {
          hint.remove();
        }, 3000);
      }

      // 更新连接状态
      updateConnectionStatus() {
        const statusText = `当前连接数: ${this.connections.size}`;
        console.log(`📊 ${statusText}`);
      }

      // 显示接入点提示
      showConnectionTooltip(event, point) {
        const type = point.dataset.type;
        const polarity = point.dataset.polarity;

        // 移除现有提示
        this.hideConnectionTooltip();

        // 创建提示元素
        const tooltip = document.createElement('div');
        tooltip.className = `connection-tooltip ${polarity}`;
        tooltip.id = 'connection-tooltip';

        // 设置提示文本
        const typeText = type === 'ammeter' ? '电流表' : '电压表';
        const polarityText = polarity === 'positive' ? '正极' : '负极';
        tooltip.textContent = `${typeText} - ${polarityText}`;

        // 计算位置
        const rect = point.getBoundingClientRect();
        const tooltipX = rect.left + rect.width / 2;
        const tooltipY = rect.top - 8;

        tooltip.style.left = `${tooltipX}px`;
        tooltip.style.top = `${tooltipY}px`;
        tooltip.style.transform = 'translateX(-50%) translateY(-100%)';

        document.body.appendChild(tooltip);

        // 显示动画
        setTimeout(() => {
          tooltip.classList.add('show');
        }, 10);
      }

      // 隐藏接入点提示
      hideConnectionTooltip() {
        const existingTooltip = document.getElementById('connection-tooltip');
        if (existingTooltip) {
          existingTooltip.classList.remove('show');
          setTimeout(() => {
            if (existingTooltip.parentNode) {
              existingTooltip.parentNode.removeChild(existingTooltip);
            }
          }, 200);
        }
      }

      // 初始化示波器拖拽功能
      initOscilloscopeDrag() {
        const oscilloscope = document.getElementById('oscilloscope');
        const header = document.getElementById('oscilloscope-header');

        if (!oscilloscope || !header) return;

        let isDragging = false;
        let currentX, currentY, initialX, initialY;
        let xOffset = 0, yOffset = 0;

        header.addEventListener('mousedown', (e) => {
          isDragging = true;
          initialX = e.clientX - xOffset;
          initialY = e.clientY - yOffset;
          e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
          if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
            xOffset = currentX;
            yOffset = currentY;
            oscilloscope.style.transform = `translate(${currentX}px, ${currentY}px)`;
          }
        });

        document.addEventListener('mouseup', () => {
          if (isDragging) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
          }
        });

        // 添加探头悬停提示
        const probes = document.querySelectorAll('.probe');
        probes.forEach(probe => {
          probe.addEventListener('mouseenter', (e) => {
            const channel = probe.dataset.channel;
            let channelName = '';
            switch(channel) {
              case '1': channelName = '示波器 - 通道1'; break;
              case '2': channelName = '示波器 - 通道2'; break;
              case '0': channelName = '示波器 - 地线'; break;
            }
            this.showProbeTooltip(e, probe, channelName);
          });

          probe.addEventListener('mouseleave', () => {
            this.hideConnectionTooltip();
          });
        });

        console.log('📺 示波器拖拽功能初始化完成');
      }

      // 显示探头提示
      showProbeTooltip(event, probe, text) {
        // 移除现有提示
        this.hideConnectionTooltip();

        // 创建提示元素
        const tooltip = document.createElement('div');
        tooltip.className = 'connection-tooltip';
        tooltip.id = 'connection-tooltip';
        tooltip.textContent = text;

        // 计算位置
        const rect = probe.getBoundingClientRect();
        const tooltipX = rect.left + rect.width / 2;
        const tooltipY = rect.top - 8;

        tooltip.style.left = `${tooltipX}px`;
        tooltip.style.top = `${tooltipY}px`;
        tooltip.style.transform = 'translateX(-50%) translateY(-100%)';

        document.body.appendChild(tooltip);

        // 显示动画
        setTimeout(() => {
          tooltip.classList.add('show');
        }, 10);
      }

      // 清除所有连接
      clearAllConnections() {
        // 移除所有连接线
        this.connectionWires.forEach(wire => {
          if (wire && wire.parentNode) {
            wire.parentNode.removeChild(wire);
          }
        });

        // 清空数据
        this.connections.clear();
        this.connectionWires = [];
        this.resetConnectionState();

        this.showConnectionHint('所有连接已清除');
        this.updateConnectionStatus();
      }

      // 清除所有连接
      clearAllConnections() {
        // 移除所有连接线
        this.connectionWires.forEach(wire => {
          if (wire && wire.parentNode) {
            wire.parentNode.removeChild(wire);
          }
        });

        // 清空数据
        this.connections.clear();
        this.connectionWires = [];
        this.resetConnectionState();

        this.showConnectionHint('所有连接已清除');
        this.updateConnectionStatus();
      }

      // 创建波纹效果
      createRippleEffect(event, element) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s ease-out;
          pointer-events: none;
          z-index: 1000;
        `;

        // 添加动画样式
        if (!document.getElementById('ripple-style')) {
          const style = document.createElement('style');
          style.id = 'ripple-style';
          style.textContent = `
            @keyframes ripple {
              to {
                transform: scale(2);
                opacity: 0;
              }
            }
          `;
          document.head.appendChild(style);
        }

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      }
    }

    // 全局仪表管理器
    const meterManager = new MeterManager();

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 初始化现代化仪表系统...');

      // 初始化拖拽功能
      if (typeof initAllMetersDrag === 'function') {
        initAllMetersDrag();
      }

      // 初始化现代化交互
      meterManager.initModernInteractions();

      // 显示欢迎提示
      setTimeout(() => {
        meterManager.showConnectionHint('欢迎使用智能电路仪表！点击接入点可创建连接');
      }, 1000);

      // 智能数据更新
      let ammeterInterval = setInterval(() => {
        const value = meterManager.generateRealisticData('ammeter');
        meterManager.updateMeterValue('ammeter', value);
      }, 2500 + Math.random() * 1000);

      let voltmeterInterval = setInterval(() => {
        const value = meterManager.generateRealisticData('voltmeter');
        meterManager.updateMeterValue('voltmeter', value);
      }, 3000 + Math.random() * 1500);

      // 添加键盘快捷键
      document.addEventListener('keydown', (e) => {
        if (e.key === 'r' && e.ctrlKey) {
          e.preventDefault();
          // 重置所有组件位置
          document.getElementById('ammeter-1').style.cssText = 'top: 120px; left: 120px;';
          document.getElementById('voltmeter-1').style.cssText = 'top: 120px; right: 120px;';
          document.getElementById('oscilloscope').style.cssText = 'top: 120px; left: 400px;';
          document.getElementById('oscilloscope').style.transform = '';
          console.log('📍 所有组件位置已重置');
        }

        if (e.key === 'c' && e.ctrlKey) {
          e.preventDefault();
          // 清除所有连接
          meterManager.clearAllConnections();
          console.log('🧹 所有连接已清除');
        }

        if (e.key === 'Escape') {
          // 取消当前连接操作
          meterManager.resetConnectionState();
          meterManager.showConnectionHint('连接操作已取消');
        }
      });

      console.log('✅ 现代化仪表系统初始化完成');
      console.log('📖 使用说明:');
      console.log('   🖱️  拖拽组件头部可移动位置');
      console.log('   🔌  点击接入点可创建连接');
      console.log('   📺  悬停探头查看通道信息');
      console.log('   ⌨️  Ctrl+R 重置所有位置');
      console.log('   ⌨️  Ctrl+C 清除所有连接');
      console.log('   ⌨️  Esc 取消连接操作');
      console.log('📊 组件尺寸:');
      console.log('   📏  电流表/电压表: 180px (与示波器一致)');
      console.log('   📺  示波器: 180px (标准型)');
      console.log('   ✨  数值显示区域已美化优化');
    });
  </script>
</body>
</html> 