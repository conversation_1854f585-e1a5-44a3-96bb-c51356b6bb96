import { defineStore, storeToRefs } from 'pinia'
import { computed, toRaw } from 'vue'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { useLineStateStore } from '@/store/lineState'
import { useTextBoxStore } from '@/store/textBox'

export const useCurrentUserCircuitDataStore = defineStore('currentUserCircuitData', () => {
  const componentsInfoStore = useComponentsInfoStore()
  const lineStateStore = useLineStateStore()
  const textBoxStore = useTextBoxStore()
  const { components } = storeToRefs(componentsInfoStore)
  const { fixedLines, intersections } = storeToRefs(lineStateStore)
  const { textBoxs } = storeToRefs(textBoxStore)

  // 当前用户电路图数据
  const currentUserCircuitData = computed(() => {
    // 🔧 修复：安全访问响应式引用，防止删除连线时的 undefined 错误
    try {
      return {
        components: toRaw(components?.value || []),
        fixedLines: toRaw(fixedLines?.value || []),
        intersections: toRaw(intersections?.value || []),
        textBoxs: toRaw(textBoxs?.value || []),
      }
    } catch (error) {
      console.warn('⚠️ 获取电路数据时出错，返回默认值:', error)
      return {
        components: [],
        fixedLines: [],
        intersections: [],
        textBoxs: [],
      }
    }
  })

  // 更新当前用户电路图数据
  const updateCurrentCircuitData = (newData) => {
    // 使用 Object.assign 保持响应式
    Object.assign(currentUserCircuitData, newData)
  }

  return { currentUserCircuitData, updateCurrentCircuitData }
})
