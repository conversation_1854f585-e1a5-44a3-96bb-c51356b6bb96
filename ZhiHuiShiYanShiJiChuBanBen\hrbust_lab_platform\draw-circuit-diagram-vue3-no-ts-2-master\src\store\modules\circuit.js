const state = {
  testPoints: []
}

const mutations = {
  ADD_TEST_POINT(state, point) {
    state.testPoints.push(point)
  },
  
  UPDATE_TEST_POINT(state, {id, x, y, otherData}) {
    const index = state.testPoints.findIndex(p => p.id === id)
    if (index !== -1) {
      state.testPoints[index] = {
        ...state.testPoints[index],
        x, 
        y,
        ...otherData
      }
    }
  },
  
  REMOVE_TEST_POINT(state, id) {
    state.testPoints = state.testPoints.filter(p => p.id !== id)
  }
} 