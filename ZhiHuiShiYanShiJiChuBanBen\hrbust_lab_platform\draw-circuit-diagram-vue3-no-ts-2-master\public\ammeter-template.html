<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电流表组件</title>
  <link rel="stylesheet" href="ammeter.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body style="background-color: #f5f5f5; padding: 20px; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0;">
  
  <!-- 电流表组件 -->
  <div class="ammeter-component" id="ammeter-1">
    <div class="ammeter-header" id="ammeter-header-1">
      <div class="ammeter-title">
        <span class="material-icons ammeter-icon">electric_bolt</span>
        <span>电流表</span>
        <small style="margin-left: 5px; font-size: 11px; color: #6c757d;">A1</small>
      </div>
      <span class="material-icons ammeter-drag-handle">drag_indicator</span>
    </div>
    
    <div class="ammeter-content">
      <div class="ammeter-display">
        <div class="ammeter-value" id="ammeter-value-1">0.00</div>
        <div class="ammeter-unit">安培 (A)</div>
      </div>
      
      <div class="ammeter-info">
        <div class="ammeter-label">电流测量</div>
        <div class="ammeter-range">量程: 0-10A</div>
      </div>
    </div>
  </div>

  <script>
    // 使元素可拖拽的函数
    function makeElementDraggable(elementId, handleId) {
      const element = document.getElementById(elementId);
      const handle = document.getElementById(handleId);
      
      if (!element || !handle) return;
      
      let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
      
      handle.onmousedown = dragMouseDown;
      
      function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();
        
        // 获取鼠标初始位置
        pos3 = e.clientX;
        pos4 = e.clientY;
        
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
      }
      
      function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();
        
        // 计算新位置
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        
        // 设置元素新位置
        element.style.top = (element.offsetTop - pos2) + "px";
        element.style.left = (element.offsetLeft - pos1) + "px";
      }
      
      function closeDragElement() {
        // 停止拖拽
        document.onmouseup = null;
        document.onmousemove = null;
      }
    }

    // 初始化拖拽功能
    document.addEventListener('DOMContentLoaded', function() {
      makeElementDraggable("ammeter-1", "ammeter-header-1");
      
      // 模拟数值变化的演示功能
      setInterval(function() {
        const value = (Math.random() * 5).toFixed(2);
        const valueElement = document.getElementById('ammeter-value-1');
        valueElement.classList.add('updating');
        
        setTimeout(function() {
          valueElement.textContent = value;
          valueElement.classList.remove('updating');
        }, 500);
      }, 3000);
    });
  </script>
</body>
</html> 