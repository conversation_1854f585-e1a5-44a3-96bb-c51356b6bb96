<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旋钮测试页面</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .knobs-container {
            display: flex;
            justify-content: space-around;
            width: 100%;
            flex-wrap: wrap;
            gap: 80px;
            margin-bottom: 30px;
        }
        
        .knob-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .knob-title {
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }
        
        /* 旋钮式电位器 */
        .potentiometer-knob {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .knob {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
            border: 8px solid #ddd;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            user-select: none;
            --rotation: 0deg;
        }
        
        .knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 40%;
            background-color: #555;
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(var(--rotation));
            border-radius: 4px;
        }
        
        .knob-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .knob-value {
            font-size: 14px;
            color: #666;
            background-color: #fff;
            padding: 4px 10px;
            border-radius: 15px;
            border: 1px solid #ddd;
            min-width: 60px;
            text-align: center;
        }
        
        /* LFO旋钮样式 */
        .lfo-knob {
            background: #1a1a1a;
            border: 2px solid #000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8), inset 0 1px 3px rgba(255, 255, 255, 0.2);
            --rotation: 0deg;
            width: 80px;
            height: 80px;
            position: relative;
        }
        
        /* 添加中心点 */
        .lfo-knob::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background-color: #5DADE2;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 4px rgba(93, 173, 226, 0.7);
        }
        
        /* 弧线样式 - 增加宽度 */
        .lfo-knob .arc-line {
            position: absolute;
            top: -10px;  /* 调整位置以适应更宽的弧线 */
            left: -10px;  /* 调整位置以适应更宽的弧线 */
            width: calc(100% + 20px);  /* 调整大小以适应更宽的弧线 */
            height: calc(100% + 20px);  /* 调整大小以适应更宽的弧线 */
            border-radius: 50%;
            border: 5px solid transparent;  /* 增加到5px */
            border-top: 5px solid #5DADE2;  /* 增加到5px */
            border-right: 5px solid #5DADE2;  /* 增加到5px */
            transform: rotate(-60deg);
            box-sizing: border-box;
            pointer-events: all;
            cursor: pointer;
            box-shadow: 0 0 6px rgba(93, 173, 226, 0.8);  /* 增加阴影效果 */
            clip-path: polygon(50% 50%, 0% 0%, 100% 0%, 100% 100%, 0% 100%);
        }
        
        /* 旋钮指针样式 - 增加宽度 */
        .lfo-knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #5DADE2;
            height: 45%;
            width: 4px;  /* 增加到4px */
            transform: translate(-50%, -100%) rotate(var(--rotation));
            transform-origin: bottom;
            border-radius: 2px;
            box-shadow: 0 0 5px rgba(93, 173, 226, 0.8);  /* 增加阴影效果 */
        }
        
        /* 滑动式电位器 */
        .potentiometer-slider {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .slider-container {
            width: 200px;
            height: 40px;
            background-color: #e0e0e0;
            border-radius: 20px;
            position: relative;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .slider-handle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background-color: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            user-select: none;
        }
        
        .slider-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .slider-value {
            font-size: 14px;
            color: #666;
            background-color: #fff;
            padding: 4px 10px;
            border-radius: 15px;
            border: 1px solid #ddd;
            min-width: 60px;
            text-align: center;
        }
        
        .debug-panel {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        
        #debug-output {
            font-family: monospace;
            white-space: pre-wrap;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>旋钮测试页面</h1>
        
        <div class="knobs-container">
            <div class="knob-section">
                <div class="knob-title">标准旋钮</div>
                <div class="potentiometer-knob">
                    <div class="knob" id="knob1"></div>
                    <div class="knob-label">电阻调节</div>
                    <div class="knob-value" id="knob1-value">50%</div>
                </div>
            </div>
            
            <div class="knob-section">
                <div class="knob-title">LFO旋钮</div>
                <div class="potentiometer-knob">
                    <div class="knob lfo-knob" id="lfo-knob">
                        <div class="arc-line"></div>
                    </div>
                    <div class="knob-label">LFO</div>
                    <div class="knob-value" id="lfo-value">0.1Hz</div>
                </div>
            </div>
            
            <div class="knob-section">
                <div class="knob-title">滑动电位器</div>
                <div class="potentiometer-slider">
                    <div class="slider-container" id="slider1">
                        <div class="slider-handle" id="slider1-handle"></div>
                    </div>
                    <div class="slider-label">电压调节</div>
                    <div class="slider-value" id="slider1-value">50%</div>
                </div>
            </div>
        </div>
        
        <div class="debug-panel">
            <h3>调试信息</h3>
            <div id="debug-output"></div>
        </div>
    </div>

    <script>
        // 调试输出函数
        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML = `[${timestamp}] ${message}\n` + debugOutput.innerHTML;
            console.log(message);
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，初始化旋钮...');
            
            // 初始化标准旋钮
            initKnobPotentiometer('knob1', 'knob1-value');
            
            // 初始化LFO旋钮
            setupLFOKnob();
            
            // 初始化滑动电位器
            initSliderPotentiometer('slider1', 'slider1-handle', 'slider1-value');
        });

        // 初始化标准旋钮电位器
        function initKnobPotentiometer(knobId, valueId) {
            const knob = document.getElementById(knobId);
            const valueDisplay = document.getElementById(valueId);
            let currentRotation = 150; // 初始角度
            let isDragging = false;
            let startX = 0;
            let startY = 0;
            
            log(`初始化标准旋钮: ${knobId}`);
            
            // 设置初始值
            updateKnobRotation(currentRotation);
            
            // 点击旋转
            knob.addEventListener('click', function(e) {
                // 如果是拖动结束的点击，不处理
                if (isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算点击位置相对于中心的角度
                const clickX = e.clientX - centerX;
                const clickY = e.clientY - centerY;
                
                // 计算角度（以12点钟方向为0度）
                let angle = Math.atan2(clickY, clickX) * 180 / Math.PI;
                angle = (angle + 90) % 360; // 调整为12点钟方向为0度
                if (angle < 0) angle += 360;
                
                // 限制在0-300度范围内
                if (angle > 300) angle = 300;
                
                log(`标准旋钮点击角度: ${angle.toFixed(1)}度`);
                updateKnobRotation(angle);
                
                // 防止事件冒泡
                e.stopPropagation();
            });
            
            // 拖动旋转 - 使用与LFO旋钮相同的向量角度差计算
            knob.addEventListener('mousedown', function(e) {
                isDragging = true;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算起始位置相对于中心的向量
                startX = e.clientX - centerX;
                startY = e.clientY - centerY;
                
                log('标准旋钮开始拖动');
                e.preventDefault();
                
                document.addEventListener('mousemove', onKnobDrag);
                document.addEventListener('mouseup', onKnobRelease);
            });
            
            function onKnobDrag(e) {
                if (!isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算当前位置相对于中心的向量
                const currentX = e.clientX - centerX;
                const currentY = e.clientY - centerY;
                
                // 计算起始向量和当前向量之间的角度差（弧度）
                const startAngle = Math.atan2(startY, startX);
                const currentAngle = Math.atan2(currentY, currentX);
                let angleDiff = currentAngle - startAngle;
                
                // 转换为度数
                angleDiff = angleDiff * 180 / Math.PI;
                
                // 处理角度跨越边界的情况
                if (angleDiff > 180) angleDiff -= 360;
                if (angleDiff < -180) angleDiff += 360;
                
                // 更新旋转角度
                const newDeg = currentRotation + angleDiff;
                updateKnobRotation(newDeg);
                
                // 更新起始位置
                startX = currentX;
                startY = currentY;
            }
            
            function onKnobRelease() {
                log('标准旋钮停止拖动');
                isDragging = false;
                document.removeEventListener('mousemove', onKnobDrag);
                document.removeEventListener('mouseup', onKnobRelease);
            }
            
            function updateKnobRotation(rotation) {
                // 限制旋转范围在0-300度之间
                rotation = Math.max(0, Math.min(300, rotation));
                currentRotation = rotation;
                
                // 设置CSS变量控制旋转
                knob.style.setProperty('--rotation', `${rotation}deg`);
                
                // 计算百分比值 (0-300度映射到0-100%)
                const percentage = Math.round((rotation / 300) * 100);
                valueDisplay.textContent = `${percentage}%`;
                
                log(`标准旋钮旋转至: ${rotation.toFixed(1)}度, ${percentage}%`);
            }
        }

        // 初始化LFO旋钮 - 使用与标准旋钮相同的拖动逻辑
        function setupLFOKnob() {
            const knob = document.getElementById('lfo-knob');
            const valueDisplay = document.getElementById('lfo-value');
            const arcLine = knob.querySelector('.arc-line');
            let isDragging = false;
            let startX = 0;
            let startY = 0;
            let currentRotation = -60; // 初始角度

            function updateLFORotation(deg) {
                // 限制旋转范围在 -60 到 160 度之间，与弧线范围一致
                deg = Math.max(-60, Math.min(160, deg));
                currentRotation = deg;
                knob.style.setProperty('--rotation', `${deg}deg`);
                
                // 计算频率：从0.1Hz到10Hz
                // 映射 -60 到 160 度的范围（总共220度）到 0.1Hz到10Hz
                const normalizedRotation = (deg - (-60)) / 220;
                const frequency = 0.1 + normalizedRotation * 9.9;
                valueDisplay.textContent = frequency.toFixed(1) + 'Hz';
                
                log(`LFO旋钮旋转至: ${deg.toFixed(1)}度, ${frequency.toFixed(1)}Hz`);
            }

            // 为旋钮和弧线添加点击事件 - 使用相同的处理逻辑
            function handleClick(event) {
                // 如果是拖动结束的点击，不处理
                if (isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算点击位置相对于中心的角度
                const clickX = event.clientX - centerX;
                const clickY = event.clientY - centerY;
                
                // 计算角度（以12点钟方向为0度）
                let angle = Math.atan2(clickY, clickX) * 180 / Math.PI;
                angle = (angle + 90) % 360; // 调整为12点钟方向为0度
                if (angle < 0) angle += 360;
                
                // 映射到LFO旋钮的范围 (-60到160度，总共220度)
                // 标准旋钮是0-300度，我们需要映射
                let lfoAngle = -60 + (angle / 300) * 220;
                
                // 限制在有效的弧度范围内
                lfoAngle = Math.max(-60, Math.min(160, lfoAngle));
                
                log(`LFO点击角度: ${angle.toFixed(1)}度，映射到LFO角度: ${lfoAngle.toFixed(1)}度`);
                updateLFORotation(lfoAngle);
                
                // 防止事件冒泡
                event.stopPropagation();
            }

            // 为旋钮和弧线添加相同的点击处理
            knob.addEventListener('click', handleClick);
            arcLine.addEventListener('click', handleClick);

            // 拖动处理 - 与标准旋钮使用相同的逻辑
            knob.addEventListener('mousedown', (event) => {
                isDragging = true;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算起始位置相对于中心的向量
                startX = event.clientX - centerX;
                startY = event.clientY - centerY;
                
                log('LFO旋钮开始拖动');
                event.preventDefault();
                
                document.addEventListener('mousemove', onLFODrag);
                document.addEventListener('mouseup', onLFORelease);
            });
            
            function onLFODrag(event) {
                if (!isDragging) return;
                
                // 获取旋钮中心
                const rect = knob.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 计算当前位置相对于中心的向量
                const currentX = event.clientX - centerX;
                const currentY = event.clientY - centerY;
                
                // 计算起始向量和当前向量之间的角度差（弧度）
                const startAngle = Math.atan2(startY, startX);
                const currentAngle = Math.atan2(currentY, currentX);
                let angleDiff = currentAngle - startAngle;
                
                // 转换为度数
                angleDiff = angleDiff * 180 / Math.PI;
                
                // 处理角度跨越边界的情况
                if (angleDiff > 180) angleDiff -= 360;
                if (angleDiff < -180) angleDiff += 360;
                
                // 更新旋转角度
                const newDeg = currentRotation + angleDiff;
                updateLFORotation(newDeg);
                
                // 更新起始位置
                startX = currentX;
                startY = currentY;
            }
            
            function onLFORelease() {
                log('LFO旋钮停止拖动');
                isDragging = false;
                document.removeEventListener('mousemove', onLFODrag);
                document.removeEventListener('mouseup', onLFORelease);
            }

            // 设置初始旋转角度为-60度
            updateLFORotation(-60);
        }

        // 初始化滑动电位器
        function initSliderPotentiometer(sliderId, handleId, valueId) {
            const slider = document.getElementById(sliderId);
            const handle = document.getElementById(handleId);
            const valueDisplay = document.getElementById(valueId);
            let isDragging = false;
            
            log(`初始化滑动电位器: ${sliderId}`);
            
            // 设置初始位置
            updateSliderPosition(50);
            
            handle.addEventListener('mousedown', function(e) {
                e.preventDefault();
                isDragging = true;
                log('滑动电位器开始拖动');
                document.addEventListener('mousemove', onSliderDrag);
                document.addEventListener('mouseup', onSliderRelease);
            });
            
            // 点击滑动条任意位置移动滑块
            slider.addEventListener('click', function(e) {
                if (e.target !== handle) {
                    const sliderRect = slider.getBoundingClientRect();
                    let position = (e.clientX - sliderRect.left) / sliderRect.width;
                    position = Math.max(0, Math.min(1, position));
                    log(`滑动条点击位置: ${(position * 100).toFixed(2)}%`);
                    updateSliderPosition(position * 100);
                }
            });
            
            function onSliderDrag(e) {
                if (!isDragging) return;
                
                const sliderRect = slider.getBoundingClientRect();
                let position = (e.clientX - sliderRect.left) / sliderRect.width;
                
                // 限制在0-1范围内
                position = Math.max(0, Math.min(1, position));
                
                updateSliderPosition(position * 100);
            }
            
            function onSliderRelease() {
                if (isDragging) {
                    log('滑动电位器停止拖动');
                    isDragging = false;
                    document.removeEventListener('mousemove', onSliderDrag);
                    document.removeEventListener('mouseup', onSliderRelease);
                }
            }
            
            function updateSliderPosition(percentage) {
                // 限制在滑块范围内
                const position = Math.max(0, Math.min(100, percentage));
                
                // 更新滑块位置
                handle.style.left = `${position}%`;
                
                // 更新显示值
                valueDisplay.textContent = `${Math.round(position)}%`;
                
                log(`滑动电位器值: ${Math.round(position)}%`);
            }
        }
    </script>
</body>
</html> 