// 引入需要注册的全局组件
import ExportPngSvg from '@/components/ExportPngSvg'
import SvgIcon from '@/components/SvgIcon'
import EditComponent from '@/components/EditComponent'
import EditTextBox from '@/components/EditTextBox'
import TestPoint from './circuit/TestPoint.vue'

// 全局对象
const allGlobalComponents = {
  ExportPngSvg,
  SvgIcon,
  EditComponent,
  EditTextBox,
  TestPoint
}

// 对外暴露插件对象
export default {
  install(app) {
    // 注册项目当中全部的全局组件
    Object.keys(allGlobalComponents).forEach((key) => {
      // 将每个组件都注册为全局组件
      app.component(key, allGlobalComponents[key])
    })
  },
}
