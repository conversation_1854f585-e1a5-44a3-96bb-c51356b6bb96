import service from '@/utils/request'

export function get(data) {
  return service({
    url: '/circuitDataTemplate/get',
    method: 'post',
    data,
  })
}

export function addOrUpdate(data) {
  return service({
    url: '/circuitDataTemplate/addOrUpdate',
    method: 'post',
    data,
  })
}

export function remove(data) {
  return service({
    url: '/circuitDataTemplate/delete',
    method: 'delete',
    data,
  })
}
