import { useSelectedComponentStore } from '@/store/selectedComponent'
import { storeToRefs } from 'pinia'

export default function () {
  // 获取到当前选中的组件
  const { selectedComponent } = storeToRefs(useSelectedComponentStore())

  /**
   * 旋转组件
   */
  const rotateComponent = (degree) => {
    selectedComponent.value.rotation = (selectedComponent.value.rotation + degree) % 360
  }

  return { rotateComponent }
}
