<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测点调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .debug-title {
            font-size: 20px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .log-output {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .connection-item {
            background-color: white;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .component-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 测点连接关系调试工具</h1>
        <p>此工具帮助调试测点无法识别电阻和可变电阻的问题。</p>
        
        <div class="debug-section">
            <div class="debug-title">📊 当前状态检查</div>
            <button onclick="checkCurrentStatus()">检查当前状态</button>
            <div id="statusResult"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🔍 连接关系分析</div>
            <button onclick="analyzeConnections()">分析连接关系</button>
            <div id="connectionResult"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">⚙️ 组件匹配测试</div>
            <button onclick="testComponentMatching()">测试组件匹配</button>
            <div id="matchingResult"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📝 实时日志</div>
            <button onclick="clearLogs()">清空日志</button>
            <button onclick="enableVerboseLogging()">启用详细日志</button>
            <div class="log-output" id="logOutput">等待日志输出...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">💡 解决方案建议</div>
            <div class="info status">
                <h4>常见问题及解决方案：</h4>
                <ul>
                    <li><strong>测点标识符为空：</strong> 检查测点组件的identifier属性是否正确设置</li>
                    <li><strong>连接关系未检测到：</strong> 确保测点与组件之间有正确的连接线</li>
                    <li><strong>组件类型不匹配：</strong> 验证组件的type属性是否为'resistor'或'rheostat'</li>
                    <li><strong>坐标不匹配：</strong> 检查组件和测点的坐标是否在合理范围内</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let logBuffer = [];
        
        // 重写console.log来捕获日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logBuffer.push(`[${new Date().toLocaleTimeString()}] ${message}`);
            updateLogDisplay();
        };
        
        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent = logBuffer.slice(-50).join('\n'); // 只显示最近50条
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        function clearLogs() {
            logBuffer = [];
            updateLogDisplay();
        }
        
        function checkCurrentStatus() {
            const result = document.getElementById('statusResult');
            
            // 检查是否在Vue应用环境中
            const isInVueApp = typeof window !== 'undefined' && window.Vue;
            const hasLocalStorage = typeof localStorage !== 'undefined';
            
            let statusHtml = '<div class="component-grid">';
            
            // 环境检查
            statusHtml += `<div class="component-card">
                <h4>🌐 环境检查</h4>
                <p>Vue环境: <span class="${isInVueApp ? 'highlight' : ''}">${isInVueApp ? '✓ 可用' : '✗ 不可用'}</span></p>
                <p>本地存储: <span class="${hasLocalStorage ? 'highlight' : ''}">${hasLocalStorage ? '✓ 可用' : '✗ 不可用'}</span></p>
            </div>`;
            
            // 检查本地存储数据
            if (hasLocalStorage) {
                const testPoints = localStorage.getItem('circuit_test_points');
                const components = localStorage.getItem('circuit_components');
                
                statusHtml += `<div class="component-card">
                    <h4>💾 本地数据</h4>
                    <p>测点数据: <span class="${testPoints ? 'highlight' : ''}">${testPoints ? '✓ 存在' : '✗ 不存在'}</span></p>
                    <p>组件数据: <span class="${components ? 'highlight' : ''}">${components ? '✓ 存在' : '✗ 不存在'}</span></p>
                </div>`;
            }
            
            statusHtml += '</div>';
            
            result.innerHTML = statusHtml;
            console.log('状态检查完成');
        }
        
        function analyzeConnections() {
            const result = document.getElementById('connectionResult');
            
            // 模拟连接关系分析
            const mockConnections = [
                {
                    from: "可变电阻 - R1 - 1 KΩ - 连接点2 - null - 坐标: 400,100",
                    to: "测点 -   -   - 连接点undefined - null - 坐标: 540,100"
                },
                {
                    from: "电阻 - R1 - 1 KΩ - 连接点1 - null - 坐标: 660,100",
                    to: "测点 -   -   - 连接点undefined - null - 坐标: 540,100"
                }
            ];
            
            let analysisHtml = '<h4>🔗 连接关系分析结果：</h4>';
            
            mockConnections.forEach((conn, index) => {
                const fromParts = conn.from.split(' - ');
                const toParts = conn.to.split(' - ');
                
                analysisHtml += `<div class="connection-item">
                    <h5>连接 ${index + 1}:</h5>
                    <p><strong>起点:</strong> ${fromParts[0]} (${fromParts[1]})</p>
                    <p><strong>终点:</strong> ${toParts[0]} (${toParts[1] || '无标识符'})</p>
                    <p><strong>问题:</strong> ${toParts[1] === '  ' ? '⚠️ 测点标识符为空' : '✓ 正常'}</p>
                </div>`;
            });
            
            result.innerHTML = analysisHtml;
            console.log('连接关系分析完成', mockConnections);
        }
        
        function testComponentMatching() {
            const result = document.getElementById('matchingResult');
            
            // 模拟组件匹配测试
            const testCases = [
                {
                    componentInfo: ['电阻', 'R1', '1 KΩ'],
                    targetComponent: { type: 'resistor', identifier: 'R1', label: '电阻1' },
                    expected: true
                },
                {
                    componentInfo: ['可变电阻', 'R1', '1 KΩ'],
                    targetComponent: { type: 'rheostat', identifier: 'R1', label: '可变电阻1' },
                    expected: true
                },
                {
                    componentInfo: ['电阻', '', ''],
                    targetComponent: { type: 'resistor', identifier: 'R1', label: '电阻1' },
                    expected: false
                }
            ];
            
            let testHtml = '<h4>🧪 组件匹配测试结果：</h4><div class="component-grid">';
            
            testCases.forEach((testCase, index) => {
                const matchResult = simulateComponentMatch(testCase.componentInfo, testCase.targetComponent);
                const isCorrect = matchResult === testCase.expected;
                
                testHtml += `<div class="component-card">
                    <h5>测试 ${index + 1}</h5>
                    <p><strong>组件信息:</strong> ${testCase.componentInfo.join(' - ')}</p>
                    <p><strong>目标组件:</strong> ${testCase.targetComponent.type} (${testCase.targetComponent.identifier})</p>
                    <p><strong>匹配结果:</strong> <span class="${isCorrect ? 'highlight' : ''}">${matchResult ? '✓ 匹配' : '✗ 不匹配'}</span></p>
                    <p><strong>测试状态:</strong> <span class="${isCorrect ? 'highlight' : ''}">${isCorrect ? '✓ 通过' : '✗ 失败'}</span></p>
                </div>`;
            });
            
            testHtml += '</div>';
            result.innerHTML = testHtml;
            console.log('组件匹配测试完成');
        }
        
        function simulateComponentMatch(componentInfo, targetComponent) {
            const [componentName, componentIdentifier] = componentInfo;
            
            // 模拟匹配逻辑
            if (targetComponent.identifier && componentIdentifier && 
                targetComponent.identifier === componentIdentifier) {
                return true;
            }
            
            if (componentName && targetComponent.type) {
                if (componentName.includes('电阻') && targetComponent.type === 'resistor') {
                    return true;
                }
                if (componentName.includes('可变电阻') && targetComponent.type === 'rheostat') {
                    return true;
                }
            }
            
            return false;
        }
        
        function enableVerboseLogging() {
            console.log('已启用详细日志模式');
            console.log('请在主应用中操作测点以查看详细日志');
            
            // 模拟一些日志输出
            setTimeout(() => {
                console.log('开始查找测点周围组件...');
                console.log('获取到的连接关系: 2个');
                console.log('检查连接: 可变电阻 - R1 -> 测点');
                console.log('✓ 通过电阻类型描述匹配到组件');
                console.log('最终找到的连接组件: 1个');
            }, 1000);
        }
        
        // 页面加载时自动检查状态
        window.onload = function() {
            console.log('测点调试工具已加载');
            checkCurrentStatus();
        };
    </script>
</body>
</html>
