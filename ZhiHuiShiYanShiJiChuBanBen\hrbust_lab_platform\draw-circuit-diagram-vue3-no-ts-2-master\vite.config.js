import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import {resolve} from 'path'
import VueSetupExtend from 'vite-plugin-vue-setup-extend'
import svgString from 'vite-plugin-svgstring'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  // base 是 Vite 的公共路径配置，用于指定资源文件（如 JS、CSS、图片等）的 URL 前缀
  base: '/hrbust_lab_platform/draw-circuit-diagram-dist',
  build: {
    outDir: 'draw-circuit-diagram-dist', // 指定输出文件夹名称
  },
  plugins: [ 
    vue(),
    VueSetupExtend(),
    svgString(),  
    vueDevTools(),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
      // 指定symbolId格式
      symbolId: 'icon-[name]',
    }),
  ],

  server: {
    cors: true,
    proxy: { // 配置跨域
    	'/hrbust_lab_platform/draw-circuit-diagram-dist/dev-api': {
      //'/circuitData': {
        // target: 'http://my.mcdd.top:9527/', 
        //target: 'http://127.0.0.1:8084/hrbust_lab_platform', // 根据需求替换 请求后台接口
        target: '/hrbust_lab_platform', // 根据需求替换 请求后台接口
        changeOrigin: true, // 允许跨域
        ws: true,
        rewrite: (path) => path.replace(new RegExp('^' + '/hrbust_lab_platform/draw-circuit-diagram-dist/dev-api'), '')
      }
    }
  },
  
  resolve: {
    // 以下两个配置搭配使用
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
    extensions: ['.js', '.vue', '.json'] ,
    // 以上两个配置搭配使用
  }
})
