import { toRaw } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { useLineStateStore } from '@/store/lineState'
import { useTextBoxStore } from '@/store/textBox'
import { useCurrentUserCircuitDataStore } from '@/store/currentUserCircuitData'
import { addOrUpdate } from '@/api/circuitData'
import { validateURLParam } from '@/utils/before/validateURLParam'
import { useVerifyStore } from '@/store/verify'
import domtoimage from 'dom-to-image'

/**
 * 保存数据函数
 */
const saveData = () => {
  const { userId, courseName, experimentName } = validateURLParam()
  const { components, fixedLines, intersections, textBoxs, updateCurrentCircuitData } = getStoreData()

  // 校验 store : { 生成连接关系 }
  const verifyStore = useVerifyStore()
  const { generateConnectionRelationships } = verifyStore

  // 🔧 修复：安全访问响应式引用，防止删除连线后的状态更新错误
  let dataToSave
  try {
    dataToSave = {
      components: toRaw(components?.value || []),
      fixedLines: toRaw(fixedLines?.value || []),
      intersections: toRaw(intersections?.value || []),
      textBoxs: toRaw(textBoxs?.value || []),
    }
  } catch (error) {
    console.error('❌ 获取电路数据时出错:', error)
    dataToSave = {
      components: [],
      fixedLines: [],
      intersections: [],
      textBoxs: [],
    }
  }

  // 保存当前数据到store里，后续方便往数据库里插入
  updateCurrentCircuitData(dataToSave)

  // 🔧 修复：安全生成测点连接关系数据
  let testPointRelationsData = '{}'
  try {
    testPointRelationsData = exportTestPointRelations() || '{}'
  } catch (error) {
    console.error('❌ 生成测点连接关系时出错:', error)
    testPointRelationsData = '{}'
  }

  // 🔧 修复：安全生成连接关系
  let connections = []
  try {
    connections = generateConnectionRelationships() || []
  } catch (error) {
    console.error('❌ 生成连接关系时出错:', error)
    connections = []
  }

  // 访问后端API：将数据插入到数据库
  const requestBody = {
    userId,
    courseName,
    experimentName,
    circuitData: JSON.stringify(dataToSave),
    connections: JSON.stringify(connections),
    testPointRelations: testPointRelationsData, // 添加测点连接关系数据
  }
  addOrUpdate(requestBody)
    .then((res) => {
      const { code, msg } = res.data
      if (code == 200) {
        ElMessage.success(msg)
      } else {
        ElMessage.error('addOrUpdate 失败')
      }
    })
    .catch((err) => {
      ElMessage.error('保存失败', err)
    })

  // 将画布导出为png，并存储在浏览器本地存储
  canvasToPng()
}

/**
 * 加载数据函数
 */
const loadData = async (circuitData) => {
  if (!circuitData) {
    // 如果没有传入数据，直接返回，避免后续不必要的操作
    ElMessage({
      type: 'warning',
      message: '加载数据需要传入参数！',
    })

    return
  }

  let data
  try {
    data = JSON.parse(circuitData)
  } catch (error) {
    console.error(circuitData)
    console.error('@@@ ', error)
    // 数据解析异常，系统已为您【重置】该实验的电路图数据。
    await ElMessageBox.alert(
      '数据解析异常，系统已为您【重置】该实验的电路图数据。 ' +
        '<br> 点击<strong>【收到】</strong>后，请<strong>【手动关闭】</strong>此页面！',
      '警告',
      {
        confirmButtonText: '确定',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        callback: () => {
          // 将空数据保存至数据库
          saveData()
        },
      },
    )
  } finally {
    // 重新加载数据
    data = data || { components: [], fixedLines: [], intersections: [], textBoxs: [] }

    // 🔧 修复测点标识符重复问题
    if (data.components && Array.isArray(data.components)) {
      console.log('🔧 开始检查和修复测点标识符...');

      // 收集所有测点组件
      const testPoints = data.components.filter(component =>
        component.type === 'testPoint' || component.isTestPoint
      );

      // 检查是否有重复的标识符
      const identifierMap = new Map();
      const duplicateIdentifiers = new Set();

      testPoints.forEach((component, index) => {
        console.log(`🔧 发现测点组件 ${index}:`, {
          id: component.id || component.componentId,
          currentIdentifier: component.identifier,
          identifierType: typeof component.identifier,
          isEmptyOrSpace: !component.identifier || component.identifier.trim() === ''
        });

        const identifier = component.identifier;
        if (identifier && identifier.trim() !== '') {
          if (identifierMap.has(identifier)) {
            duplicateIdentifiers.add(identifier);
            console.log(`🚨 发现重复标识符: ${identifier}`);
          } else {
            identifierMap.set(identifier, component);
          }
        }
      });

      // 如果有重复标识符或空标识符，重新分配所有测点标识符
      if (duplicateIdentifiers.size > 0 || testPoints.some(tp => !tp.identifier || tp.identifier.trim() === '')) {
        console.log('🔧 重新分配所有测点标识符以确保唯一性...');

        testPoints.forEach((component, index) => {
          const newIdentifier = (index + 1).toString();
          const oldIdentifier = component.identifier;
          component.identifier = newIdentifier;

          console.log(`🔧 ${oldIdentifier ? '更新' : '分配'}测点标识符: ${component.id || component.componentId} → "${newIdentifier}"`);
        });

        console.log(`🔧 测点标识符重新分配完成，共处理 ${testPoints.length} 个测点`);
      } else {
        console.log('🔧 测点标识符检查完成，无需修复');
      }
    }

    // 更新store里的电路图数据
    const { updateComponents, updateFixedLines, updateIntersections, updateTextBoxs } = getStoreData()
    updateComponents(data.components)
    updateFixedLines(data.fixedLines)
    updateIntersections(data.intersections)
    updateTextBoxs(data.textBoxs)
  }
}

/**
 * 辅助函数：获取当前画布的数据
 */
const getCurrentCircuitData = () => {
  const { components, fixedLines, intersections, textBoxs } = getStoreData()

  const currentCircuitData = {
    components: toRaw(components.value),
    fixedLines: toRaw(fixedLines.value),
    intersections: toRaw(intersections.value),
    textBoxs: toRaw(textBoxs.value),
  }

  return currentCircuitData
}
/**
 * 辅助函数：获取需要的store和相关的更新函数
 */
const getStoreData = () => {
  const componentsInfoStore = useComponentsInfoStore()
  const lineStateStore = useLineStateStore()
  const textBoxStore = useTextBoxStore()
  const currentUserCircuitDataStore = useCurrentUserCircuitDataStore()

  const { components } = storeToRefs(componentsInfoStore)
  const { fixedLines, intersections } = storeToRefs(lineStateStore)
  const { textBoxs } = storeToRefs(textBoxStore)

  const { updateCurrentCircuitData } = currentUserCircuitDataStore

  return {
    components,
    fixedLines,
    intersections,
    textBoxs,
    updateCurrentCircuitData,
    updateComponents: componentsInfoStore.updateComponents,
    updateFixedLines: lineStateStore.updateFixedLines,
    updateIntersections: lineStateStore.updateIntersections,
    updateTextBoxs: textBoxStore.updateTextBoxs,
    clearComponents: componentsInfoStore.clearComponents,
    clearFixedLines: lineStateStore.clearFixedLines,
    clearIntersections: lineStateStore.clearIntersections,
    clearTextBoxs: textBoxStore.clearTextBoxs,
  }
}

/**
 * 重置数据
 */
const resetData = () => {
  const { clearComponents, clearFixedLines, clearIntersections, clearTextBoxs } = getStoreData()
  clearComponents()
  clearFixedLines()
  clearIntersections()
  clearTextBoxs()
}

/**
 * 辅助函数：将画布导出为png，并存储在浏览器本地存储
 * 同时保存测点信息和可变电阻信息
 */
const canvasToPng = () => {

  const canvasElement = document.getElementById('canvas-area')
  domtoimage
    .toPng(canvasElement)
    .then((dataUrl) => {
      // 保存图片数据到 localStorage
      localStorage.setItem('temp_circuit_png', dataUrl)
      
      // 获取测点信息
      const { components } = getStoreData()
      const testPoints = extractTestPoints(components.value)
      
      // 保存测点数据到 localStorage
      localStorage.setItem('circuit_test_points', JSON.stringify(testPoints))
      
      // 获取并保存可变电阻信息
      const variableResistors = extractVariableResistors(components.value)
      localStorage.setItem('variable_resistors', JSON.stringify(variableResistors))
      console.log(`已保存${variableResistors.length}个可变电阻组件信息:`, variableResistors)
    
      const switchs= extractSwitch(components.value)
      localStorage.setItem('switchs', JSON.stringify(switchs))
      
      const sswitchs= extractSSwitch(components.value)
      localStorage.setItem('SPDTSwitchs', JSON.stringify(sswitchs))
      
      // 获取并保存测点连接关系数据（标准JSON格式）
      const testPointRelations = exportTestPointRelations()
      localStorage.setItem('test_point_relations', testPointRelations)
      console.log('已保存测点连接关系数据:', JSON.parse(testPointRelations))
    })
    .catch((error) => {
      console.error('画布导出为png失败:', error)
    })
}

/**
 * 辅助函数：从组件中提取测点信息
 * @param {Array} components - 组件数组
 * @returns {Array} - 测点数组，格式为 [{x, y}, ...]
 */
const extractTestPoints = (components) => {
  // 筛选出测点类型的组件
  const testPointComponents = components.filter(component => 
    component.type === 'testPoint' || component.isTestPoint === true
  );
  
  // 提取测点的位置信息，并转换为百分比坐标
  const canvasElement = document.getElementById('canvas-area');
  if (!canvasElement) {
    console.error('找不到 canvas-area 元素');
    return [];
  }
  
  const canvasWidth = canvasElement.offsetWidth;
  const canvasHeight = canvasElement.offsetHeight;
  
  // 先调用findSurroundingComponents获取所有测点的周围组件
  const testPointsWithSurrounding = testPointComponents.map(point => {
    // 获取周围器件
    const surroundingComponents = findSurroundingComponents(point, components);
    
    // 将像素坐标转换为百分比坐标
    const result = {
      x: (point.x / canvasWidth) * 100,
      y: (point.y / canvasHeight) * 100,
      id: point.componentId || point.id || `tp_${Math.random().toString(36).substr(2, 9)}`,
      label: point.label || point.identifier || '',
      identifier: point.identifier,
      value: point.value,
      // 添加周围器件信息
      surroundingComponents: surroundingComponents
    };
    
    // 同时更新原始组件对象，确保在界面上也能显示
    point.surroundingComponents = surroundingComponents;
    
    return result;
  });
  
  // 保存带有周围组件信息的测点数据
  localStorage.setItem('circuit_test_points_with_surrounding', JSON.stringify(testPointsWithSurrounding));
  
  return testPointsWithSurrounding;
}

/**
 * 辅助函数：查找测点周围的器件
 * @param {Object} testPoint - 测点对象
 * @param {Array} allComponents - 所有组件数组
 * @returns {Array} - 与测点直接相连的器件列表
 */
const findSurroundingComponents = (testPoint, allComponents) => {
  // 简化：只在需要时显示组件信息

  // 使用verifyStore获取连接关系
  const verifyStore = useVerifyStore();
  const connections = verifyStore.generateConnectionRelationships();

  if (!connections || connections.length === 0) {
    console.warn('没有找到任何连接关系');
    return [];
  }

  // 测点ID和标签
  const testPointId = testPoint.componentId || testPoint.id;
  const testPointLabel = testPoint.label || '';
  let testPointIdentifier = testPoint.identifier || '';

  // 🔧 统一标识符生成逻辑：与连接关系生成保持一致
  if (!testPointIdentifier || testPointIdentifier.trim() === '') {
    // 使用组件ID的后几位作为标识符，与 formatConnectionDescription 保持一致
    const componentId = testPoint.id || testPoint.componentId || ''
    testPointIdentifier = componentId.slice(-6) || 'TP'
    console.log(`⚠️ 测点缺少标识符，使用组件ID后6位: ${testPointIdentifier} (完整ID: ${componentId})`);
  }

  // 存储与测点直接相连的组件
  const connectedComponents = [];
  // 用于跟踪已添加的组件标识符，避免重复
  const addedComponentIdentifiers = new Set();

  // 收集所有与测点相关的连接
  const testPointConnections = [];

  // 简化：移除详细连接关系日志

  // 第一步：收集所有与测点相关的连接
  // console.log(`🔍 开始分析测点 (${testPoint.x}, ${testPoint.y}), 标识符: "${testPointIdentifier}", 标签: "${testPointLabel}"`);

  connections.forEach(connection => {
    // 解析连接信息
    const fromParts = connection.from.split(' - ');
    const toParts = connection.to.split(' - ');

    // 调试：输出所有连接
    // if (fromParts[0].includes('测点') || toParts[0].includes('测点')) {
    //   console.log(`🔗 连接: ${connection.from} → ${connection.to}`);
    // }

    // 精确的测点匹配逻辑 - 只匹配当前特定的测点
    let isFromTestPoint = false;
    let isToTestPoint = false;

    // 检查from端是否是当前测点
    if (fromParts[0].includes('测点')) {
      // console.log(`  🔍 检查from端: ${connection.from}`);

      // 使用标识符匹配来区分不同的测点
      const connectionTestPointId = fromParts[1] || ''; // 连接中的测点标识符

      // 🔧 调试日志已注释 - 连接关系中的测点标识符
      // console.log('🔧 连接关系from端测点标识符:', {
      //   connectionTestPointId: connectionTestPointId,
      //   currentTestPointId: testPointIdentifier,
      //   fromParts: fromParts,
      //   fullConnection: connection.from
      // });

      if (connectionTestPointId === testPointIdentifier) {
        isFromTestPoint = true;
        // console.log(`    ✅ 标识符匹配: 当前测点(${testPointIdentifier}) = 连线测点(${connectionTestPointId})`);
      } else {
        // console.log(`    ❌ 标识符不匹配: 当前测点(${testPointIdentifier}) ≠ 连线测点(${connectionTestPointId})`);
      }
    }

    // 检查to端是否是当前测点
    if (toParts[0].includes('测点')) {
      // console.log(`  🔍 检查to端: ${connection.to}`);

      // 使用标识符匹配来区分不同的测点
      const connectionTestPointId = toParts[1] || ''; // 连接中的测点标识符

      // 🔧 调试日志已注释 - 连接关系中的测点标识符
      // console.log('🔧 连接关系to端测点标识符:', {
      //   connectionTestPointId: connectionTestPointId,
      //   currentTestPointId: testPointIdentifier,
      //   toParts: toParts,
      //   fullConnection: connection.to
      // });

      if (connectionTestPointId === testPointIdentifier) {
        isToTestPoint = true;
        // console.log(`    ✅ 标识符匹配: 当前测点(${testPointIdentifier}) = 连线测点(${connectionTestPointId})`);
      } else {
        // console.log(`    ❌ 标识符不匹配: 当前测点(${testPointIdentifier}) ≠ 连线测点(${connectionTestPointId})`);
      }
    }

    // 坐标匹配已经整合到上面的逻辑中了

    // 如果连接与当前测点有关
    if (isFromTestPoint || isToTestPoint) {
      // 确定与测点相连的另一端组件信息
      const connectedComponentInfo = isFromTestPoint ? toParts : fromParts;

      // 检查是否是直接连接（不通过其他器件）
      const isDirectConnection = !connectedComponentInfo[0].includes('测点');

      // 只处理直接连接的情况
      if (isDirectConnection) {
        testPointConnections.push({
          info: connectedComponentInfo,
          fromTestPoint: isFromTestPoint
        });
      }
    }
  });

  // 第二步：处理收集到的连接，查找对应的组件
  testPointConnections.forEach(connection => {
    const connectedComponentInfo = connection.info;
    const isFromTestPoint = connection.fromTestPoint;

    // 获取组件标识符和名称
    const componentIdentifier = connectedComponentInfo[1];
    const componentName = connectedComponentInfo[0];

    // 创建唯一键：组合标识符和类型，避免不同类型但相同标识符的组件被误跳过
    const uniqueKey = `${componentIdentifier}_${connectedComponentInfo[0]}_${connectedComponentInfo[2] || ''}`;

    // 如果已经添加过相同的组件，跳过
    if (componentIdentifier && addedComponentIdentifiers.has(uniqueKey)) {
      return;
    }

    // 增强的组件查找逻辑 - 需要同时匹配类型和标识符
    const connectedComponent = allComponents.find(comp => {

      // 最优先：通过标识符和类型精确匹配
      if (comp.identifier && componentIdentifier && comp.identifier === componentIdentifier) {
        // 检查类型是否匹配
        const typeMatches = (
          (componentName === '电阻' && comp.type === 'resistor') ||
          (componentName === '可变电阻' && comp.type === 'rheostat') ||
          (componentName === '极性电容' && comp.type === 'polarizedCapacitor') ||
          (componentName === '无极性电容' && comp.type === 'nonPolarizedCapacitor') ||
          (comp.label && comp.label === componentName)
        );

        if (typeMatches) {
          return true;
        }
        // 继续检查下一个组件
      }

      // 如果标识符匹配失败，尝试其他匹配方式

      // 方式2: 通过标签精确匹配
      if (comp.label && componentName && comp.label === componentName) {
        return true;
      }

      // 方式3: 通过坐标匹配（如果其他方式都失败）
      if (connectedComponentInfo.length > 5) {
        const coordMatch = connectedComponentInfo.join(' - ').match(/坐标:\s*(\d+),(\d+)/);
        if (coordMatch) {
          const targetX = parseInt(coordMatch[1]);
          const targetY = parseInt(coordMatch[2]);
          const distance = Math.sqrt(Math.pow(comp.x - targetX, 2) + Math.pow(comp.y - targetY, 2));
          if (distance < 30) { // 30像素容差
            return true;
          }
        }
      }

      // 没有匹配
      return false;
    });

    // 如果找到对应组件且不是测点本身
    if (connectedComponent &&
        connectedComponent.id !== testPointId &&
        connectedComponent.type !== 'testPoint' &&
        !connectedComponent.isTestPoint) {

      // 确定组件类型名称
      let componentTypeName = getComponentTypeName(connectedComponent.type);

      // 特殊处理电阻类型
      if (connectedComponent.type === 'resistor') {
        componentTypeName = '电阻';
      } else if (connectedComponent.type === 'rheostat') {
        componentTypeName = '可变电阻';
      }

      // 保持电容的具体类型，不要统一为"电容"
      // 移除了原来的电容统一处理逻辑，让极性电容和无极性电容分别显示

      // 添加到已处理组件集合
      if (componentIdentifier) {
        addedComponentIdentifiers.add(uniqueKey);
      }

      // 添加到相连组件列表
      const componentInfo = {
        id: connectedComponent.id,
        type: connectedComponent.type,
        typeName: componentTypeName, // 使用统一的获取类型名称函数
        label: connectedComponent.label || connectedComponent.identifier || '未命名组件',
        identifier: connectedComponent.identifier,
        connectionPoint: isFromTestPoint ?
                        (connectedComponentInfo[3] ? connectedComponentInfo[3].replace('连接点', '') : '') :
                        (connectedComponentInfo[3] ? connectedComponentInfo[3].replace('连接点', '') : ''),
        position: {
          x: connectedComponent.x,
          y: connectedComponent.y
        }
      };

      connectedComponents.push(componentInfo);
    }
  });

  // 只在最后输出简洁的结果
  // if (connectedComponents.length > 0) {
  //   // 使用已经分配的标识符
  //   console.log(`${testPointIdentifier}测点连接了 ${connectedComponents.length} 个组件:`,
  //     connectedComponents.map(comp => `${comp.typeName} ${comp.identifier}`).join(', '));
  // }

  return connectedComponents;
}

/**
 * 辅助函数：从组件中提取可变电阻信息
 * @param {Array} components - 组件数组
 * @returns {Array} - 可变电阻数组
 */
const extractVariableResistors = (components) => {
  // 筛选出可变电阻类型的组件
  const rheostats = components.filter(component => 
    component.type === 'rheostat'
  );
  
  console.log('找到的可变电阻组件:', rheostats); // 添加日志
  
  // 如果没有找到可变电阻，添加一个默认的
  if (rheostats.length === 0) {
    console.log('未找到可变电阻组件，添加一个默认的可变电阻');
    return [{
      id: 'default-resistor',
      label: '默认可变电阻',
      defaultValue: 50,
      identifier:'R1'
    }];
  } 
  
  // 提取可变电阻信息
  return rheostats.map(resistor => {
    const result = {
      id: resistor.componentId || resistor.id || `resistor_${Math.random().toString(36).substr(2, 9)}`,
      label: resistor.label || resistor.identifier || '可变电阻',
      defaultValue: resistor.value || 50, // 默认值为50%
      identifier:resistor.identifier||'R1',//标识
      unit: resistor.unit || 'KΩ',
      name: resistor.name ,
    }; 
    
    console.log('处理可变电阻:', resistor, '结果:', result); // 添加日志
    
    return result;
  });
}
const extractSwitch = (components) => {
  // 筛选出开关组件
  const switchArr = components.filter(component => 
    component.type === 'switch'
  );
  const canvasElement = document.getElementById('canvas-area');
 const canvasWidth = canvasElement.offsetWidth;
 const canvasHeight = canvasElement.offsetHeight;
  // 提取开关组件信息
  return switchArr.map(switcho => {
    const result = {
      id: switcho.componentId || switcho.id || `switcho_${Math.random().toString(36).substr(2, 9)}`,
      label: switcho.label || switcho.identifier || '开关',
      identifier:switcho.identifier||'S1',//标识
      name: switcho.name ,
      x: (switcho.x / canvasWidth) * 100,
      y: (switcho.y / canvasHeight) * 100,
    }; 
    return result;
  });
}
const extractSSwitch = (components) => {
  // 筛选出开关组件
  const sswitchArr = components.filter(component => 
    component.type === 'SPDTSwitch'
  );
  const canvasElement = document.getElementById('canvas-area');
 const canvasWidth = canvasElement.offsetWidth;
 const canvasHeight = canvasElement.offsetHeight;
  // 提取开关组件信息
  return sswitchArr.map(switcho => {
    const result = {
      id: switcho.componentId || switcho.id || `SPDTSwitch_${Math.random().toString(36).substr(2, 9)}`,
      label: switcho.label || switcho.identifier || '开关',
      identifier:switcho.identifier||'S1',//标识
      name: switcho.name ,
      x: (switcho.x / canvasWidth) * 100,
      y: (switcho.y / canvasHeight) * 100,
    }; 
    return result;
  });
}

/**
 * 辅助函数：根据测点ID查询其周围的器件信息
 * @param {String} testPointId - 测点ID
 * @returns {Object|null} - 周围器件信息对象，如果找不到则返回null
 */
const getTestPointSurroundingComponents = (testPointId) => {
  try {
    // 从本地存储中获取测点数据
    const testPointsJSON = localStorage.getItem('circuit_test_points_with_surrounding');
    if (!testPointsJSON) {
      console.warn('找不到测点周围器件信息数据');
      return null;
    }
    
    const testPoints = JSON.parse(testPointsJSON);
    
    // 查找指定ID的测点
    const testPoint = testPoints.find(point => point.id === testPointId);
    if (!testPoint) {
      console.warn(`找不到ID为 ${testPointId} 的测点`);
      return null;
    }
    
    return testPoint.surroundingComponents || null;
  } catch (error) {
    console.error('获取测点周围器件信息时出错:', error);
    return null;
  }
}

/**
 * 导出测点连接关系为标准JSON格式
 * @returns {String} 格式化的JSON字符串
 */
const exportTestPointRelations = () => {
  try {
    // 🔧 移除测点计数器重置，使用统一的标识符生成逻辑
    // window.testPointCounter = 0;

    // 获取连接关系数据
    const verifyStore = useVerifyStore();
    const connections = verifyStore.generateConnectionRelationships();

    // 获取组件数据
    const componentsStore = useComponentsInfoStore();
    const { components } = storeToRefs(componentsStore);

    console.log('🔍 开始导出测点关系，总连接数:', connections ? connections.length : 0);

    // 获取所有测点组件 - 使用与调试按钮相同的逻辑
    const testPoints = [];
    components.value?.forEach(component => {
      if (component.type === 'testPoint' || component.isTestPoint) {
        testPoints.push({
          id: component.id || component.componentId,
          name: component.label || component.name || component.identifier || '测点',
          identifier: component.identifier || '',
          x: component.x,
          y: component.y,
          component: component
        });
      }
    });

    console.log('📍 找到测点数量:', testPoints.length);

    if (testPoints.length === 0) {
      console.warn('⚠️ 没有找到任何测点');
      return JSON.stringify({
        version: "1.0",
        timestamp: new Date().toISOString(),
        testPointConnections: [],
        groupedByTestPoint: {}
      });
    }

    // 使用与调试按钮完全相同的逻辑分析每个测点
    const groupedByTestPoint = {};
    let testPointCounter = 0;

    testPoints.forEach((testPoint, index) => {
      // 调用与调试按钮相同的函数
      const surroundingComponents = findSurroundingComponents(testPoint.component, components.value);

      if (surroundingComponents.length > 0) {
        testPointCounter++;
        const identifier = testPointCounter.toString();

        // console.log(`📋 测点${identifier} (${testPoint.x}, ${testPoint.y}): 连接了 ${surroundingComponents.length} 个器件`);

        groupedByTestPoint[identifier] = {
          testPoint: {
            identifier: identifier,
            name: testPoint.name,
            position: {
              x: testPoint.x,
              y: testPoint.y
            }
          },
          connectedDevices: surroundingComponents.map(comp => ({
            name: comp.typeName, // 使用中文名称
            typeName: comp.typeName,
            identifier: comp.identifier,
            type: comp.type
          }))
        };
      }
    });

    // 构建最终的数据结构
    const testPointRelationsData = {
      version: "1.0",
      timestamp: new Date().toISOString(),
      testPointConnections: [], // 保持兼容性
      groupedByTestPoint: groupedByTestPoint
    };

    // 输出分组信息，便于调试
    // console.log('📊 测点连接关系分组:');
    // const groupKeys = Object.keys(groupedByTestPoint);

    // if (groupKeys.length === 0) {
    //   console.log('  ⚠️ 没有找到任何测点分组');
    // } else {
    //   console.log(`  📋 共找到 ${groupKeys.length} 个测点分组:`);
    //   groupKeys.forEach(identifier => {
    //     const group = groupedByTestPoint[identifier];
    //     const deviceTypes = group.connectedDevices.map(d => d.typeName).join('、');
    //     const deviceCount = group.connectedDevices.length;
    //     console.log(`    🔸 ${identifier}测点: ${deviceTypes} (${deviceCount}个器件)`);
    //   });
    // }

    // 返回格式化的JSON字符串
    return JSON.stringify(testPointRelationsData, null, 2);
  } catch (error) {
    console.error('导出测点连接关系时出错:', error);
    return JSON.stringify({
      error: true,
      message: error.message || '导出测点连接关系时出错',
      testPointConnections: []
    });
  }
};

/**
 * 辅助函数：根据组件类型获取中文类型名称
 * @param {String} type - 组件类型
 * @returns {String} - 中文类型名称
 */
const getComponentTypeName = (type) => {
  if (!type) return '未知器件';

  // console.log('获取组件类型名称，输入类型:', type);

  // 统一转换为小写进行判断
  const lowerType = type.toLowerCase();

  // 精确匹配常见组件类型
  const typeMapping = {
    'resistor': '电阻',
    'rheostat': '可变电阻',
    'potentiometer': '可变电阻',
    'capacitor': '电容',
    'nonpolarizedcapacitor': '无极性电容',
    'polarizedcapacitor': '极性电容',
    'inductor': '电感',
    'diode': '二极管',
    'zenerdiode': '稳压管',
    'dualzenerdiode': '双向稳压管',
    'transistor_npn': 'NPN三极管',
    'transistor_pnp': 'PNP三极管',
    'switch': '开关',
    'spdtswitch': '双刀开关',
    'voltage_source': '电压源',
    'ground': '接地',
    'oscilloscope': '示波器',
    'ammeter': '电流表',
    'voltmeter': '电压表',
    'fuse': '保险管',
    'lm78xx': '三端稳压集成电路',
    'op_amp': '运算放大器',
    'variablecapacitor': '可变电容'
  };

  // 首先尝试精确匹配
  if (typeMapping[lowerType]) {
    // console.log('精确匹配到类型:', typeMapping[lowerType]);
    return typeMapping[lowerType];
  }

  // 如果精确匹配失败，使用模糊匹配
  // 检查是否是可变电阻（包含"可变"、"滑动"或特定类型名称）
  if (type.includes('可变电阻') || type.includes('滑动电阻') ||
      lowerType === 'rheostat' || lowerType === 'potentiometer' ||
      lowerType.includes('rheostat') || lowerType.includes('potentiometer')) {
    // console.log('模糊匹配到可变电阻');
    return '可变电阻';
  }
  // 检查是否是普通电阻
  else if (type.includes('电阻') || lowerType === 'resistor' ||
           lowerType.includes('resistor')) {
    // console.log('模糊匹配到电阻');
    return '电阻';
  }
  // 电容类型 - 保持具体类型
  else if (type.includes('极性电容') || lowerType === 'polarizedcapacitor') {
    // console.log('模糊匹配到极性电容');
    return '极性电容';
  }
  else if (type.includes('无极性电容') || lowerType === 'nonpolarizedcapacitor') {
    // console.log('模糊匹配到无极性电容');
    return '无极性电容';
  }
  else if (type.includes('电容') || lowerType.includes('capacitor')) {
    console.log('模糊匹配到电容');
    return '电容';
  }
  // 电感
  else if (type.includes('电感') || lowerType.includes('inductor')) {
    console.log('模糊匹配到电感');
    return '电感';
  }
  // 双刀开关
  else if ((type.includes('开关') && type.includes('双刀')) ||
           lowerType.includes('spdtswitch') || lowerType.includes('spdt')) {
    console.log('模糊匹配到双刀开关');
    return '双刀开关';
  }
  // 普通开关
  else if (type.includes('开关') || lowerType.includes('switch')) {
    console.log('模糊匹配到开关');
    return '开关';
  }
  // 二极管
  else if (type.includes('二极管') || lowerType.includes('diode')) {
    console.log('模糊匹配到二极管');
    return '二极管';
  }
  // 三极管
  else if (type.includes('三极管') || lowerType.includes('transistor')) {
    console.log('模糊匹配到三极管');
    return '三极管';
  }
  // 电源
  else if (type.includes('电源') || lowerType.includes('voltage') ||
           lowerType.includes('source') || lowerType.includes('power')) {
    console.log('模糊匹配到电源');
    return '电源';
  }
  // 示波器
  else if (type.includes('示波器') || lowerType.includes('oscilloscope')) {
    console.log('模糊匹配到示波器');
    return '示波器';
  }
  // 万用表
  else if (type.includes('万用表') || lowerType.includes('multimeter')) {
    console.log('模糊匹配到万用表');
    return '万用表';
  }
  // 接地
  else if (type.includes('接地') || lowerType.includes('ground')) {
    console.log('模糊匹配到接地');
    return '接地';
  }

  // 如果没有匹配到任何类型，返回原始类型
  console.log('未匹配到任何类型，返回原始类型:', type);
  return type;
};

/**
 * 调试函数：分析测点连接关系
 * @param {Object} testPoint - 测点对象
 * @param {Array} allComponents - 所有组件数组
 * @returns {Object} - 调试信息
 */
const debugTestPointConnections = (testPoint, allComponents) => {
  console.log('=== 开始调试测点连接关系 ===');
  console.log('测点信息:', testPoint);
  console.log('所有组件数量:', allComponents.length);

  // 获取连接关系
  const verifyStore = useVerifyStore();
  const connections = verifyStore.generateConnectionRelationships();

  console.log('所有连接关系:', connections);

  // 分析每个连接
  const relatedConnections = [];
  connections.forEach((connection, index) => {
    const fromParts = connection.from.split(' - ');
    const toParts = connection.to.split(' - ');

    const isFromTestPoint = fromParts[0].includes('测点');
    const isToTestPoint = toParts[0].includes('测点');

    if (isFromTestPoint || isToTestPoint) {
      relatedConnections.push({
        index,
        connection,
        fromParts,
        toParts,
        isFromTestPoint,
        isToTestPoint
      });

      console.log(`连接 ${index}:`, {
        from: connection.from,
        to: connection.to,
        isFromTestPoint,
        isToTestPoint
      });
    }
  });

  console.log('与测点相关的连接数量:', relatedConnections.length);

  // 分析组件匹配
  const componentAnalysis = [];
  allComponents.forEach((comp, index) => {
    if (comp.type === 'resistor' || comp.type === 'rheostat') {
      componentAnalysis.push({
        index,
        type: comp.type,
        identifier: comp.identifier,
        label: comp.label,
        position: { x: comp.x, y: comp.y }
      });

      console.log(`电阻类组件 ${index}:`, {
        type: comp.type,
        identifier: comp.identifier,
        label: comp.label,
        position: { x: comp.x, y: comp.y }
      });
    }
  });

  console.log('电阻类组件数量:', componentAnalysis.length);
  console.log('=== 调试结束 ===');

  return {
    testPoint,
    relatedConnections,
    componentAnalysis,
    totalConnections: connections.length,
    totalComponents: allComponents.length
  };
};

export { saveData, loadData, resetData, getCurrentCircuitData, extractVariableResistors, extractTestPoints, getTestPointSurroundingComponents, getStoreData, exportTestPointRelations, findSurroundingComponents, debugTestPointConnections }
