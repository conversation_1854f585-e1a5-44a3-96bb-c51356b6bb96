/**
 * 在画布内【绘制组件】
 */
export default function () {
  /**
   * 计算元器件的样式
   * @param {Object} component - 元器件数据
   * @returns {Object} - 样式对象
   */
  const getComponentStyle = (component, gridSpacing) => {
    const x = alignToGrid(component.x)
    const y = alignToGrid(component.y)
    const scale = alignToGrid(component.scale)
    return {
      transform: `translate(${x}px, ${y}px) scale(${scale})`,
      width: `${gridSpacing * 2}px`, // 控制元器件大小
      height: `${gridSpacing * 2}px`,
    }
  }
  /**
   * 对齐网格：将坐标对齐到网格点
   * @param {number} value - 原始坐标值
   * @returns {number} - 对齐后的坐标值
   */
  const alignToGrid = (value, gridSpacing) => {
    return Math.round(value / gridSpacing) * gridSpacing
  }
  return { getComponentStyle, alignToGrid }
}
