import axios from 'axios'
import { ElMessage } from 'element-plus'

// 默认 headers
axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8'

// 创建一个axios实例
const service = axios.create({
  // 🔧 修复：根据环境动态设置 baseURL
  baseURL: import.meta.env.MODE === 'development'
    ? '/hrbust_lab_platform'  // 开发环境：通过代理
    : '/hrbust_lab_platform', // 生产环境：直接请求（不包含 draw-circuit-diagram-dist）
  timeout: 5000, // 请求超时时间，单位是毫秒
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求头中添加 token 或其他请求参数
    const token = localStorage.getItem('token') // 假设你存储了token
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 获取已有cookie并添加JSESSIONID
    // 设置一个名为 JSESSIONID 的 cookie，值为某个标识符
    if (import.meta.env.MODE === 'development') {
      document.cookie = 'JSESSIONID=C09B6DFEDF8A534215A2A99CC0B091F8;'
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const { status, data } = response

    // 根据响应状态码判断并提示信息
    if (status == 200) {
      // 200：成功提示
    } else if (status >= 400 && status < 500) {
      // 400系列：警告提示
      ElMessage.warning(data.msg)
    } else if (status >= 500) {
      // 500系列：错误提示
      ElMessage.error(data.msg)
    }

    return response
  },
  (error) => {
    // 统一错误处理
    const { response } = error
    if (response) {
      const { status, data } = response
      if (status >= 400 && status < 500) {
        ElMessage.warning(`警告: ${data.msg || '请求存在问题'}`)
      } else if (status >= 500) {
        ElMessage.error(`错误: ${data.msg || '服务器出错'}`)
      }
    } else {
      ElMessage.error('请求失败，请检查网络连接')
    }
    return Promise.reject(error)
  },
)

// 导出axios实例
export default service
