import { ref, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useCanvasInfoStore = defineStore('canvasInfo', () => {
  const canvasSize = reactive({ width: 800, height: 600 }) // 画布尺寸
  const gridSpacing = ref(20) // 网格间距

  /**
   * 计算最近的网格点
   */
  const getClosestGridPoint = (x, y) => {
    const px = Math.round(x / gridSpacing.value) * gridSpacing.value
    const py = Math.round(y / gridSpacing.value) * gridSpacing.value
    return { x: px, y: py }
  }

  return { canvasSize, gridSpacing, getClosestGridPoint }
})
