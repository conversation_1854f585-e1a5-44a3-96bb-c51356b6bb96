{"name": "draw-circuit-diagram-vue3-nots-2", "version": "0.0.0", "description": "交互式电路设计器", "private": true, "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "tar": "tar --exclude='./node_modules' -zcf draw-circuit-diagram-vue3-nots-2.tar.gz ./*", "rm": "rm -f draw-circuit-diagram-vue3-nots-2.tar.gz", "linux-gpt": "find src -type f \\( -name \"*.js\" -o -name \"*.vue\" -o -name \"*.ts\" -o -name \"*.jsx\" -o -name \"*.tsx\" -o -name \"*.html\" -o -name \"*.css\" \\) -exec echo \"File: {}\" \\; -exec cat {} \\; > public/gpt.txt", "win-gpt": "find src -type f \\( -name \"*.js\" -o -name \"*.vue\" -o -name \"*.ts\" -o -name \"*.jsx\" -o -name \"*.tsx\" -o -name \"*.html\" -o -name \"*.css\" \\) | xargs -I {} sh -c 'echo \"File: {}\"; cat {}' > public/gpt.txt"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@layui/layui-vue": "^2.19.2", "axios": "^1.7.9", "dom-to-image": "^2.6.0", "element-plus": "^2.8.7", "html2canvas": "^1.4.1", "nanoid": "^5.0.9", "path": "^0.12.7", "pinia": "^2.2.6", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.5.12"}, "devDependencies": {"@eslint/js": "^9.13.0", "@vitejs/plugin-vue": "^5.1.4", "@vue/eslint-config-prettier": "^10.0.0", "eslint": "^9.13.0", "eslint-plugin-vue": "^9.29.0", "fast-glob": "^3.3.2", "less": "^4.2.0", "less-loader": "^12.2.0", "prettier": "^3.3.3", "prettier-plugin-organize-attributes": "^1.0.0", "vite": "^5.4.10", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgstring": "^1.0.0", "vite-plugin-vue-devtools": "^7.6.8", "vite-plugin-vue-setup-extend": "^0.4.0"}}