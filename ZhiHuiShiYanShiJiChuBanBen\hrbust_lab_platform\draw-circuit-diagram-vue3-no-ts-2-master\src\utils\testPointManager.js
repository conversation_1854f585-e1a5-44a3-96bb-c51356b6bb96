/**
 * src/utils/testPointManager.js
 * 测点连接关系管理工具
 */

import { ElMessage } from 'element-plus'
import { exportTestPointRelations } from '@/utils/canvasDataManager'
import { validateURLParam } from '@/utils/before/validateURLParam'
import { saveTestPointRelations } from '@/api/testPointRelations'

/**
 * 获取测点连接关系数据
 * @returns {Object} 解析后的测点连接关系数据对象
 */
export const getTestPointRelations = () => {
  try {
    // 从localStorage获取测点连接关系数据
    const testPointRelationsJSON = localStorage.getItem('test_point_relations')
    if (!testPointRelationsJSON) {
      console.warn('未找到测点连接关系数据')
      return null
    }
    
    // 解析JSON数据
    return JSON.parse(testPointRelationsJSON)
  } catch (error) {
    console.error('获取测点连接关系数据时出错:', error)
    ElMessage.error('获取测点连接关系数据失败')
    return null
  }
}

/**
 * 获取最新的测点连接关系数据
 * 直接从组件和连接中计算，而不是从localStorage获取
 * @returns {Object} 当前最新的测点连接关系数据
 */
export const getCurrentTestPointRelations = () => {
  try {
    // 导出当前测点连接关系数据
    const testPointRelationsJSON = exportTestPointRelations()
    return JSON.parse(testPointRelationsJSON)
  } catch (error) {
    console.error('获取当前测点连接关系数据时出错:', error)
    ElMessage.error('获取当前测点连接关系数据失败')
    return null
  }
}

/**
 * 验证学生绘制的电路与模板的测点连接关系是否匹配
 * @param {Object} templateRelations - 模板的测点连接关系数据
 * @param {Object} studentRelations - 学生绘制的测点连接关系数据
 * @returns {Object} 验证结果，包含是否匹配、匹配的测点、不匹配的测点等信息
 */
export const validateTestPointRelations = (templateRelations, studentRelations) => {
  if (!templateRelations || !studentRelations) {
    return {
      isValid: false,
      message: '模板或学生数据不存在',
      matchedPoints: [],
      unmatchedPoints: []
    }
  }

  try {
    const templateConnections = templateRelations.testPointConnections || []
    const studentConnections = studentRelations.testPointConnections || []
    
    // 用于存储验证结果
    const matchedPoints = []
    const unmatchedPoints = []
    
    // 根据测点标识符创建映射
    const templateMap = {}
    templateConnections.forEach(conn => {
      const testPoint = conn.testPoint
      const key = `${testPoint.name}:${testPoint.identifier || ''}`
      
      if (!templateMap[key]) {
        templateMap[key] = []
      }
      
      templateMap[key].push({
        deviceName: conn.connectedDevice.name,
        deviceIdentifier: conn.connectedDevice.identifier || '',
        connectionPoint: conn.connectedDevice.connectionPoint || ''
      })
    })
    
    // 验证学生的每个测点连接
    studentConnections.forEach(conn => {
      const testPoint = conn.testPoint
      const key = `${testPoint.name}:${testPoint.identifier || ''}`
      
      // 检查该测点是否存在于模板中
      if (!templateMap[key]) {
        unmatchedPoints.push({
          testPoint: testPoint,
          reason: '测点在模板中不存在'
        })
        return
      }
      
      // 检查连接的设备是否匹配
      const deviceName = conn.connectedDevice.name
      const deviceIdentifier = conn.connectedDevice.identifier || ''
      const connectionPoint = conn.connectedDevice.connectionPoint || ''
      
      // 查找匹配的设备连接
      const matchedDevice = templateMap[key].find(device => 
        device.deviceName === deviceName && 
        device.deviceIdentifier === deviceIdentifier &&
        (device.connectionPoint === connectionPoint || !device.connectionPoint || !connectionPoint)
      )
      
      if (matchedDevice) {
        matchedPoints.push({
          testPoint: testPoint,
          connectedDevice: conn.connectedDevice
        })
      } else {
        unmatchedPoints.push({
          testPoint: testPoint,
          connectedDevice: conn.connectedDevice,
          reason: '连接的设备与模板不匹配'
        })
      }
    })
    
    // 计算匹配率
    const matchRate = templateConnections.length > 0 
      ? matchedPoints.length / templateConnections.length 
      : 0
    
    return {
      isValid: unmatchedPoints.length === 0 && matchedPoints.length === templateConnections.length,
      matchRate: matchRate,
      message: `匹配率: ${(matchRate * 100).toFixed(1)}%`,
      matchedPoints,
      unmatchedPoints
    }
  } catch (error) {
    console.error('验证测点连接关系时出错:', error)
    return {
      isValid: false,
      message: `验证过程中发生错误: ${error.message}`,
      matchedPoints: [],
      unmatchedPoints: []
    }
  }
}

/**
 * 为组件添加标准化的测点连接
 * @param {Object} component - 要添加测点连接的组件
 * @param {Array} testPointRelations - 测点连接关系数据
 * @returns {Object} 添加了连接信息的组件
 */
export const addTestPointConnectionsToComponent = (component, testPointRelations) => {
  if (!component || !testPointRelations || !testPointRelations.testPointConnections) {
    return component
  }
  
  // 只对特定类型的组件处理
  if (!component.type || !['resistor', 'capacitor', 'inductor', 'diode', 'transistor'].includes(component.type)) {
    return component
  }
  
  // 查找与该组件相关的测点连接
  const relatedConnections = testPointRelations.testPointConnections.filter(conn => 
    conn.connectedDevice.name === component.name || 
    conn.connectedDevice.identifier === component.identifier
  )
  
  if (relatedConnections.length > 0) {
    component.testPointConnections = relatedConnections.map(conn => ({
      testPointName: conn.testPoint.name,
      testPointIdentifier: conn.testPoint.identifier,
      connectionPoint: conn.connectedDevice.connectionPoint
    }))
  }
  
  return component
}

/**
 * 保存测点连接关系数据到后端
 * @returns {Promise} - 返回保存操作的Promise
 */
export const saveTestPointRelationsToServer = async () => {
  try {
    // 获取URL参数
    const { userId, courseName, experimentName } = validateURLParam()
    
    // 生成测点连接关系数据
    const testPointRelationsJSON = exportTestPointRelations()
    const testPointRelations = JSON.parse(testPointRelationsJSON)
    
    if (!testPointRelations || !testPointRelations.testPointConnections || testPointRelations.testPointConnections.length === 0) {
      ElMessage.warning('没有测点连接关系数据可保存')
      return Promise.reject('没有测点连接关系数据')
    }
    
    // 构建请求参数
    const requestData = {
      userId,
      courseName,
      experimentName,
      testPointRelationsData: testPointRelationsJSON
    }
    
    // 调用API保存数据
    return saveTestPointRelations(requestData)
      .then((res) => {
        const { code, msg } = res.data
        if (code === 200) {
          ElMessage.success('测点连接关系数据保存成功')
          return res.data
        } else {
          ElMessage.error(`保存失败: ${msg || '未知错误'}`)
          return Promise.reject(msg || '保存测点连接关系失败')
        }
      })
      .catch((error) => {
        console.error('保存测点连接关系时出错:', error)
        ElMessage.error('保存测点连接关系失败')
        return Promise.reject(error)
      })
  } catch (error) {
    console.error('准备测点连接关系数据时出错:', error)
    ElMessage.error('准备测点连接关系数据失败')
    return Promise.reject(error)
  }
} 