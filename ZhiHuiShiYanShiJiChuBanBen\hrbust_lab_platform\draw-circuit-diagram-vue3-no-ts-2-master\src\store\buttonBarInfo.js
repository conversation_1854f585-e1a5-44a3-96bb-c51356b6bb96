import { computed, ref } from 'vue'
import { defineStore, storeToRefs } from 'pinia'
import { useSelectedComponentStore } from '@/store/selectedComponent' // 假设组件信息存储在这里
import { useLineModeStore } from '@/store/lineMode'
import { useTextBoxStore } from '@/store/textBox'
import { ElMessageBox } from 'element-plus'

export const useButtonBarInfoStore = defineStore('buttonBarInfo', () => {
  /**
   * store: selectedComponent、textBox
   */
  const { selectedComponent } = storeToRefs(useSelectedComponentStore()) // 当前是否有组件为选中状态
  const { isLineDrawing } = storeToRefs(useLineModeStore()) // 当前是否处于连线模式
  const { selectedTextBox } = storeToRefs(useTextBoxStore())

  /**
   * state
   */
  // 控制【编辑组件抽屉】的显示
  const editComponentDrawerVisible = ref(false)
  // 控制【编辑文本框表单】的显示
  const editTextBoxDrawerVisible = ref(false)

  /**
   * getters
   */
  // 根据选中的组件来设置【删除按钮】的状态
  const deleteComponentEnabled = computed(() => selectedComponent.value !== null && !isLineDrawing.value)
  // 根据选中的组件来设置【编辑按钮】的状态
  const editComponentEnabled = computed(() => selectedComponent.value !== null && !isLineDrawing.value)
  // 根据选中的组件来设置【编辑按钮】的状态
  const editTextBoxEnabled = computed(() => selectedTextBox.value !== null && !isLineDrawing.value)

  /**
   * action
   */

  // 打开抽屉
  const openDrawer = () => {
    console.log('@@@ 打开抽屉')
    editComponentDrawerVisible.value = true
  }

  // 关闭抽屉
  const closeDrawer = () => {
    console.log('@@@ 关闭抽屉')

    editComponentDrawerVisible.value = false
  }

  // 打开文本框表单
  const openTextBoxDrawer = () => {
    console.log('@@@ 打开文本框表单')
    // 组件自带的文本框只能通过【编辑组件按钮】进行修改
    console.log('@@@ selectedTextBox', selectedTextBox.value)
    if (!selectedTextBox.value.componentId) {
      editTextBoxDrawerVisible.value = true
    } else {
      ElMessageBox.alert('组件自带的文本框，只能通过【编辑组件】按钮进行修改！', '提示', {
        // autofocus: false,
        confirmButtonText: 'OK',
        type: 'warning',
      })
    }
  }

  // 关闭文本框表单
  const closeTextBoxDrawer = () => {
    console.log('@@@ 关闭文本框表单')
    editTextBoxDrawerVisible.value = false
  }

  return {
    editComponentDrawerVisible,
    editTextBoxDrawerVisible,
    deleteComponentEnabled,
    editComponentEnabled,
    editTextBoxEnabled,
    openDrawer,
    closeDrawer,
    openTextBoxDrawer,
    closeTextBoxDrawer,
  }
})
