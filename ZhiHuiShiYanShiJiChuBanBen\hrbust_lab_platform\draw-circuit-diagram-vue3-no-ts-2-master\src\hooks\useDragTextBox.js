import { storeToRefs } from 'pinia'
import { useTextBoxStore } from '@/store/textBox'
import { useCanvasInfoStore } from '@/store/canvasInfo'

export default function useDragTextBox() {
  // store: 获取 useTextBoxStore 实例，并解构
  const textBoxStore = useTextBoxStore()
  const { textBoxes } = storeToRefs(textBoxStore)
  const { updateTextBoxRelativeOffset } = textBoxStore

  // store: 获取 useCanvasInfoStore 实例，并解构
  const canvasInfo = useCanvasInfoStore()
  const { gridSpacing } = storeToRefs(canvasInfo)

  // 当前正在拖拽的文本框
  let currentDraggedTextBox = null
  let offsetX = 0
  let offsetY = 0

  // 开始拖拽
  const startTextBoxDrag = (textBox, event) => {
    event.preventDefault()

    // 记录当前文本框和鼠标位置
    currentDraggedTextBox = textBox
    offsetX = event.clientX - textBox.offsetX
    offsetY = event.clientY - textBox.offsetY

    // 绑定鼠标移动和松开事件
    window.addEventListener('mousemove', handleTextBoxMouseMove)
    window.addEventListener('mouseup', stopTextBoxDrag)
  }

  // 处理鼠标移动时文本框的位置更新
  const handleTextBoxMouseMove = (event) => {
    if (!currentDraggedTextBox) return

    // 计算文本框的新位置
    let newX = event.clientX - offsetX
    let newY = event.clientY - offsetY

    // 按网格大小对位置进行对齐（20px为单位）
    newX = Math.round(newX / 5) * 5
    newY = Math.round(newY / 5) * 5

    // 更新文本框的位置
    currentDraggedTextBox.offsetX = newX
    currentDraggedTextBox.offsetY = newY

    // 重新计算文本框的相对偏移量
    updateTextBoxRelativeOffset(currentDraggedTextBox.componentId, currentDraggedTextBox)
  }

  // 停止拖拽
  const stopTextBoxDrag = () => {
    // 重新计算文本框的相对偏移量
    updateTextBoxRelativeOffset(currentDraggedTextBox.componentId, currentDraggedTextBox)

    currentDraggedTextBox = null
    offsetX = 0
    offsetY = 0

    // 移除鼠标移动和松开事件
    window.removeEventListener('mousemove', handleTextBoxMouseMove)
    window.removeEventListener('mouseup', stopTextBoxDrag)
  }

  return {
    startTextBoxDrag,
    handleTextBoxMouseMove,
    stopTextBoxDrag,
  }
}
