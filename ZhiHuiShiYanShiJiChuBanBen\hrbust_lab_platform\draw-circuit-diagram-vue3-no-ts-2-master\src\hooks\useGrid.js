import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useCanvasInfoStore } from '@/store/canvasInfo'

export default function () {
  const canvasInfo = useCanvasInfoStore()
  const { canvasSize, gridSpacing } = storeToRefs(canvasInfo)

  const gridLines = computed(() => {
    const lines = []
    // 根据动态缩放后的画布尺寸生成网格
    for (let x = 0; x <= canvasSize.value.width; x += gridSpacing.value) {
      lines.push({
        key: `v${x}`,
        x1: x,
        y1: 0,
        x2: x,
        y2: canvasSize.value.height,
      })
    }
    for (let y = 0; y <= canvasSize.value.height; y += gridSpacing.value) {
      lines.push({
        key: `h${y}`,
        x1: 0,
        y1: y,
        x2: canvasSize.value.width,
        y2: y,
      })
    }
    return lines
  })

  return { gridLines }
}
