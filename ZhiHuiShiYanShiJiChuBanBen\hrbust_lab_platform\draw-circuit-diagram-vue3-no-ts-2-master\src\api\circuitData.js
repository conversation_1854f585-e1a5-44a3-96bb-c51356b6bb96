import service from '@/utils/request'

export function get(data) {
  return service({
    url: '/circuitData/get',
    method: 'post',
    data,
  })
}

export function addOrUpdate(data) {
  return service({
    url: '/circuitData/addOrUpdate',
    method: 'post',
    data,
  })
}

export function remove(data) {
  return service({
    url: '/circuitData/delete',
    method: 'post',
    data,
  })
}

// 获取所有数据，当初只为测试，先不用这个
function getAll() {
  return service({
    url: '/circuitData/getAll',
    method: 'get',
  })
}
