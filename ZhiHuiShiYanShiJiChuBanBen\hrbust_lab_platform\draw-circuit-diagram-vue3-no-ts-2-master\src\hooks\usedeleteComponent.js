import { storeToRefs } from 'pinia'
import { useComponentsInfoStore } from '@/store/componentsInfo'
import { useSelectedComponentStore } from '@/store/selectedComponent'
import { useTextBoxStore } from '@/store/textBox'

export default function () {
  // store: 画布中的所有组件 components
  const { components } = storeToRefs(useComponentsInfoStore())

  // store: 被选中的组件 selectedComponent
  const selectedComponentStore = useSelectedComponentStore()
  const { selectedComponent } = storeToRefs(selectedComponentStore)
  const { clearSelectedComponent } = selectedComponentStore

  // store: 文本框 textBox
  const textBoxStore = useTextBoxStore()
  const { textBoxs } = storeToRefs(textBoxStore)

  /**
   * 删除组件
   */
  const deleteComponent = () => {
    // 获取到组件数据
    const _component = selectedComponent.value

    // 从画布中删除组件
    const index = components.value.findIndex((item) => item.componentId === selectedComponent.value.componentId)
    components.value.splice(index, 1)

    // 清除选中组件
    clearSelectedComponent()

    // 删除对应的文本框
    const componentId = _component.componentId
    // 从textBoxs中删除组件对应的所有文本框
    const textBoxIndex = textBoxs.value.filter((textBox) => textBox.componentId === componentId)
    if (textBoxIndex.length > 0) {
      textBoxIndex.forEach((textBox) => {
        const textBoxDeleteIndex = textBoxs.value.findIndex((textBoxItem) => textBoxItem.id === textBox.id)
        if (textBoxDeleteIndex !== -1) {
          textBoxs.value.splice(textBoxDeleteIndex, 1) // 删除对应的文本框
        }
      })
    }
  }
  return { deleteComponent }
}
