<template>
   <!-- 添加缩放提示 -->
   <div class="zoom-warning" v-if="showZoomWarning">
      <span>⚠️ 为了保证测点定位准确，请将浏览器缩放设置为100%</span>
      <button class="close-btn" @click="closeWarning">×</button>
    </div>
  <div class="common-layout">
    <el-container class="layout-container">
      <!-- 左侧元器件库 -->
      <el-aside class="layout-aside" width="200px">
        <components-panel />
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="layout-main">
        <!-- 标题 -->
        <!--<div class="header">
          <Header />
        </div>-->
        <!-- 按钮工具栏 -->
        <div class="button-tool-bar">
          <button-tool-bar />
        </div>
        <!-- 画布区域 -->
        <canvas-area :components="components" @add-component="addComponentToCanvas" />
      </el-main>

      <!-- 抽屉 -->
      <edit-component />

      <!-- 编辑文本框 -->
      <edit-text-box />
    </el-container>
  </div>
</template>

<script setup name="App">
  import { storeToRefs } from 'pinia'
  import { onMounted, onUnmounted } from 'vue'
  import { getCircuitDataAndLoadData } from '@/utils/before/validateURLParam.js'
  import { saveData } from '@/utils/canvasDataManager.js'
  import { ref } from 'vue'

  // 缩放警告相关状态和方法
  const showZoomWarning = ref(false)
  
  // 检查浏览器缩放
  const checkBrowserZoom = () => {
    // 检测浏览器缩放级别，如果不是100%则显示警告
    const zoom = Math.round((window.outerWidth / window.innerWidth) * 100)
    
    // 只有当缩放不是100%，且本次会话中没有显示过警告时才显示
    if (zoom !== 100 && !window.hasShownZoomWarning) {
      showZoomWarning.value = true;
      
      // 标记本次会话已经显示过警告
      window.hasShownZoomWarning = true;
      
      // 5秒后自动关闭警告
      setTimeout(() => {
        showZoomWarning.value = false;
      }, 5000);
    }
  }
  
  const closeWarning = () => {
    showZoomWarning.value = false
  }

  /**
   * store: 获取 components 状态
   */
  import { useComponentsInfoStore } from '@/store/componentsInfo.js'
  const { components } = storeToRefs(useComponentsInfoStore())

  /**
   * hooks: 添加组件到画布
   */
  import useAddComponentToCanvas from '@/hooks/useAddComponentToCanvas.js'
  const { addComponentToCanvas } = useAddComponentToCanvas()

  // 监听窗口大小变化
  onMounted(() => {
    // 检查浏览器缩放
    checkBrowserZoom()
    window.addEventListener('resize', checkBrowserZoom)

    // 监听页面失去焦点
    window.addEventListener('blur', handleBlur)
    // 监听页面获取焦点
    window.addEventListener('focus', handleFocus)
    // 离开页面之前操作【原页面的未完成 -> 已完成】
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 🔧 修复：页面首次加载时获取数据，避免双重GET请求
    getCircuitDataAndLoadData()
  })

  onUnmounted(() => {
    window.removeEventListener('resize', checkBrowserZoom)
    window.removeEventListener('blur', handleBlur)
    window.removeEventListener('focus', handleFocus)
    window.removeEventListener('beforeunload', handleBeforeUnload)
  })

  const handleBlur = () => {
    console.log('@@@ 页面 lost 焦点')
    // 收集当前电路图数据，并保存到数据库（收集数据并保存）
    saveData()
  }

  const handleFocus = () => {
    console.log('@@@ 页面 focus 焦点')
    // 🔧 修复：只有从实验环境返回时才重新加载数据
    const fromExperiment = sessionStorage.getItem('from_experiment_return')
    if (fromExperiment) {
      console.log('🔄 从实验环境返回，重新加载数据')
      sessionStorage.removeItem('from_experiment_return')
      getCircuitDataAndLoadData()
    } else {
      console.log('⏭️ 普通焦点变化，跳过数据加载')
    }
  }

  /**
   * 离开页面之前操作【原页面的未完成 -> 已完成】
   */
  const handleBeforeUnload = () => {
    if (window.opener != null && !window.opener.closed) {
      window.opener.lianxianwancheng(2) //调用主页面的函数
    }
  }
</script>

<style lang="less" scoped>
  /* 定义基础变量 */
  @aside-width: 240px;
  @background-color: #f3f6f8;
  @main-padding: 20px;
  @header-spacing: 20px; /* 标题与其他部分间距 */
  @tool-spacing: 15px; /* 工具栏与画布的间距 */
  
  /* 缩放警告样式 */
  .zoom-warning {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff8e1;
    border: 1px solid #ffd600;
    border-radius: 4px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 2000;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    
    span {
      margin-right: 16px;
      font-size: 14px;
      color: #b71c1c;
    }
    
    .close-btn {
      background: none;
      border: none;
      color: #757575;
      font-size: 18px;
      cursor: pointer;
      padding: 0 4px;
      
      &:hover {
        color: #212121;
      }
    }
  }

  /* 布局容器 */
  .layout-container {
    height: 100vh; /* 占满视口高度 */
    display: flex; /* 使用 flex 布局 */
    background-color: @background-color; /* 全局背景色 */
  }

  /* 左侧边栏 */
  .layout-aside {
    background-color: @background-color; /* 更新背景色 */
    min-width: @aside-width;
    max-width: @aside-width;
    overflow: auto; /* 内容超出时支持滚动 */
    border-right: 1px solid #dcdcdc; /* 添加右边框 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 可选：添加轻微阴影 */
  }

  /* 主内容区域 */
  .layout-main {
    flex: 1; /* 占用剩余空间 */
    padding: @main-padding;
    background-color: @background-color; /* 更新背景色 */
    overflow-y: auto; /* 超出时支持滚动 */
  }

  /* 标题部分样式 */
  .header {
    margin-bottom: @header-spacing; /* 标题与工具栏的间距 */
    text-align: center; /* 标题居中 */
  }

  /* 工具栏样式 */
  .button-tool-bar {
    margin-bottom: @tool-spacing; /* 工具栏与画布的间距 */
  }
 
</style>
