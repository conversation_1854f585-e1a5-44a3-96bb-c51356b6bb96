<template>
  <el-drawer
    class="component-edit-drawer"
    v-model="editComponentDrawerVisible"
    :key="drawerKey"
    :before-close="beforeClose"
    title="编辑组件属性"
    size="30%">
    <el-card class="component-edit-drawer-card" shadow="hover">
    	<!--组件为可变电阻是：标识必须为RN,组件为测试点点-->
      <h3 class="component-edit-drawer-card-h3">{{ selectedComponent?.identifier }}</h3>

      <el-form v-if="selectedComponent" :model="selectedComponent" label-width="80px">
        <el-form-item label="器件名称">
          <el-input v-model="selectedComponent.label" disabled="true" />
        </el-form-item>

        <el-form-item label="标识">
          <el-input v-model="selectedComponent.identifier"placeholder=""  />
        </el-form-item>

        <el-form-item label="值">
          <el-input v-model="selectedComponent.value" />
        </el-form-item>

        <el-form-item label="单位">
          <el-select v-model="selectedComponent.unit" placeholder="选择单位">
            <el-option v-for="unit in selectedComponent.unitList" :key="unit" :label="unit" :value="unit"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="缩放比例">
          <el-input-number v-model="selectedComponent.scale" :min="2" :max="2" :step="2" />
        </el-form-item>

        <el-form-item label="旋转率">
          <el-input-number v-model="selectedComponent.rotation" :min="-360" :max="360" :step="90" />
        </el-form-item>
      </el-form>
    </el-card>

    <div class="drawer-footer">
      <!-- <el-button type="primary" @click="confirmEditing">确认</el-button> -->
      <el-button @click="handleConfirm" type="success">确认更改</el-button>
    </div>
  </el-drawer>
</template>

<script setup name="EditComponent">
  import { ref, watchEffect } from 'vue'
  import { storeToRefs } from 'pinia'

  const drawerKey = ref(0)

  /**
   * store: 按钮工具栏状态
   */
  import { useButtonBarInfoStore } from '@/store/buttonBarInfo.js'
  const buttonBarInfoStore = useButtonBarInfoStore()
  const { editComponentDrawerVisible } = storeToRefs(buttonBarInfoStore)
  const { closeDrawer } = buttonBarInfoStore

  /**
   * store: 选中的组件
   */
  import { useSelectedComponentStore } from '@/store/selectedComponent.js'
  const selectedComponentStore = useSelectedComponentStore()
  const { selectedComponent } = storeToRefs(selectedComponentStore)
  const { clearSelectedComponent } = selectedComponentStore

  /**
   * store: 文本框
   */
  import { useTextBoxStore } from '@/store/textBox.js'
  const textBoxStore = useTextBoxStore()
  const { updateTextBoxContent } = textBoxStore

  /**
   * 处理【确认更改】
   */
  const handleConfirm = () => {
    // 手动关闭drawer
    closeDrawer()
    // 更新文本框内容
    updateTextBoxContent()
  }

  /**
   * beforeClose
   */
  const beforeClose = () => {
    // 手动关闭drawer
    closeDrawer()
    // 更新文本框内容
    updateTextBoxContent()
    // 清空选中的组件
    clearSelectedComponent()
  }

  watchEffect(() => {
    if (selectedComponent) {
      updateTextBoxContent()
    }
  })
</script>

<style scoped>
  /**
    抽屉
  */
  .component-edit-drawer {
    padding: 20px;
  }

  .component-edit-drawer-card {
    margin-top: -20px;
  }
  .component-edit-drawer-card-h3 {
    text-align: center;
    font-weight: bold;
    margin-bottom: 30px;
  }
  .drawer-footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  .drawer-footer .el-button {
    margin-left: 0px;
  }
</style>
