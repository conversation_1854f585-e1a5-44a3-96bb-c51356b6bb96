import { storeToRefs } from 'pinia'
import { useTextBoxStore } from '@/store/textBox'

export default function () {
  // store: 文本框
  const textBoxStore = useTextBoxStore()
  const { textBoxs } = storeToRefs(textBoxStore)
  const { createTextBox, removeTextBox } = textBoxStore

  /**
   * 添加自定义文本框
   */
  const addTextBox = () => {
    console.log('@@@ addTextBox')
    const content = '文本框'
    const type = 'custom'
    const newTextBox = createTextBox(content, type, 40, 40)
    textBoxs.value.push(newTextBox)
  }

  /**
   * 编辑自定义文本框
   */
  const editTextBox = (textBoxId, newContent) => {
    console.log('@@@ editTextBox')
    const textBox = textBoxs.value.find((tb) => tb.id === textBoxId)
    if (!textBox) {
      console.error('未找到对应的文本框')
      return
    }
    textBox.content = newContent
  }

  /**
   * 删除自定义文本框
   */
  const deleteTextBox = (textBox) => {
    console.log('@@@ deleteTextBox')
    removeTextBox(textBox)
  }

  return { addTextBox, editTextBox, deleteTextBox }
}
