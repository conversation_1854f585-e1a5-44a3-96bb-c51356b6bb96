# 🔧 版本切换指南

## 📍 统一切换位置

**只需要修改一个文件就能切换整个系统的版本！**

### 🎯 主要切换位置

**文件：** `src/config/validationConfig.js`

```javascript
// ========================================
// 🔧 版本切换配置 - 只需要修改这里！
// ========================================

/**
 * 连接关系校验版本选择
 * 
 * 'v1'   - 原始版本：使用包含匹配，可能存在组件类型混淆
 * 'v1.1' - 改进版本：使用精确匹配，避免组件类型混淆
 */
export const VALIDATION_VERSION = 'v1.1'  // 👈 在这里切换！
```

## 🔄 版本说明

### v1 版本 - 原始版本
- **匹配方式：** 包含匹配 (`type.includes('电阻')`)
- **优点：** 兼容性好，逻辑简单
- **缺点：** 可能出现组件类型混淆
  - 例如：`"无极性电容"` 被错误识别为 `"极性电容"`
  - 例如：`"可变电阻"` 被错误识别为 `"电阻"`

### v1.1 版本 - 改进版本
- **匹配方式：** 精确匹配 (`typeStr === '电阻'`)
- **优点：** 精确识别所有组件类型，避免混淆
- **缺点：** 需要维护完整的组件类型列表

## 📋 影响的功能

切换版本会影响以下功能：

1. **连接关系校验** (`src/store/verify.js`)
2. **模板保存功能** (`src/hooks/useSaveConnectionToTemplate.js`)
3. **测试页面** (`test_connection_versions.html`)

## 🧪 测试切换效果

### 1. 修改配置文件
```javascript
// 切换到 v1 版本
export const VALIDATION_VERSION = 'v1'

// 切换到 v1.1 版本
export const VALIDATION_VERSION = 'v1.1'
```

### 2. 查看测试页面
打开 `test_connection_versions.html` 查看不同版本的效果

### 3. 测试实际功能
- 绘制包含 "可变电阻" 和 "电阻" 的电路
- 绘制包含 "极性电容" 和 "无极性电容" 的电路
- 观察校验结果的差异

## ⚠️ 注意事项

1. **测试页面独立配置**
   - 测试页面有自己的版本配置：`test_connection_versions.html` 中的 `VALIDATION_VERSION`
   - 如果要测试，需要同时修改测试页面的配置

2. **数据库兼容性**
   - v1.1 版本生成的模板数据与 v1 版本不完全兼容
   - 建议在切换版本前备份重要的模板数据

3. **生产环境切换**
   - 在生产环境切换版本前，请充分测试
   - 建议先在开发环境验证所有功能正常

## 🔍 调试信息

在浏览器控制台中可以看到当前使用的版本信息：

```javascript
// 在任何地方调用
import { printCurrentConfig } from '@/config/validationConfig'
printCurrentConfig()
```

输出示例：
```
🔧 当前校验配置:
   校验版本: v1.1
   保存版本: both
   拓扑比较: 启用
   版本说明: 使用精确匹配进行组件类型规范化
```

## 📞 问题反馈

如果在版本切换过程中遇到问题：

1. 检查配置文件语法是否正确
2. 清除浏览器缓存重新加载
3. 查看浏览器控制台是否有错误信息
4. 确认所有相关文件都已正确修改
