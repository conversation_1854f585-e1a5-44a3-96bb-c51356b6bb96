import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useLineModeStore } from '@/store/lineMode'
import { useLineStateStore } from '@/store/lineState'
import { useCanvasInfoStore } from '@/store/canvasInfo'
import { useSelectedComponentStore } from '@/store/selectedComponent'
import { useTextBoxStore } from '@/store/textBox'

/**
 * 连线逻辑 Hooks
 */
export default function (canvas) {
  const lineModeStore = useLineModeStore()
  const { isLineDrawing } = storeToRefs(lineModeStore)

  const lineStateStore = useLineStateStore()

  const canvasInfo = useCanvasInfoStore()
  const { gridSpacing } = storeToRefs(canvasInfo)

  const selectedComponentStore = useSelectedComponentStore()
  const { clearSelectedComponent } = selectedComponentStore

  const textBoxStore = useTextBoxStore()
  const { clearSelectedTextBox } = textBoxStore

  // 状态变量
  const startPoint = ref(null) // 起点
  const currentPath = reactive([]) // 实时路径
  const highlightedPoint = ref(null) // 高亮的网格点
  const lastMousePosition = ref(null) // 鼠标最后位置
  const movementDirection = ref(null) // 鼠标移动方向

  /**
   * 键盘事件：按下 ESC 取消连线
   * @param {KeyboardEvent} event
   */
  const handleKeydown = (event) => {
    if (event.key === 'Escape') {
      resetLineDrawing()
    }
  }

  /**
   * 重置连线状态
   */
  const resetLineDrawing = () => {
    startPoint.value = null // 清空起点
    currentPath.splice(0, currentPath.length) // 清空动态路径
  }

  onMounted(() => {
    if (canvas.value) {
      canvas.value.addEventListener('keydown', handleKeydown)
      canvas.value.setAttribute('tabindex', '0') // 确保画布可接收键盘事件
      canvas.value.focus()
    }
  })
  onUnmounted(() => {
    if (canvas.value) {
      canvas.value.removeEventListener('keydown', handleKeydown)
    }
  })

  /**
   * 获取最近的网格点
   */
  const getClosestPoint = (x, y) => {
    const px = Math.round(x / gridSpacing.value) * gridSpacing.value
    const py = Math.round(y / gridSpacing.value) * gridSpacing.value
    return { x: px, y: py }
  }

  /**
   * 更新鼠标移动方向
   */
  const updateMovementDirection = (mouseX, mouseY) => {
    if (lastMousePosition.value) {
      const dx = mouseX - lastMousePosition.value.x
      const dy = mouseY - lastMousePosition.value.y
      movementDirection.value = Math.abs(dx) > Math.abs(dy) ? 'horizontal' : 'vertical'
    }
    lastMousePosition.value = { x: mouseX, y: mouseY }
  }

  /**
   * 鼠标移动事件
   */
  const onMouseMove = (e) => {
    if (!canvas.value || lineModeStore.lineMode === 'disabled') return

    const rect = canvas.value.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const closestPoint = getClosestPoint(mouseX, mouseY)

    highlightedPoint.value = closestPoint
    updateMovementDirection(mouseX, mouseY)

    if (startPoint.value) {
      currentPath.splice(0, currentPath.length)

      if (lineModeStore.lineMode === 'horizontal-first') {
        const midPoint = { x: closestPoint.x, y: startPoint.value.y }
        currentPath.push(startPoint.value, midPoint, closestPoint)
      } else if (lineModeStore.lineMode === 'vertical-first') {
        const midPoint = { x: startPoint.value.x, y: closestPoint.y }
        currentPath.push(startPoint.value, midPoint, closestPoint)
      } else if (lineModeStore.lineMode === 'smart') {
        const midPoint =
          movementDirection.value === 'horizontal' ?
            { x: closestPoint.x, y: startPoint.value.y }
          : { x: startPoint.value.x, y: closestPoint.y }
        currentPath.push(startPoint.value, midPoint, closestPoint)
      }
    }
  }

  /**
   * 鼠标点击事件
   */
  const onMouseClick = (e) => {
    if (!isLineDrawing.value) {
      // 非连线模式
      clearSelectedComponent()
      clearSelectedTextBox()
    } else {
      // 连线模式
      console.log('@@@ 触发画布点击事件')
      if (!canvas.value || lineModeStore.lineMode === 'disabled') return

      const rect = canvas.value.getBoundingClientRect()
      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top
      const clickedPoint = getClosestPoint(mouseX, mouseY)

      if (!startPoint.value) {
        startPoint.value = clickedPoint
      } else {
        const newLine = [startPoint.value, clickedPoint]
        const path = []

        if (lineModeStore.lineMode === 'horizontal-first') {
          const midPoint = { x: clickedPoint.x, y: startPoint.value.y }
          path.push(startPoint.value, midPoint, clickedPoint)
        } else if (lineModeStore.lineMode === 'vertical-first') {
          const midPoint = { x: startPoint.value.x, y: clickedPoint.y }
          path.push(startPoint.value, midPoint, clickedPoint)
        } else if (lineModeStore.lineMode === 'smart') {
          const midPoint =
            movementDirection.value === 'horizontal' ?
              { x: clickedPoint.x, y: startPoint.value.y }
            : { x: startPoint.value.x, y: clickedPoint.y }
          path.push(startPoint.value, midPoint, clickedPoint)
        }

        // 检测交点
        const intersections = findLineIntersections(newLine, lineStateStore.fixedLines)

        intersections.forEach((intersection) => {
          const { start, end, middle } = getLineEndpoints(newLine)
          const isEndpoint =
            (intersection.x === start.x && intersection.y === start.y) ||
            (intersection.x === end.x && intersection.y === end.y) ||
            middle.some((point) => point.x === intersection.x && point.y === intersection.y)

          if (isEndpoint) {
            lineStateStore.intersections.push(intersection) // 将交点存储起来
          }
        })

        // 打印新线段的所有经过点
        const allPoints = generateGridPoints(path)
        console.log('新线段的所有网格点:', allPoints)

        lineStateStore.addFixedLine(path)
        startPoint.value = null
        currentPath.splice(0, currentPath.length)
      }
    }
  }

  /**
   * 获取线段的起点、终点和中间点
   */
  const getLineEndpoints = (line) => {
    if (!line || line.length < 2) {
      throw new Error('Invalid line data: A line must contain at least two points.')
    }
    return {
      start: line[0],
      end: line[line.length - 1],
      middle: line.slice(1, line.length - 1),
    }
  }

  /**
   * 生成线段经过的所有网格点
   */
  const generateGridPoints = (line) => {
    const points = []
    const n = line.length

    for (let i = 0; i < n - 1; i++) {
      const start = line[i]
      const end = line[i + 1]

      // 起点为关键点
      if (points.length === 0 || points[points.length - 1].x !== start.x || points[points.length - 1].y !== start.y) {
        points.push({ x: start.x, y: start.y })
      }

      // 检测方向
      const dx = end.x - start.x
      const dy = end.y - start.y

      if (dx !== 0 && dy === 0) {
        // 水平线
        const step = dx > 0 ? gridSpacing.value : -gridSpacing.value
        for (let x = start.x + step; Math.abs(x - end.x) >= Math.abs(step); x += step) {
          points.push({ x, y: start.y })
        }
      } else if (dy !== 0 && dx === 0) {
        // 垂直线
        const step = dy > 0 ? gridSpacing.value : -gridSpacing.value
        for (let y = start.y + step; Math.abs(y - end.y) >= Math.abs(step); y += step) {
          points.push({ x: start.x, y })
        }
      }
    }

    // 添加最终的终点，避免重复
    const finalPoint = { x: line[n - 1].x, y: line[n - 1].y }
    if (points.length === 0 || points[points.length - 1].x !== finalPoint.x || points[points.length - 1].y !== finalPoint.y) {
      points.push(finalPoint)
    }

    return points
  }

  /**
   * 计算两条线段的交点
   */
  const findLineIntersections = (newLine, existingLines) => {
    const intersections = []

    existingLines.forEach((line) => {
      const newLinePoints = generateGridPoints(newLine)
      const existingLinePoints = generateGridPoints(line)

      const commonPoints = newLinePoints.filter((p1) => existingLinePoints.some((p2) => p1.x === p2.x && p1.y === p2.y))

      intersections.push(...commonPoints)
    })

    return intersections
  }

  return {
    currentPath,
    highlightedPoint,
    onMouseMove,
    onMouseClick,
    handleKeydown,
    getLineEndpoints,
    generateGridPoints,
    findLineIntersections,
  }
}
