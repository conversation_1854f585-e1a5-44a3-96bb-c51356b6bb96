import { ElMessageBox } from 'element-plus'
import { get as getCircuitData } from '@/api/circuitData'
import { get as getTemplate } from '@/api/circuitDataTemplate'
import { getRolesByUserId } from '@/api/sys-user'
import { loadData, saveData } from '@/utils/canvasDataManager'
import { checkUserExist } from '@/utils/before/checkUserExists'

/**
 * 校验参数合法性
 */
export function validateURLParam() {
  // 首先从URL获取参数
  const urlParams = getURLParam()

  // 如果URL中有参数，保存到localStorage
  if (urlParams.userId || urlParams.courseName || urlParams.experimentName) {
    if (urlParams.userId) {
      localStorage.setItem('userId', urlParams.userId)
    }
    if (urlParams.courseName) {
      localStorage.setItem('courseName', urlParams.courseName)
    }
    if (urlParams.experimentName) {
      localStorage.setItem('experimentName', urlParams.experimentName)
    }
  }

  // 然后从localStorage获取参数（确保一致性）
  const { userId, courseName, experimentName } = getLocalStorgeParam()

  // // 校验参数合法性：不合法就给提示然后关闭页面
  // const isValid = (value) => value && value.trim() !== ''

  // if (![userId, courseName, experimentName].every(isValid)) {
  //   ElMessageBox.alert('URL 参数不正确！ <br> 点击<strong>【收到】</strong>后，将<strong>【自动关闭】</strong>此页面！', '警告', {
  //     confirmButtonText: '收到',
  //     type: 'warning',
  //     dangerouslyUseHTMLString: true,
  //     callback: () => {
  //       window.close()
  //     },
  //   })
  // }

  // 校验是否是系统用户
  // checkUserExist()

  // 参数合法，返回
  return { userId, courseName, experimentName }
}

/**
 * 🔧 检查用户角色，判断是否为老师/管理员
 */
async function checkUserRole() {
  const userId = localStorage.getItem('userId')

  // 临时修改：如果是admin用户，直接返回true
  if (userId === 'admin' || userId === 'Admin' || userId === 'ADMIN') {
    return true
  }

  if (!userId) {
    return false // 没有用户ID，默认为学生
  }

  try {
    const response = await getRolesByUserId(userId)
    if (response && response.data && response.data.code === 200) {
      const roleList = response.data.data
      // 检查是否包含老师/管理员角色
      const teacherRoles = ['系统超管', '教师', '管理员', 'admin', 'teacher', 'Admin', 'ADMIN']
      return teacherRoles.some(role => roleList.includes(role))
    }
    return false
  } catch (error) {
    console.warn('角色检查失败，默认为学生角色:', error)
    return false // 出错时默认为学生
  }
}

/**
 * 从数据库中查询电路图参数, 并加载到 store
 * 🎯 根据用户角色选择不同的API接口
 */
export async function getCircuitDataAndLoadData() {
  try {
    // 🔧 首先检查用户角色
    const isTeacher = await checkUserRole()
    console.log('🔍 页面初始化 - 用户角色检查结果:', isTeacher ? '老师/管理员' : '学生')

    // 请求体
    const urlParams = validateURLParam()

    // 🎯 根据角色选择不同的API接口
    const apiFunction = isTeacher ? getTemplate : getCircuitData
    const apiName = isTeacher ? '/circuitDataTemplate/get' : '/circuitData/get'
    console.log('🔍 页面初始化 - 使用API接口:', apiName)

    // 请求数据
    const res = await apiFunction(urlParams)
    const { code, msg, data } = res.data

    console.log('🔍 API响应完整数据:', res.data)
    console.log('🔍 data字段内容:', data)

    if (code == 200) {
      if (isTeacher) {
        // 老师加载模板数据
        if (data && data.circuitData) {
          console.log('🔍 老师用户 - 加载完整电路数据')

          // 🔧 添加调试：检查电路数据中的组件标识符
          try {
            const circuitDataObj = JSON.parse(data.circuitData)
            console.log('🔍 电路数据解析结果:', circuitDataObj)

            if (circuitDataObj.components) {
              console.log('🔍 组件数量:', circuitDataObj.components.length)
              circuitDataObj.components.forEach((comp, index) => {
                if (comp.type === 'resistor') {
                  console.log(`🔍 电阻组件 ${index}:`, {
                    type: comp.type,
                    identifier: comp.identifier,
                    label: comp.label,
                    id: comp.id || comp.componentId
                  })
                }
              })
            }
          } catch (parseError) {
            console.error('❌ 解析电路数据失败:', parseError)
          }

          loadData(data.circuitData) // 从完整电路数据加载
        } else if (data && data.connections) {
          console.log('🔍 老师用户 - 加载连接关系数据')
          console.log('🔍 原始connections数据:', data.connections)

          try {
            const parsedConnections = JSON.parse(data.connections)
            console.log('🔍 解析后的connections:', parsedConnections)

            // 🔧 修复：兼容旧格式的模板数据，构建正确的数据格式
            const templateData = {
              components: [],
              fixedLines: [],
              intersections: [],
              textBoxs: [],
              connections: parsedConnections // 保留连接关系数据
            }
            console.log('🔍 构建的templateData:', templateData)
            loadData(JSON.stringify(templateData))
          } catch (parseError) {
            console.error('❌ 解析connections数据失败:', parseError)
            console.log('🔍 老师用户 - connections解析失败，创建空数据')
            saveData()
          }
        } else {
          console.log('🔍 老师用户 - 没有模板数据，创建空数据')
          saveData()
        }
      } else {
        // 学生加载个人数据
        if (data && data.circuitData) {
          console.log('🔍 学生用户 - 加载个人电路数据')
          loadData(data.circuitData) // 从个人数据加载
        } else {
          console.log('🔍 学生用户 - 没有个人数据，创建空数据')
          saveData()
        }
      }
    }
  } catch (error) {
    console.error('❌ 页面初始化数据加载失败:', error)
    // 出错时创建空数据
    saveData()
  }
}

export function getURLParam() {
  // 获取 URL 查询参数
  const urlParams = new URLSearchParams(window.location.search)
  const userId = urlParams.get('userId') || ''
  const courseName = urlParams.get('courseName') || ''
  const experimentName = urlParams.get('experimentName') || ''

  return { userId, courseName, experimentName }
}

export function getLocalStorgeParam() {
  // 从本地存储中获取参数
  const userId = localStorage.getItem('userId')
  const courseName = localStorage.getItem('courseName')
  const experimentName = localStorage.getItem('experimentName')

  return { userId, courseName, experimentName }
}
