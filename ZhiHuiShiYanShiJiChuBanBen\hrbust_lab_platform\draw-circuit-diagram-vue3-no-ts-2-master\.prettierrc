{"plugins": ["prettier-plugin-organize-attributes"], "printWidth": 130, "organizeAttributes": {"maxAttrsOnTag": 3}, "arrowParens": "always", "bracketSameLine": true, "bracketSpacing": true, "semi": false, "experimentalTernaries": true, "singleQuote": true, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": true, "proseWrap": "preserve", "insertPragma": false, "requirePragma": false, "tabWidth": 2, "useTabs": false, "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.json", "options": {"tabWidth": 2, "useTabs": false, "printWidth": 80, "trailingComma": "none", "singleQuote": false}}]}