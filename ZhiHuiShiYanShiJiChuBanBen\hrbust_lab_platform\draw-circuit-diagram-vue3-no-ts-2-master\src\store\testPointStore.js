import { defineStore } from 'pinia'

export const useTestPointStore = defineStore('testPoints', {
  state: () => ({
    points: []
  }),
  
  actions: {
    addPoint(point) {
      // 确保使用网格坐标
      if (point.x !== undefined && point.gridX === undefined) {
        // 如果提供的是像素坐标，转换为网格坐标
        const gridSize = 20
        point.gridX = Math.round(point.x / gridSize)
        point.gridY = Math.round(point.y / gridSize)
        // 删除像素坐标
        delete point.x
        delete point.y
      }
      
      this.points.push(point)
    },
    
    getAllPoints() {
      // 确保返回的所有点都使用网格坐标
      return this.points.map(point => {
        // 如果点仍然有x/y属性，转换为gridX/gridY
        if (point.x !== undefined && point.gridX === undefined) {
          const gridSize = 20
          return {
            id: point.id,
            gridX: Math.round(point.x / gridSize),
            gridY: Math.round(point.y / gridSize),
            label: point.label
          }
        }
        return point
      })
    },
    
    saveToSessionStorage() {
      // 保存到sessionStorage时确保使用网格坐标
      const points = this.getAllPoints()
      sessionStorage.setItem('circuit_test_points', JSON.stringify(points))
      console.log('保存测试点到sessionStorage:', points)
    }
  }
}) 