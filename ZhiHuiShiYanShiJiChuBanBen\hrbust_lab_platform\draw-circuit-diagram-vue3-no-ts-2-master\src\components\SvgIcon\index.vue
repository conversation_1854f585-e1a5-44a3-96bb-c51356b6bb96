<template>
  <svg :style="{ width: props.width, height: props.height }">
    <use :xlink:href="props.prefix + props.name" :fill="props.color"></use>
  </svg>
</template>

<script setup name="SvgIcon">
  /**
   * 接收父组件传来的参数
   */
  const props = defineProps({
    // xlink:href 属性值前缀
    prefix: {
      type: String,
      default: '#icon-',
    },
    // 图标使用的名字
    name: String,
    // 图标的填充颜色
    color: {
      type: String,
      default: '',
    },
    //图标宽度
    width: {
      type: String,
      default: '16px',
    },
    // 图标高度
    height: {
      type: String,
      default: '16px',
    },
  })
</script>
