# 测点自动识别与隐藏标识系统设计方案

## 1. 功能概述

本方案旨在实现电路图中测点的自动识别与隐藏标识功能。系统会在底层自动识别测点周围的器件组合，判断测点的逻辑标识（如A点、B点），但在界面上默认不显示这些标识，从而简化操作流程，减少人为错误。

### 核心功能

- **测点自动识别**：根据测点周围的器件组合自动判断测点的逻辑标识
- **隐藏标识显示**：在UI界面上默认不显示标识，减少视觉干扰
- **底层数据保存**：在数据层保留测点的逻辑标识，用于后续的匹配和验证
- **可选的显示控制**：提供开关，在需要时可以显示测点的逻辑标识

## 2. 设计原理

### 2.1 测点标识分离

将测点的标识分为两个独立的属性：
- **显示标签(label)**：显示在UI界面上的标签，默认为空
- **逻辑标签(logicalLabel)**：底层存储的逻辑标识，用于系统识别和处理

### 2.2 周围器件分析

系统通过分析测点周围（上、下、左、右）的器件类型、参数等信息，匹配预定义的规则，从而推断测点的逻辑标识。

例如：
- 如果测点左侧是电容C1，右侧是电阻R9，则可能是"B点"
- 如果测点左侧是电源，右侧是电阻，则可能是"A点"

### 2.3 规则匹配系统

采用灵活的规则匹配系统，支持：
- 基于器件类型的匹配
- 基于器件参数的匹配
- 基于多种器件组合模式的匹配
- 规则优先级和冲突解决

## 3. 技术架构

### 3.1 核心组件

- **testPointAutoRecognition.js**：测点自动识别引擎
- **testPointMappingRules.js**：测点标识映射规则配置
- **canvasDataManager.js**：画布数据管理，集成测点识别功能
- **TestPoint.vue**：测点组件，控制标识的显示逻辑
- **appConfig.js**：应用配置，提供全局控制选项

### 3.2 数据流程

1. **测点创建**：用户在画布上创建测点
2. **周围器件分析**：系统分析测点周围的器件组合
3. **规则匹配**：匹配预定义规则，确定逻辑标识
4. **数据存储**：将逻辑标识存储在测点对象中，但不显示
5. **可选显示**：在需要时通过配置选项显示逻辑标识
6. **导出验证**：在导出和验证时使用逻辑标识

## 4. 实现步骤

### 4.1 测点自动识别引擎

```javascript
// src/utils/testPointAutoRecognition.js

/**
 * 根据周围器件自动识别测点的物理位置标识
 * @param {Object} surroundingComponents - 测点周围的器件信息
 * @returns {Object} 包含识别结果但不显示在UI上的结果
 */
export function recognizeTestPoint(surroundingComponents) {
  // 没有周围器件信息时返回null
  if (!surroundingComponents) return { logicalLabel: null, displayLabel: '' };
  
  // 匹配规则列表
  const recognitionRules = [
    {
      // B点规则：左侧是电容，右侧是电阻
      match: (components) => {
        return components.left?.type === 'capacitor' && 
               components.right?.type === 'resistor';
      },
      logicalLabel: 'B'
    },
    // 其他规则...
  ];
  
  // 逐个尝试匹配规则
  for (const rule of recognitionRules) {
    if (rule.match(surroundingComponents)) {
      return {
        logicalLabel: rule.logicalLabel,  // 底层存储的标识，如"B"
        displayLabel: ''  // 界面上显示的标识，设为空
      };
    }
  }
  
  // 没有匹配的规则
  return { logicalLabel: null, displayLabel: '' };
}
```

### 4.2 测点标识映射规则配置

```javascript
// src/config/testPointMappingRules.js
export const testPointMappingRules = [
  {
    id: 'point_B',
    label: 'B',
    description: 'B点 - 电容和电阻之间的连接点',
    conditions: {
      left: { type: 'capacitor' },  // 左侧是电容
      right: { type: 'resistor' },  // 右侧是电阻
      top: null,  // 上方无特定要求
      bottom: null  // 下方无特定要求
    }
  },
  {
    id: 'point_A',
    label: 'A',
    description: 'A点 - 电源和电阻之间的连接点',
    conditions: {
      left: { type: 'voltage_source' },  // 左侧是电源
      right: { type: 'resistor' },  // 右侧是电阻
      top: null,
      bottom: null
    }
  },
  // 可以添加更多规则...
]
```

### 4.3 修改测点提取函数

```javascript
// src/utils/canvasDataManager.js

// 导入自动识别模块
import { recognizeTestPoint } from '@/utils/testPointAutoRecognition';

const extractTestPoints = (components) => {
  // 现有的筛选测点组件代码...
  
  return testPointComponents.map(point => {
    // 获取周围器件信息
    const surroundingInfo = findSurroundingComponents(point, components);
    
    // 自动识别测点标识
    const recognition = recognizeTestPoint(surroundingInfo);
    
    // 构建测点结果，分离显示标签和逻辑标签
    const result = {
      x: (point.x / canvasWidth) * 100,
      y: (point.y / canvasHeight) * 100,
      id: point.componentId || point.id || `tp_${Math.random().toString(36).substr(2, 9)}`,
      // 显示标签（UI上显示）- 为空
      label: recognition.displayLabel,
      // 逻辑标签（底层存储）
      logicalLabel: recognition.logicalLabel,
      identifier: point.identifier,
      value: point.value,
      surroundingComponents: surroundingInfo
    };
    
    console.log('测点自动识别结果:', point.id, 'UI标签:', recognition.displayLabel, '逻辑标签:', recognition.logicalLabel);
    
    return result;
  });
}
```

### 4.4 修改测点组件显示控制

```vue
<!-- src/components/circuit/TestPoint.vue -->
<template>
  <div class="test-point" :style="{ ... }">
    <div class="point-circle"></div>
    
    <!-- 只有当指定要显示时才显示标签 -->
    <div v-if="showLabels && component.label" class="point-label">
      {{ component.label }}
    </div>
    
    <!-- 测点周围组件信息 (鼠标悬停时显示) -->
    <div v-if="showSurroundingInfo" class="surrounding-info">
      <!-- 周围器件信息 -->
      <!-- ... -->
      
      <!-- 在悬停时可以选择性显示逻辑标签 -->
      <div v-if="component.logicalLabel" class="logical-label-info">
        <span class="logical-label-text">逻辑点位: {{ component.logicalLabel }}</span>
      </div>
    </div>
  </div>
</template>
```

### 4.5 添加全局配置

```javascript
// src/config/appConfig.js
import { reactive } from 'vue';

export const appConfig = reactive({
  // 默认不在UI上显示测点逻辑标签
  showTestPointLabels: false,
  
  // 切换是否显示测点标签
  toggleTestPointLabels(show) {
    this.showTestPointLabels = show !== undefined ? show : !this.showTestPointLabels;
  }
});
```

### 4.6 测点验证机制更新

```javascript
// src/utils/testPointManager.js

export const validateTestPointRelations = (templateRelations, studentRelations) => {
  // ...
  
  // 创建映射时使用逻辑标签
  templateConnections.forEach(conn => {
    const testPoint = conn.testPoint;
    // 使用逻辑标签作为键
    const key = `${testPoint.logicalLabel || ''}:${testPoint.identifier || ''}`;
    
    if (!templateMap[key]) {
      templateMap[key] = [];
    }
    
    // ...
  });
  
  // ...
};
```

## 5. 使用指南

### 5.1 教师操作流程

1. **创建模板**：教师在实验预设阶段创建电路模板
2. **添加测点**：在关键位置添加测点，系统自动识别其逻辑标识
3. **保存模板**：保存模板，系统会在底层保存测点的逻辑标识
4. **验证测点**：如需验证测点识别是否正确，可以临时开启标识显示

### 5.2 学生操作流程

1. **创建电路**：学生根据实验要求创建电路
2. **添加测点**：在关键位置添加测点
3. **系统验证**：系统在底层自动识别测点的逻辑标识，并与模板进行匹配
4. **查看反馈**：学生查看系统反馈的匹配结果

### 5.3 标识显示控制

在需要查看测点逻辑标识时，可以通过设置面板中的开关临时显示标识：

```vue
<el-switch
  v-model="showLogicalLabels"
  active-text="显示测点逻辑标识"
  inactive-text="隐藏测点逻辑标识"
  @change="toggleLabels"
/>
```

## 6. 扩展与优化建议

### 6.1 高级规则匹配

- **多模式匹配**：支持一个逻辑点位对应多种器件组合模式
- **参数条件**：基于器件参数进行更精准的匹配
- **相对位置**：考虑器件之间的相对位置关系
- **多器件组合**：支持复杂电路中的多器件组合识别

### 6.2 UI优化

- **悬停提示**：鼠标悬停时显示测点的逻辑标识
- **高亮关联**：在识别或选择测点时，高亮显示相关联的器件
- **可视化配置**：提供可视化的规则配置界面

### 6.3 验证增强

- **部分匹配**：支持部分正确的测点连接验证
- **错误提示**：提供详细的测点连接错误提示
- **学习反馈**：基于学生的测点连接错误提供学习建议

## 7. 结论

测点自动识别与隐藏标识系统能够极大地简化电路实验中的测点标识流程，减少人为错误，提高教学效率。系统通过智能分析测点周围的器件组合，在底层自动识别测点的逻辑标识，同时在界面上保持简洁，实现了技术与用户体验的完美结合。 