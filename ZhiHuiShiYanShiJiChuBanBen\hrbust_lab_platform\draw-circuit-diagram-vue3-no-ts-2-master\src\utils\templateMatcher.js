/**
 * 测点模板匹配工具
 * 用于保存教师模板和匹配学生作业
 */

import { ElMessage } from 'element-plus'
import { exportTestPointRelations } from '@/utils/canvasDataManager'
import { getStudentTestPointRelations } from '@/api/testPointRelations'
import { validateURLParam } from '@/utils/before/validateURLParam'

/**
 * 保存当前电路为教师模板
 * @param {String} templateName - 模板名称
 * @returns {Object} 保存的模板数据
 */
export const saveAsTemplate = (templateName) => {
  try {
    // 导出当前测点连接关系
    const relationDataJSON = exportTestPointRelations()
    const relationData = JSON.parse(relationDataJSON)
    
    if (!relationData.groupedByTestPoint || Object.keys(relationData.groupedByTestPoint).length === 0) {
      ElMessage.warning('当前电路没有测点连接关系，无法保存为模板')
      return null
    }
    
    // 构建模板数据
    const template = {
      templateName: templateName,
      createdAt: new Date().toISOString(),
      version: "1.0",
      testPointRequirements: relationData.groupedByTestPoint
    }
    
    // 保存到localStorage（实际项目中应该保存到服务器）
    const templates = getStoredTemplates()
    templates[templateName] = template
    localStorage.setItem('circuit_templates', JSON.stringify(templates))

    // 将当前模板名称保存到sessionStorage，供导出PNG时使用
    sessionStorage.setItem('current_template_name', templateName)

    console.log('📝 保存模板成功:', template)
    console.log('📁 模板名称已保存到会话存储:', templateName)
    ElMessage.success(`模板 "${templateName}" 保存成功`)

    return template
  } catch (error) {
    console.error('保存模板时出错:', error)
    ElMessage.error('保存模板失败')
    return null
  }
}

/**
 * 获取存储的模板列表
 * @returns {Object} 模板列表
 */
export const getStoredTemplates = () => {
  try {
    const templatesJSON = localStorage.getItem('circuit_templates')
    return templatesJSON ? JSON.parse(templatesJSON) : {}
  } catch (error) {
    console.error('获取模板列表时出错:', error)
    return {}
  }
}

/**
 * 匹配学生作业与模板
 * @param {String} templateName - 模板名称
 * @returns {Object} 匹配结果
 */
export const matchWithTemplate = async (templateName) => {
  try {
    // 获取模板
    const templates = getStoredTemplates()
    const template = templates[templateName]

    if (!template) {
      ElMessage.error(`模板 "${templateName}" 不存在`)
      return null
    }

    // 🔧 优先使用后端返回的测点连接关系数据
    let studentData = null

    try {
      // 尝试从后端获取学生的测点连接关系
      const { userId, courseName, experimentName } = validateURLParam()

      const response = await getStudentTestPointRelations({
        userId: userId,
        courseId: courseName,
        experimentId: experimentName
      })

      if (response && response.data && response.data.testPointRelations) {
        studentData = response.data.testPointRelations
      } else {
        throw new Error('后端数据为空')
      }
    } catch (backendError) {
      // 后端获取失败，使用本地生成的数据作为备用
      const studentDataJSON = exportTestPointRelations()
      const localData = JSON.parse(studentDataJSON)
      studentData = localData.groupedByTestPoint
    }

    if (!studentData || Object.keys(studentData).length === 0) {
      ElMessage.warning('当前电路没有测点连接关系')
      return {
        isMatch: false,
        score: 0,
        details: [],
        message: '当前电路没有测点连接关系'
      }
    }

    // 执行匹配
    const matchResult = compareTestPointConnections(
      template.testPointRequirements,
      studentData
    )

    // 显示匹配结果
    if (matchResult.isMatch) {
      ElMessage.success(`匹配成功！得分: ${matchResult.score}%`)
    } else {
      ElMessage.warning(`匹配不完全，得分: ${matchResult.score}%`)
    }

    return matchResult
  } catch (error) {
    console.error('匹配模板时出错:', error)
    ElMessage.error('匹配模板失败')
    return null
  }
}

/**
 * 比较测点连接关系 - 严格按分组匹配
 * @param {Object} templateRequirements - 模板要求
 * @param {Object} studentConnections - 学生连接
 * @returns {Object} 比较结果
 */
const compareTestPointConnections = (templateRequirements, studentConnections) => {
  console.log('🔍 开始测点分组校验...')
  console.log('📋 模板分组JSON:', JSON.stringify(templateRequirements, null, 2))
  console.log('📋 用户分组JSON:', JSON.stringify(studentConnections, null, 2))

  // 提取模板中所有测点的器件类型组合
  const templateGroups = []
  Object.keys(templateRequirements).forEach(testPointId => {
    const deviceTypes = templateRequirements[testPointId].connectedDevices
      .map(d => d.typeName)
      .sort() // 排序确保顺序一致
    templateGroups.push({
      originalId: testPointId,
      deviceTypes: deviceTypes,
      deviceTypesStr: JSON.stringify(deviceTypes),
      deviceCount: deviceTypes.length
    })
  })

  // 提取学生电路中所有测点的器件类型组合
  const studentGroups = []
  Object.keys(studentConnections).forEach(testPointId => {
    const deviceTypes = studentConnections[testPointId].connectedDevices
      .map(d => d.typeName)
      .sort() // 排序确保顺序一致
    studentGroups.push({
      originalId: testPointId,
      deviceTypes: deviceTypes,
      deviceTypesStr: JSON.stringify(deviceTypes),
      deviceCount: deviceTypes.length
    })
  })



  const matchDetails = []
  let matchedPoints = 0
  const usedStudentGroups = new Set() // 记录已匹配的学生分组

  // 🔧 严格分组匹配：每个模板分组必须找到完全相同的学生分组
  templateGroups.forEach((templateGroup) => {
    let foundMatch = false

    // 在学生分组中寻找完全匹配的分组
    for (let studentIndex = 0; studentIndex < studentGroups.length; studentIndex++) {
      const studentGroup = studentGroups[studentIndex]

      // 如果这个学生分组已经被匹配过，跳过
      if (usedStudentGroups.has(studentIndex)) {
        continue
      }

      // 🔧 严格匹配：器件类型组合必须完全一致
      if (templateGroup.deviceTypesStr === studentGroup.deviceTypesStr) {
        // 找到完全匹配的分组！
        foundMatch = true
        matchedPoints++
        usedStudentGroups.add(studentIndex)

        matchDetails.push({
          templateId: templateGroup.originalId,
          studentId: studentGroup.originalId,
          status: 'correct',
          message: `✅ 分组匹配成功: 模板测点${templateGroup.originalId} ↔ 学生测点${studentGroup.originalId}`,
          expected: templateGroup.deviceTypes,
          actual: studentGroup.deviceTypes
        })

        break
      }
    }

    // 如果没有找到匹配的学生分组
    if (!foundMatch) {
      matchDetails.push({
        templateId: templateGroup.originalId,
        studentId: null,
        status: 'missing',
        message: `模板${templateGroup.originalId}测点 无匹配`,
        expected: templateGroup.deviceTypes,
        actual: []
      })
    }
  })

  // 检查学生是否有多余的分组
  studentGroups.forEach((studentGroup, studentIndex) => {
    if (!usedStudentGroups.has(studentIndex)) {
      matchDetails.push({
        templateId: null,
        studentId: studentGroup.originalId,
        status: 'extra',
        message: `学生${studentGroup.originalId}测点 多余`,
        expected: [],
        actual: studentGroup.deviceTypes
      })
    }
  })

  const totalPoints = templateGroups.length
  const score = totalPoints > 0 ? Math.round((matchedPoints / totalPoints) * 100) : 0

  // 🔧 严格匹配条件：
  // 1. 所有模板分组都必须找到匹配 (matchedPoints === totalPoints)
  // 2. 学生分组数量必须与模板一致 (studentGroups.length === totalPoints)
  // 3. 不能有多余的学生分组
  const isMatch = matchedPoints === totalPoints && studentGroups.length === totalPoints

  console.log(`📊 校验结果: ${isMatch ? '✅ 完全匹配' : '❌ 不匹配'} (${matchedPoints}/${totalPoints})`)

  return {
    isMatch: isMatch,
    score: score,
    matchedPoints: matchedPoints,
    totalPoints: totalPoints,
    details: matchDetails,
    message: `严格分组匹配: ${matchedPoints}/${totalPoints} 个测点分组匹配成功`
  }
}

/**
 * 删除模板
 * @param {String} templateName - 模板名称
 */
export const deleteTemplate = (templateName) => {
  try {
    const templates = getStoredTemplates()
    if (templates[templateName]) {
      delete templates[templateName]
      localStorage.setItem('circuit_templates', JSON.stringify(templates))
      ElMessage.success(`模板 "${templateName}" 删除成功`)
      return true
    } else {
      ElMessage.warning(`模板 "${templateName}" 不存在`)
      return false
    }
  } catch (error) {
    console.error('删除模板时出错:', error)
    ElMessage.error('删除模板失败')
    return false
  }
}

/**
 * 获取模板详情
 * @param {String} templateName - 模板名称
 * @returns {Object} 模板详情
 */
export const getTemplateDetails = (templateName) => {
  const templates = getStoredTemplates()
  return templates[templateName] || null
}

/**
 * 显示匹配结果详情
 * @param {Object} matchResult - 匹配结果
 */
export const displayMatchResult = (matchResult) => {
  if (!matchResult) return

  console.log('📊 ========== 匹配结果详情 ==========')
  console.log(`总体得分: ${matchResult.score}%`)
  console.log(`匹配状态: ${matchResult.isMatch ? '完全匹配' : '部分匹配'}`)
  console.log(`匹配详情: ${matchResult.message}`)
  console.log('')

  matchResult.details.forEach((detail, index) => {
    console.log(`${index + 1}. ${detail.message}`)
    console.log(`   状态: ${getStatusText(detail.status)}`)

    if (detail.expected.length > 0) {
      console.log(`   要求器件: ${detail.expected.join('、')}`)
    }
    if (detail.actual.length > 0) {
      console.log(`   实际器件: ${detail.actual.join('、')}`)
    }

    // 显示匹配关系
    if (detail.status === 'correct') {
      console.log(`   匹配关系: 模板${detail.templateId} ↔ 学生${detail.studentId}`)
    } else if (detail.status === 'missing') {
      console.log(`   缺失: 模板${detail.templateId}测点在学生电路中无对应`)
    } else if (detail.status === 'extra') {
      console.log(`   多余: 学生${detail.studentId}测点在模板中无对应`)
    }
    console.log('')
  })

  console.log('========================================')
}

/**
 * 获取状态文本
 * @param {String} status - 状态
 * @returns {String} 状态文本
 */
const getStatusText = (status) => {
  const statusMap = {
    'correct': '✅ 正确',
    'incorrect': '❌ 错误',
    'missing': '⚠️ 缺失',
    'extra': '➕ 多余'
  }
  return statusMap[status] || status
}
