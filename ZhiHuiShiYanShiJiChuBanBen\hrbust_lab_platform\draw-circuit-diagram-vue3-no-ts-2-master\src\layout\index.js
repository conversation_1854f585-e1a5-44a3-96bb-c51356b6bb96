// 引入需要注册的全局组件
import ButtonToolBar from '@/layout/ButtonToolBar'
import CanvasArea from '@/layout/CanvasArea'
import ComponentsPanel from '@/layout/ComponentsPanel'
import Header from '@/layout/Header'

// 全局对象
const allGlobalLayout = {
  ButtonToolBar,
  CanvasArea,
  ComponentsPanel,
  Header,
}

// 对外暴露插件对象
export default {
  install(app) {
    // 注册项目当中全部的全局组件
    Object.keys(allGlobalLayout).forEach((key) => {
      // 将每个组件都注册为全局组件
      app.component(key, allGlobalLayout[key])
    })
  },
}
