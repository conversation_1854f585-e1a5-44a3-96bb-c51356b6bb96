<template>
  <div 
    class="test-point" 
    :style="{ 
      left: `${component.x}px`, 
      top: `${component.y}px`,
      transform: `translate(-50%, -50%)`
    }"
    @mousedown.stop="handleMouseDown"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    :class="{ 'selected': isSelected }"
    :data-id="component.id || component.componentId"
  >
    <div class="point-circle"></div>
    <!-- 隐藏测点标识符显示 -->
    <!-- <div v-if="component.label" class="point-label">{{ component.label }}</div> -->
    
    <!-- 刷新按钮 -->
    <button v-if="false" class="refresh-btn" @click.stop="refreshConnections">刷新</button>
    
    <!-- 测点周围组件信息 (鼠标悬停时显示) -->
    <div v-if="showSurroundingInfo" class="surrounding-info">
      <div class="info-title">==== 测点 ====</div>
      <div v-if="!connectedComponents || connectedComponents.length === 0" class="no-connections">
        无直接相连器件
      </div>
      <div v-else class="connected-components">
        <div v-for="(comp, index) in connectedComponents" :key="index" class="component-info">
          <div class="connection-line">
            <span class="connection-label">连接到</span>
            <span class="component-type">{{ comp.typeName || comp.type }}</span>
            <span class="component-id">({{ comp.identifier }})</span>
          </div>
          <div class="connection-separator" v-if="index < connectedComponents.length - 1"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, inject, ref, computed, reactive } from 'vue'
import { findSurroundingComponents, debugTestPointConnections } from '@/utils/canvasDataManager'
import { useComponentsInfoStore } from '@/store/componentsInfo'

const emit = defineEmits(['select', 'startDrag'])

const props = defineProps({
  component: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  x: Number,
  y: Number,
  id: {
    type: String,
    default: () => `test-point-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  },
  gridX: Number,
  gridY: Number,
  label: String
})

// 使用provide/inject模式或事件总线替代vuex
// 方式一：如果使用了provide/inject
const store = inject('store', null)

// 方式二：如果使用了emitter (mitt或tiny-emitter)
const emitter = inject('emitter', null)

// 状态管理 - 是否显示周围组件信息
const showSurroundingInfo = ref(false) // 默认不显示，只在鼠标悬停时显示

// 获取周围器件信息
const connectedComponents = computed(() => {
  console.log('测点组件数据:', props.component);
  if (props.component && props.component.surroundingComponents) {
    console.log('测点周围组件数据:', props.component.surroundingComponents);
    return props.component.surroundingComponents;
  }
  return [];
})

// 鼠标事件处理
const handleMouseEnter = () => {
  console.log('鼠标悬停在测点上，显示连接器件信息');
  
  // 无论是否有连接器件数据，都尝试刷新
  refreshConnections();
  
  // 显示信息
  showSurroundingInfo.value = true;
}

const handleMouseLeave = () => {
  console.log('鼠标离开测点，隐藏连接器件信息');
  showSurroundingInfo.value = false;
}

onMounted(() => {
  // 根据项目实际状态管理方案存储测试点数据
  if (store) {
    // 使用store
    store.addTestPoint({
      id: props.id,
      x: props.x,
      y: props.y
    })
  } else if (emitter) {
    // 使用事件总线
    emitter.emit('test-point-add', {
      id: props.id,
      x: props.x,
      y: props.y
    })
  } else {
    // 使用sessionStorage直接存储
    const testPoints = JSON.parse(sessionStorage.getItem('circuit_test_points') || '[]')
    testPoints.push({
      id: props.id,
      x: props.x,
      y: props.y
    })
    sessionStorage.setItem('circuit_test_points', JSON.stringify(testPoints))
  }
  
  // 添加鼠标事件监听
  const pointElement = document.querySelector(`.test-point[data-id="${props.id}"]`) || 
                       document.getElementById(props.id);
  if (pointElement) {
    pointElement.addEventListener('mouseenter', handleMouseEnter)
    pointElement.addEventListener('mouseleave', handleMouseLeave)
  }
  
  // 组件挂载时尝试获取连接器件信息
  setTimeout(() => {
    if (!props.component.surroundingComponents || props.component.surroundingComponents.length === 0) {
      try {
        const componentsStore = useComponentsInfoStore();
        if (componentsStore && componentsStore.components) {
          const surroundingComps = findSurroundingComponents(props.component, componentsStore.components);
          if (surroundingComps && surroundingComps.length > 0) {
            props.component.surroundingComponents = surroundingComps;
            console.log('组件挂载时获取到连接器件:', surroundingComps);
          }
        }
      } catch (error) {
        console.error('组件挂载时获取连接器件失败:', error);
      }
    }
  }, 500); // 延迟500ms执行，确保其他组件已加载
})

// 如果组件支持拖动，更新位置
function updatePosition() {
  // 获取电路图容器
  const circuitContainer = document.querySelector('.circuit-container') || document.getElementById('circuitContainer');
  if (!circuitContainer) {
    console.error('找不到电路图容器');
    return;
  }
  
  // 获取容器的位置信息
  const containerRect = circuitContainer.getBoundingClientRect();
  
  // 获取测试点元素
  const element = document.getElementById(props.id);
  if (!element) {
    console.error(`找不到ID为${props.id}的测试点元素`);
    return;
  }
  
  // 获取元素位置
  const rect = element.getBoundingClientRect();
  
  // 计算相对于容器的坐标（重要！）
  const relativeX = rect.left - containerRect.left + rect.width/2;
  const relativeY = rect.top - containerRect.top + rect.height/2;
  
  console.log(`测试点${props.id}位置更新: 相对坐标(${relativeX}, ${relativeY})`);
  
  // 更新到状态中
  const testPoint = {
    id: props.id,
    x: relativeX,
    y: relativeY
  };
  
  // 使用事件发射器更新测试点数据
  emitter.emit('update-test-point', testPoint);
  
  // 同时直接更新到sessionStorage作为备份
  try {
    const testPointsJSON = sessionStorage.getItem('circuit_test_points') || '[]';
    const testPoints = JSON.parse(testPointsJSON);
    
    // 查找是否已存在该测试点
    const index = testPoints.findIndex(p => p.id === props.id);
    if (index >= 0) {
      testPoints[index] = testPoint;
    } else {
      testPoints.push(testPoint);
    }
    
    // 保存回sessionStorage
    sessionStorage.setItem('circuit_test_points', JSON.stringify(testPoints));
    console.log(`测试点${props.id}已保存到sessionStorage`);
  } catch (e) {
    console.error('保存测试点数据失败:', e);
  }
}

// 组件销毁时移除测试点
onUnmounted(() => {
  // 移除鼠标事件监听
  const pointElement = document.querySelector(`.test-point[data-id="${props.id}"]`) || 
                       document.getElementById(props.id);
  if (pointElement) {
    pointElement.removeEventListener('mouseenter', handleMouseEnter)
    pointElement.removeEventListener('mouseleave', handleMouseLeave)
  }

  if (store) {
    store.removeTestPoint(props.id)
  } else if (emitter) {
    emitter.emit('test-point-remove', props.id)
  } else {
    // 使用sessionStorage移除
    const testPoints = JSON.parse(sessionStorage.getItem('circuit_test_points') || '[]')
    const filteredPoints = testPoints.filter(p => p.id !== props.id)
    sessionStorage.setItem('circuit_test_points', JSON.stringify(filteredPoints))
  }
})

const handleMouseDown = (event) => {
  emit('select', props.id)
  emit('startDrag', event, props.id)
}

// 将网格坐标转换为像素坐标用于显示
const gridSize = 20 // 确保与其他地方使用相同的网格大小
const style = reactive({
  left: `${props.gridX * gridSize}px`,
  top: `${props.gridY * gridSize}px`
})

// 导出测试点数据时，确保使用网格坐标
const getPointData = () => {
  return {
    id: props.id,
    gridX: props.gridX,
    gridY: props.gridY,
    label: props.label
  }
}

// 手动刷新连接关系
const refreshConnections = () => {
  try {
    console.log('开始刷新测点连接关系...');

    // 获取所有组件
    const componentsStore = useComponentsInfoStore();
    const allComponents = componentsStore.components;

    console.log('获取到的所有组件:', allComponents);

    // 确保组件数据存在
    if (!props.component) {
      console.error('测点组件数据不存在');
      return;
    }

    console.log('当前测点组件:', props.component);

    // 先运行调试分析
    const debugInfo = debugTestPointConnections(props.component, allComponents);
    console.log('调试分析结果:', debugInfo);

    // 重新计算周围组件
    const surroundingComps = findSurroundingComponents(props.component, allComponents);

    console.log('刷新测点连接关系结果:', surroundingComps);

    // 更新组件
    props.component.surroundingComponents = surroundingComps;

    // 显示连接信息
    showSurroundingInfo.value = true;

    // 如果找到了连接组件，延长显示时间
    const displayTime = surroundingComps.length > 0 ? 8000 : 3000;

    // 自动隐藏
    setTimeout(() => {
      showSurroundingInfo.value = false;
    }, displayTime);

    // 输出调试信息
    if (surroundingComps.length === 0) {
      console.warn('未找到与测点连接的组件，请检查：');
      console.warn('1. 测点是否正确连接到其他组件');
      console.warn('2. 连接的线路是否完整');
      console.warn('3. 组件是否有正确的标识符');
    } else {
      console.log(`成功找到 ${surroundingComps.length} 个连接组件`);
      surroundingComps.forEach((comp, index) => {
        console.log(`连接组件 ${index + 1}:`, {
          类型: comp.typeName,
          标识: comp.identifier,
          标签: comp.label
        });
      });
    }
  } catch (error) {
    console.error('刷新连接关系出错:', error);
    console.error('错误详情:', error.stack);
  }
}

defineExpose({
  updatePosition,
  getPointData
})
</script>

<style scoped>
.test-point {
  position: absolute;
  z-index: 10;
  cursor: move;
}

.point-circle {
  width: 14px;
  height: 14px;
  background: #ff0000;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(0,0,0,0.3);
  transition: all 0.3s;
}

.test-point.selected .point-circle {
  background: #00ff00;
  box-shadow: 0 0 8px rgba(0,255,0,0.5);
}

.point-circle:hover {
  transform: scale(1.2);
}

.point-label {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  white-space: nowrap;
  color: #333;
}

/* 周围组件信息弹窗样式 */
.surrounding-info {
  position: absolute;
  top: -10px;
  left: 20px;
  background-color: white;
  color: #333;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  min-width: 200px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
  pointer-events: none; /* 确保鼠标事件穿透弹窗 */
}

.info-title {
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  color: #333;
  font-size: 14px;
}

.no-connections {
  color: #999;
  font-style: italic;
  padding: 5px 0;
  font-size: 14px;
  text-align: center;
}

.connected-components {
  max-height: 150px;
  overflow-y: auto;
}

.component-info {
  padding: 3px 0;
}

.connection-line {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 3px 0;
}

.connection-label {
  color: #666;
  white-space: nowrap;
}

.component-type {
  color: #333;
  font-weight: normal;
}

.component-id {
  color: #333;
}

.connection-separator {
  height: 1px;
  background-color: #eee;
  margin: 5px 0;
  width: 100%;
}

.connection-point {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.refresh-btn {
  position: absolute;
  top: -20px;
  right: 0;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.refresh-btn:hover {
  background-color: #0056b3;
}
</style> 