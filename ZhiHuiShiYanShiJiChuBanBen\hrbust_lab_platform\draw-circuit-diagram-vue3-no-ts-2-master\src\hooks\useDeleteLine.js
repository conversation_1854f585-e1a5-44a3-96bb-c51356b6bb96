import { useLineModeStore } from '@/store/lineMode.js'
import { useLineStateStore } from '@/store/lineState.js'
import { useVerifyStore } from '@/store/verify'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'

/**
 * 用于【添加组件到画布】
 */
export default function () {
  const lineStateStore = useLineStateStore() // 引入全局的连线状态 Store
  const { selectedLine, fixedLines, intersections } = storeToRefs(lineStateStore)

  const lineModeStore = useLineModeStore() // 引入全局的连线模式 Store
  const { lineMode } = storeToRefs(lineModeStore)

  const verifyStore = useVerifyStore()
  const { generateGridPoints } = verifyStore

  /**
   * 鼠标悬停时设置高亮索引
   * @param {number} index - 连线的索引
   */
  const handleMouseOver = (index) => {
    lineStateStore.setHoveredLine(index)
  }

  /**
   * 鼠标移出时清除高亮索引
   */
  const handleMouseOut = () => {
    lineStateStore.clearHoveredLine()
  }

  /**
   * 点击连线时选中
   * @param {number} index - 连线的索引
   */
  const handleLineClick = (index) => {
    lineStateStore.setSelectedLine(index)
  }

  /**
   * 删除选中的连线
   */
  const deleteSelectedLine = () => {
    if (lineMode.value !== 'disabled') {
      console.warn('必须在关闭连线模式下删除连线！')
      return
    }
    if (selectedLine.value !== null) {
      removeLine(selectedLine.value)
      lineStateStore.clearSelectedLine() // 清空选中状态
    }
  }

  /**
   * 删除指定索引的连线
   * @param {number} index - 要删除的连线索引
   */
  const removeLine = (index) => {
    // 🔧 修复：安全访问 fixedLines.value，防止 undefined 错误
    const linesArray = fixedLines?.value
    if (linesArray && index >= 0 && index < linesArray.length) {
      linesArray.splice(index, 1)
      // 更新交点信息
      updateIntersectionValidity()
    } else {
      console.warn('⚠️ 无法删除连线：索引无效或连线数组未初始化')
    }
  }

  /**
   * 更新交点有效性，检查交点是否属于至少两条固定连线的交点
   */
  const updateIntersectionValidity = () => {
    // 🔧 修复：安全访问响应式引用，防止 undefined 错误
    const intersectionsList = Array.isArray(intersections?.value) ? intersections.value : []
    const fixedLinesArray = Array.isArray(fixedLines?.value) ? fixedLines.value : []
    const fixedLinesCount = fixedLinesArray.length

    if (intersectionsList.length === 0 || fixedLinesCount < 2) {
      // 如果没有交点或者固定线少于两条，则清空交点列表
      if (intersections?.value) {
        intersections.value = []
      }
      return
    }

    // 将每条固定线的网格点集合生成并存储
    const lineGridPoints = fixedLinesArray.map((line) => generateGridPoints(line))

    // 检查交点是否属于至少两条固定线的交点
    const validIntersections = intersectionsList.filter((intersection) => {
      let lineCount = 0
      // 遍历每条固定线，检查交点是否属于该线的网格点
      for (let i = 0; i < fixedLinesArray.length; i++) {
        if (lineGridPoints[i].some((point) => point.x === intersection.x && point.y === intersection.y)) {
          lineCount++
        }
      }
      return lineCount >= 2 // 交点必须出现在至少两条线的网格点集合中
    })

    // 更新交点有效性状态
    if (intersections?.value) {
      intersections.value = Array.isArray(validIntersections) ? validIntersections : []
    }
    console.log('@@@ ', intersections?.value)
    console.log('@@@ ', validIntersections)
  }

  return {
    handleMouseOver,
    handleMouseOut,
    handleLineClick,
    deleteSelectedLine,
  }
}
