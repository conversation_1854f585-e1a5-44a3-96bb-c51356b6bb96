<template>
  <el-dialog
    v-model="editTextBoxDrawerVisible"
    title="编辑"
    width="20%"
    :draggable="true"
    top="33vh"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-form center @submit.prevent>
      <el-form-item>
        <el-input
          v-model="selectedTextBox.content"
          style="margin-left: 5px; margin-right: 30px"
          @keyup.enter="editTextBoxDrawerVisible = false" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="editTextBoxDrawerVisible = false">Confirm</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="EditTextBox">
  import { useTextBoxStore } from '@/store/textBox.js'
  import { useButtonBarInfoStore } from '@/store/buttonBarInfo.js'
  import { storeToRefs } from 'pinia'

  const buttonBarInfoStore = useButtonBarInfoStore()
  const { editTextBoxDrawerVisible } = storeToRefs(buttonBarInfoStore)

  const textBoxStore = useTextBoxStore()
  const { selectedTextBox } = storeToRefs(textBoxStore)
</script>

<style scoped></style>
