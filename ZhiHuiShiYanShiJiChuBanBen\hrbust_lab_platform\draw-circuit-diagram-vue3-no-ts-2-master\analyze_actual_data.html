<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实际数据拓扑分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .result {
            padding: 15px;
            border-radius: 3px;
            margin: 10px 0;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>晶体管共射极单管放大器 - 拓扑结构分析</h1>
        
        <div class="section">
            <div class="title">原始连接关系数据</div>
            <div class="data" id="original-data"></div>
        </div>

        <div class="section">
            <div class="title">组件类型提取过程</div>
            <button onclick="analyzeComponentTypes()">分析组件类型</button>
            <div id="component-analysis"></div>
        </div>

        <div class="section">
            <div class="title">拓扑结构转换</div>
            <button onclick="buildTopologyStructure()">构建拓扑结构</button>
            <div id="topology-result"></div>
        </div>

        <div class="section">
            <div class="title">对比分析：传统方式 vs 拓扑方式</div>
            <button onclick="compareValidationMethods()">对比校验方式</button>
            <div id="comparison-result"></div>
        </div>

        <div class="section">
            <div class="title">测点连接分析</div>
            <button onclick="analyzeTestPointConnections()">分析测点连接</button>
            <div id="testpoint-analysis"></div>
        </div>
    </div>

    <script>
        // 实际的连接关系数据
        const actualConnections = [
            {
                "from": "极性电容 - C1 - 10 μF - 连接点1 - P",
                "to": "无极性电容 - C1 - 10 μF - 连接点2 - null"
            },
            {
                "from": "极性电容 - C1 - 10 μF - 连接点1 - P",
                "to": "测点 - Jk53VZ - - 连接点 - null"
            },
            {
                "from": "极性电容 - C1 - 10 μF - 连接点2 - N",
                "to": "二极管 - D1 -   - 连接点2 - N"
            },
            {
                "from": "无极性电容 - C1 - 10 μF - 连接点1 - null",
                "to": "电阻 - R1 - 1 KΩ - 连接点1 - null"
            },
            {
                "from": "无极性电容 - C1 - 10 μF - 连接点2 - null",
                "to": "测点 - Jk53VZ - - 连接点 - null"
            },
            {
                "from": "电阻 - R1 - 1 KΩ - 连接点2 - null",
                "to": "示波器 - 示波器 1 -   - 连接点1 - N"
            },
            {
                "from": "电阻 - R1 - 1 KΩ - 连接点2 - null",
                "to": "测点 - HsETk9 - - 连接点 - null"
            },
            {
                "from": "二极管 - D1 -   - 连接点1 - P",
                "to": "示波器 - 示波器 1 -   - 连接点2 - P"
            },
            {
                "from": "二极管 - D1 -   - 连接点1 - P",
                "to": "测点 - FxSnj1 - - 连接点 - null"
            },
            {
                "from": "示波器 - 示波器 1 -   - 连接点1 - N",
                "to": "测点 - HsETk9 - - 连接点 - null"
            },
            {
                "from": "示波器 - 示波器 1 -   - 连接点2 - P",
                "to": "测点 - FxSnj1 - - 连接点 - null"
            }
        ];

        // 显示原始数据
        document.getElementById('original-data').textContent = JSON.stringify(actualConnections, null, 2);

        // 组件类型规范化函数（修正版）
        function normalizeComponentType(type) {
            if (!type) return type

            // 保持电容的极性区分
            if (type.includes('极性电容')) return '极性电容'
            if (type.includes('无极性电容')) return '无极性电容'

            if (type.includes('电阻')) {
                if (type.includes('可变')) return '可变电阻'
                return '电阻'
            }
            if (type.includes('示波器')) return '示波器'
            if (type.includes('测点')) return '测点'
            if (type.includes('二极管')) return '二极管'

            return type
        }

        // 提取组件类型
        function extractComponentType(connectionDesc) {
            if (!connectionDesc) return ''
            const parts = connectionDesc.split(' - ')
            if (parts.length > 0) {
                return normalizeComponentType(parts[0].trim())
            }
            return ''
        }

        // 分析组件类型
        function analyzeComponentTypes() {
            let analysis = "组件类型提取过程：\n\n";
            
            actualConnections.forEach((conn, index) => {
                const fromOriginal = conn.from.split(' - ')[0];
                const toOriginal = conn.to.split(' - ')[0];
                const fromNormalized = extractComponentType(conn.from);
                const toNormalized = extractComponentType(conn.to);
                
                analysis += `连接 ${index + 1}:\n`;
                analysis += `  原始: "${fromOriginal}" → "${toOriginal}"\n`;
                analysis += `  规范化: "${fromNormalized}" → "${toNormalized}"\n\n`;
            });

            // 统计所有组件类型
            const allTypes = new Set();
            actualConnections.forEach(conn => {
                allTypes.add(extractComponentType(conn.from));
                allTypes.add(extractComponentType(conn.to));
            });

            analysis += `\n发现的组件类型: ${Array.from(allTypes).join(', ')}\n`;
            analysis += `总共 ${allTypes.size} 种不同的组件类型`;

            document.getElementById('component-analysis').innerHTML = `
                <div class="result">
                    <div class="data">${analysis}</div>
                </div>
            `;
        }

        // 构建拓扑结构
        function buildTopologyStructure() {
            const topology = {};
            
            // 构建拓扑图
            actualConnections.forEach(conn => {
                const fromType = extractComponentType(conn.from);
                const toType = extractComponentType(conn.to);
                
                if (!topology[fromType]) topology[fromType] = new Set();
                if (!topology[toType]) topology[toType] = new Set();
                
                topology[fromType].add(toType);
                topology[toType].add(fromType);
            });

            // 转换为数组并排序
            Object.keys(topology).forEach(key => {
                topology[key] = Array.from(topology[key]).sort();
            });

            let result = "拓扑结构构建结果：\n\n";
            result += JSON.stringify(topology, null, 2);
            
            result += "\n\n拓扑关系解读：\n";
            Object.keys(topology).sort().forEach(componentType => {
                const connections = topology[componentType];
                result += `• ${componentType} 连接到: ${connections.join(', ')}\n`;
            });

            document.getElementById('topology-result').innerHTML = `
                <div class="result">
                    <div class="data">${result}</div>
                </div>
            `;
        }

        // 对比校验方式
        function compareValidationMethods() {
            // 模拟另一个电路的连接关系（ID不同但拓扑相同）
            const anotherCircuit = [
                {
                    "from": "极性电容 - C2 - 10 μF - 连接点1 - P",
                    "to": "无极性电容 - C2 - 10 μF - 连接点2 - null"
                },
                {
                    "from": "极性电容 - C2 - 10 μF - 连接点1 - P",
                    "to": "测点 - ABC123 - - 连接点 - null"
                },
                {
                    "from": "极性电容 - C2 - 10 μF - 连接点2 - N",
                    "to": "二极管 - D2 -   - 连接点2 - N"
                },
                {
                    "from": "无极性电容 - C2 - 10 μF - 连接点1 - null",
                    "to": "电阻 - R2 - 1 KΩ - 连接点1 - null"
                },
                {
                    "from": "无极性电容 - C2 - 10 μF - 连接点2 - null",
                    "to": "测点 - ABC123 - - 连接点 - null"
                },
                {
                    "from": "电阻 - R2 - 1 KΩ - 连接点2 - null",
                    "to": "示波器 - 示波器 2 -   - 连接点1 - N"
                },
                {
                    "from": "电阻 - R2 - 1 KΩ - 连接点2 - null",
                    "to": "测点 - DEF456 - - 连接点 - null"
                },
                {
                    "from": "二极管 - D2 -   - 连接点1 - P",
                    "to": "示波器 - 示波器 2 -   - 连接点2 - P"
                },
                {
                    "from": "二极管 - D2 -   - 连接点1 - P",
                    "to": "测点 - GHI789 - - 连接点 - null"
                },
                {
                    "from": "示波器 - 示波器 2 -   - 连接点1 - N",
                    "to": "测点 - DEF456 - - 连接点 - null"
                },
                {
                    "from": "示波器 - 示波器 2 -   - 连接点2 - P",
                    "to": "测点 - GHI789 - - 连接点 - null"
                }
            ];

            // 传统方式比较（基于完整连接描述）
            const originalSet1 = new Set(actualConnections.map(conn => 
                JSON.stringify([conn.from, conn.to].sort())
            ));
            const originalSet2 = new Set(anotherCircuit.map(conn => 
                JSON.stringify([conn.from, conn.to].sort())
            ));
            const traditionalMatch = originalSet1.size === originalSet2.size && 
                [...originalSet1].every(conn => originalSet2.has(conn));

            // 拓扑方式比较（基于组件类型）
            const topologySet1 = new Set(actualConnections.map(conn => {
                const fromType = extractComponentType(conn.from);
                const toType = extractComponentType(conn.to);
                return JSON.stringify([fromType, toType].sort());
            }));
            const topologySet2 = new Set(anotherCircuit.map(conn => {
                const fromType = extractComponentType(conn.from);
                const toType = extractComponentType(conn.to);
                return JSON.stringify([fromType, toType].sort());
            }));
            const topologyMatch = topologySet1.size === topologySet2.size && 
                [...topologySet1].every(conn => topologySet2.has(conn));

            let comparison = `对比分析结果：\n\n`;
            comparison += `传统校验方式（基于完整连接描述）：\n`;
            comparison += `结果: ${traditionalMatch ? '✅ 匹配' : '❌ 不匹配'}\n`;
            comparison += `原因: ${traditionalMatch ? '连接描述完全相同' : '组件ID不同导致连接描述不匹配'}\n\n`;
            
            comparison += `拓扑校验方式（基于组件类型）：\n`;
            comparison += `结果: ${topologyMatch ? '✅ 匹配' : '❌ 不匹配'}\n`;
            comparison += `原因: ${topologyMatch ? '拓扑结构相同，忽略ID差异' : '拓扑结构不同'}\n\n`;

            comparison += `示例差异：\n`;
            comparison += `原始: "测点 - Jk53VZ - - 连接点 - null"\n`;
            comparison += `对比: "测点 - ABC123 - - 连接点 - null"\n`;
            comparison += `传统方式: 认为不同（ID不匹配）\n`;
            comparison += `拓扑方式: 认为相同（都是测点类型）\n`;

            document.getElementById('comparison-result').innerHTML = `
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>传统校验方式</h4>
                        <div class="data">基于完整连接描述\n结果: ${traditionalMatch ? '✅ 匹配' : '❌ 不匹配'}</div>
                    </div>
                    <div class="comparison-item">
                        <h4>拓扑校验方式</h4>
                        <div class="data">基于组件类型\n结果: ${topologyMatch ? '✅ 匹配' : '❌ 不匹配'}</div>
                    </div>
                </div>
                <div class="result">
                    <div class="data">${comparison}</div>
                </div>
            `;
        }

        // 分析测点连接
        function analyzeTestPointConnections() {
            const testPointConnections = {};
            
            actualConnections.forEach(conn => {
                const fromType = extractComponentType(conn.from);
                const toType = extractComponentType(conn.to);
                const fromId = conn.from.split(' - ')[1];
                const toId = conn.to.split(' - ')[1];
                
                if (fromType === '测点') {
                    if (!testPointConnections[fromId]) {
                        testPointConnections[fromId] = new Set();
                    }
                    testPointConnections[fromId].add(toType);
                }
                
                if (toType === '测点') {
                    if (!testPointConnections[toId]) {
                        testPointConnections[toId] = new Set();
                    }
                    testPointConnections[toId].add(fromType);
                }
            });

            let analysis = "测点连接分析：\n\n";
            Object.keys(testPointConnections).forEach(testPointId => {
                const connectedTypes = Array.from(testPointConnections[testPointId]);
                analysis += `测点 ${testPointId}:\n`;
                analysis += `  连接的组件类型: ${connectedTypes.join(', ')}\n`;
                analysis += `  连接数量: ${connectedTypes.length}\n\n`;
            });

            analysis += `总结:\n`;
            analysis += `• 发现 ${Object.keys(testPointConnections).length} 个测点\n`;
            analysis += `• 测点ID: ${Object.keys(testPointConnections).join(', ')}\n`;
            analysis += `• 在拓扑校验中，这些不同的ID会被统一为"测点"类型\n`;

            document.getElementById('testpoint-analysis').innerHTML = `
                <div class="result">
                    <div class="data">${analysis}</div>
                </div>
            `;
        }
    </script>
</body>
</html>
