import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useComponentInfoStore = defineStore('componentInfo', () => {
  const DEFAULT_COMPONENT_WIDTH = ref(40) // 组件默认长度
  const DEFAULT_COMPONENT_HEIGHT = ref(20) // 组件默认宽度
  const DEFAULT_SCALE = ref(2.0) // 组件默认缩放比例

  // 获取组件的宽度和高度
  const getComponentWH = (component) => {
    const componentWidth =
      DEFAULT_COMPONENT_WIDTH.value * component.scale || DEFAULT_COMPONENT_WIDTH.value * DEFAULT_SCALE.value // 获取不到就使用默认的宽度
    const componentHeight =
      DEFAULT_COMPONENT_HEIGHT.value * component.scale || DEFAULT_COMPONENT_HEIGHT.value * DEFAULT_SCALE.value // 获取不到就使用默认的长度

    return { componentWidth, componentHeight }
  }

  return { DEFAULT_COMPONENT_WIDTH, DEFAULT_COMPONENT_HEIGHT, DEFAULT_SCALE, getComponentWH }
})
