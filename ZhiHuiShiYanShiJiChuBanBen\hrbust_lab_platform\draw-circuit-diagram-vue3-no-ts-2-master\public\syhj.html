<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电路实验环境</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
   <script src="../js/jquery-3.3.1.js"></script>
   <script src="../js/layer/layer.js"></script>
   <link href="../css/bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
   <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        /* 主容器布局 */
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
        }
        
        /* 左侧区域 - 30% */
        .left-container {
            width: 35%;
            height: 100%;
            border-right: 1px solid #ddd;
            /*overflow: auto;
            display: flex;*/
            background: #f5f5f5;
            
            justify-content: center;
            align-items: center;
        }
        
        /* 右侧实验区域 - 70% */
        .right-container {
            flex: 1;
            position: relative;
            background: #ffffff;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 示波器样式 */
        .oscilloscope {
            position: fixed;
            top: 40px;
            right: 10px;
            width: 180px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .xhyDiv {
            position: fixed;
            top: 180px;
            right: 10px;
            width: 180px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .oscilloscope-header {
            padding: 10px 15px;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }
        
        .oscilloscope-title {
            font-weight: bold;
            font-size: 14px;
        }
        
        .oscilloscope-content {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .oscilloscope-channel {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .channel-title {
            font-size: 13px;
            color: #666;
            width: 50px;
        }
        
        .channel-value {
            font-size: 13px;
            color: #999;
            flex: 1;
        }
        
        /* 探头样式 */
        .probe {
            width: 20px;
            height: 20px;
            border: 2px solid;
            border-radius: 50%;
            cursor: pointer;
            position: relative;
        }
        
        .probe-1 {
            border-color: #ff5722;
        }
        
        .probe-2 {
            border-color: #2196f3;
        }
        
        /* 探头连线样式 */
        .probe-line {
            position: fixed;
            height: 2px;
            border-top: 2px dashed;
            pointer-events: none;
            z-index: 90;
            transform-origin: left center;
        }
        
        .probe-line-1 {
            border-color: #ff5722;
        }
        
        .probe-line-2 {
            border-color: #2196f3;
        }
        
        /* 测试点样式 */
        .test-point {
            position: absolute;
            width: 15px;
            height: 15px;
            background-color: #E3E3E3;
            border-radius: 50%;
            border: 2px solid white;
            margin-left: -7.5px;
            margin-top: -7.5px;
            z-index: 150;
            cursor: pointer;
            transition: box-shadow 0.2s, background-color 0.2s;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }
        
        .test-point-label {
            position: absolute;
            left: 12px;
            top: -5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 151;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            display: none; /* 初始隐藏 */
        }
        
        .test-point:hover .test-point-label,
        .test-point.connected .test-point-label {
            display: block; /* 悬停或连接时显示 */
        }
        
        .test-point:hover {
            box-shadow: 0 0 8px rgba(255, 0, 0, 0.8);
            background-color: #ff3333;
        }
        
        .test-point.connected {
            background-color: #4CAF50;
        }
        
        .test-point.highlight {
            box-shadow: 0 0 8px rgba(255, 87, 34, 0.8);
            background-color: #ff9800;
        }
        
        .test-point.transparent {
            width: 20px;
            height: 20px;
            background-color: rgba(255, 0, 0, 0.8);  /* 改为更明显的红色 */
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 0 8px rgba(255, 0, 0, 0.7); /* 增加红色阴影 */
            transform: translate(-50%, -50%);
            z-index: 150;
            opacity: 0.9; /* 提高不透明度 */
        }
        
        .test-point.transparent.highlight {
            background-color: rgba(255, 206, 0, 0.7);
            border: 3px solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 12px rgba(255, 87, 34, 0.8);
            transform: translate(-50%, -50%) scale(1.2);
        }
        
        .test-point.transparent.connected {
            background-color: rgba(76, 175, 80, 0.7);
            border: 3px solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 8px rgba(0, 128, 0, 0.5);
        }
        
        /* 旋钮样式 */
        .knob-container {
        	display: flex;
            flex-direction: column;
           margin-top: 6px;
           display: inline-flex; 
            align-items: center;
            background: rgba(240, 240, 240, 0.9);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            z-index: 50;
        }
        
        .knob {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #666, #333);
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            border: 2px solid #555;
            --rotation: 0deg;
        }
        
        .knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 45%;
            background-color: #5DADE2;
            transform: translate(-50%, -100%) rotate(var(--rotation));
            transform-origin: bottom;
            border-radius: 2px;
            box-shadow: 0 0 5px rgba(93, 173, 226, 0.8);
        }
        
        .knob-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-top: 2px;
            text-align: center;
        }
        
        .knob-value {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-top: 4px;
        } 
        
        /* 远程桌面按钮样式 */
        .remote-desktop-button-container {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            padding: 0 15px;
        }
        
        .remote-desktop-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: auto;
            min-width: 120px;
            height: 28px;
            font-weight: normal;
        }
        
        .remote-desktop-button:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.25);
        }
        
        .remote-desktop-button:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .remote-icon {
            font-size: 14px;
        }
        
        /* 左右区域的提示文本 */
        .content-placeholder {
            color: #aaa;
            font-size: 18px;
            text-align: center;
            padding: 20px;
        }
        
        /* 图片容器样式 */
       .image-container{
       		position: relative;
            background: #ebebeb;
            display: grid;
  			place-items: center; /* 同时水平和垂直居中 */
       }
        #local-image {
            max-width: 100%;
                text-align:center;

            /*object-fit: contain;*/
            object-fit: contain;
            display: none; 
            border: none; 
            box-shadow: none; 
            background-color: transparent; 
        }
        #iframe-container {
            width: 100%;
            margin-top: 2px;
            justify-content: center;
            align-items: center;
            position: relative;
            background: #ffffff;
        }
  
        
        .no-image-message {
            color: #aaa;
            font-size: 18px;
            padding: 20px;
            text-align: center;
            display: none; /* 初始隐藏，如果没有图片则显示 */
        }
        
        /* 测试点图层样式 */
        .test-points-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 150;
            pointer-events: none; /* 允许点击穿透到底层图像 */
        }
        
        /* 让测试点可以接收点击事件 */
        .test-point {
            pointer-events: auto;
        }
        
        /* 标记测试点按钮 */
        .mark-point-btn {
            position: fixed;
            right: 20px;
            bottom: 20px;
            padding: 8px 16px;
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        
        .mark-point-btn:hover {
            background: #e8e8e8;
        }
        
        .mark-point-btn.active {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        
        /* Material Icons 样式 */
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 18px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
        }
        
        /* 测试点编辑弹窗 */
        .point-edit-dialog {
            position: fixed;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            min-width: 200px;
            display: none;
        }
        
        .point-edit-dialog input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .point-edit-dialog .buttons {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        
        .point-edit-dialog button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .point-edit-dialog .save-btn {
            background: #4CAF50;
            color: white;
        }
        
        .point-edit-dialog .cancel-btn {
            background: #f5f5f5;
            color: #333;
        }
        
        /* 旋钮控制样式 */
        .knob {
            cursor: pointer !important;
            z-index: 200 !important;
            position: relative;
        }
        .knob.dragging {
            cursor: grabbing !important;
        }
        .knob::after {
            pointer-events: none;
        }
        
        .btn{
        	margin: 2px;
        	padding: 4px;
        	font-size: 20px;
        	border: none;
        	border-radius: 4px;
        }
        .btn:active{
        	margin-top: 14px;
        	padding: 4px;
        	
        	border: none;
        	border-radius: 4px;
        } 
        .open{
        	background-color: red;
        }
	    .redpoint{
	    	background-color: red;
	    }
       .bottombtn button{
       	 margin-top: 14px;
       }

       /* 仪表组件样式 - 完全模仿示波器 */
       .meter-component {
           position: fixed;
           width: 180px;
           background: white;
           border-radius: 8px;
           box-shadow: 0 2px 10px rgba(0,0,0,0.2);
           z-index: 1000;
       }

       .ammeter-component {
           top: 40px;
           right: 200px;
       }

       .voltmeter-component {
           top: 250px;
           right: 200px;
       }

       .meter-header {
           padding: 10px 15px;
           background: #f5f5f5;
           border-radius: 8px 8px 0 0;
           border-bottom: 1px solid #ddd;
           display: flex;
           justify-content: space-between;
           align-items: center;
           cursor: move;
       }

       .meter-title {
           font-weight: bold;
           font-size: 14px;
       }

       .meter-content {
           padding: 15px;
           display: flex;
           flex-direction: column;
           gap: 12px;
       }

       .meter-channel {
           display: flex;
           align-items: center;
           gap: 10px;
       }

       .meter-value {
           font-size: 18px;
           font-weight: bold;
           color: #333;
           min-width: 60px;
       }

       .meter-unit {
           font-size: 13px;
           color: #666;
       }

       .meter-info {
           font-size: 12px;
           color: #999;
       }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧容器 -->
       <div id="leftpar" style="display: inline-block;  float: left; ">
       	<h4 style="text-align: center;" id="tat">实验指导书</h4>
        <iframe style="width: 100%;height: 800px;  border: medium none; display: block;" src="" id="neirongframe"></iframe>
        <iframe style="width: 100%; height: 100%;  border: medium none; display: none;" src="/hrbust_lab_platform/draw-circuit-diagram-dist/editable.html" id="editable"></iframe>
       </div>
	
        <!-- 右侧容器 -->
        <div id="rightpar" style="  margin-left: 10px ; display: inline-block; border: solid 2px;  float: left; background: white;">
        	<div style="height: 30px;  font-weight: bold; color: red;font-size: 20px;" id="shijian">
				<span style="float: right;" class="countdownTime" id="timeshow">
					<span style="float: right;" class="countdownTime" id="timeshow"><span>剩余时间:&nbsp;</span><span>00</span>:<span>44</span>:<span>22</span></span>
				</span>
			    <span style="float: left; font-size: 15px; margin-top:5px;  color:gray;"   >项目名称:<span id="projectName">测试</span></span>
			</div>
            <div class="image-container" id="image-container" >
                <img id="local-image" alt="电路图" class="circuit-image" style="margin-left: -15px;">
                <div id="no-image-message" class="no-image-message">未找到电路图</div>
                <!-- 添加测试点图层 -->
                <div id="test-points-layer" class="test-points-layer"></div>
            </div>
            
            
         <div  id="iframe-container" style="background-color: #000000;display: none;">
            	<iframe id="yczm"   style="width: 100%;height: 100%;  border: medium none; display: block;" allow="keyboard *"></iframe>
            </div>
            
            
      
            
            <div class="bottombtn" style="margin: 0 auto;">
            	<div id="knoblist" style="position: absolute;right: 75px;">
	            	
	            	<div class="knob-container" style="margin:0 10px;">
		                <div class="knob" identifier="R1" befR="0" curR="0"></div>
		                <div id="knob-value" class="knob-value">R1</div>
		            </div>
		            
		            <!--<div class="knob-container" style="margin-left: 10px;">
		                <div class="knob" identifier="R1" befR="0" curR="0"></div>
		                <div id="knob-value" class="knob-value">50KΩ</div>
		            </div>
		            <div class="knob-container" style="margin-left: 10px;">
		                <div class="knob" identifier="R1" befR="0" curR="0"></div>
		                <div id="knob-value" class="knob-value">50KΩ</div>
		            </div>-->
	            </div>
	            <img    style="position: absolute;right: 20px;margin-top: 10px; width: 45px; height: 45px"   src="./quanping.png" onclick="launchFullscreen()" ></img>
	            <button style=" margin-left: 10px; margin-top: 20px;" onclick="fuwei()" type="button"disabled="disabled" class="btn btn-info">复位</button>
	            <button style=" margin-left: 10px; margin-top: 20px;" onclick="openpower()" type="button"disabled="disabled" class="btn btn-info">开电</button>
	            	<button style=" margin-left: 10px; margin-top: 20px;" onclick="closepower()" type="button"disabled="disabled" class="btn btn-info">关电</button>
	            <button onclick="createmydir()" type="button" class="btn btn-info">创建实验环境</button>
           		<button id="togbtn"  onclick="toggleFloatingBox()" type="button" class="btn btn-info">实验记录</button>
            	<button id="togbtn"  onclick="loadRemoteWin()"type="button" class="btn btn-info loadRemoteWin">💻</button>
            	
            	
            	
                <button id="togbtn"  onclick="loaddlt()" type="button" class="btn btn-info loaddlt" style="display: none;">📈</button>
                
			<!--	<button id="togbtn"  onclick="changWin('UPO')" type="button" class="btn btn-info upo"style="display: none;">示波器</button>
				
				<button id="togbtn"  onclick="changWin('SG')" type="button" class="btn btn-info sg" style="display: none;">信号原</button>
				-->
				
				<!--<button id="togbtn"  onclick="changWin('SG')" type="button" class="btn btn-info">
					S开
				</button>-->
				
				
            </div>
            
            
            
            

        </div>
    </div>

    <!-- 可拖动示波器 -->
    <div class="oscilloscope" id="oscilloscope" >
        <div class="oscilloscope-header" id="oscilloscope-header">
            <div class="oscilloscope-title">示波器
            	<!--<label><input type="radio" name="sbq"onclick="optionsbq(1)" />单</label>
            	<label><input type="radio" name="sbq" onclick="optionsbq(2)"checked="checked" />双</label>-->
           		<!--<span class="remote-icon"onclick="openRemoteDesktop()">💻</span>-->
           		<span class="loadRemoteWin" class="remote-icon"onclick="loadRemoteWin()">💻</span>
            	<span class="loaddlt" onclick="loaddlt()" style="display: none;">📈</span>
            </div> 
            <span class="material-icons" style="font-size:14px">drag_indicator</span>
        </div>
        <div class="oscilloscope-content">
        	
            <div class="oscilloscope-channel sbqlin1">
                <div class="probe probe-1" id="probe-1" data-channel="1"></div>
                <div class="channel-title">通道 1</div>
                <div class="channel-value" id="channel-1-value" <!--onclick="changeCSD(1,this)"-->>未连接</div>
            </div>
            <div class="oscilloscope-channel sbqlin2">
                <div class="probe probe-2" id="probe-2" data-channel="2"></div>
                <div class="channel-title">通道 2</div>
                <div class="channel-value" id="channel-2-value" <!--onclick="changeCSD(2,this)"-->>未连接</div>
            </div>
            <div class="oscilloscope-channel sbqlin0" >
                <div class="probe probe-0" id="probe-0" data-channel="0"></div>
                <div class="channel-title">地</div>
                <div class="channel-value" id="channel-0-value"  <!--onclick="changeCSD(0,this)"-->>未连接</div>
            </div>
        </div>
        <!-- 添加远程桌面按钮 -->
        <!--<div class="remote-desktop-button-container">
            <button id="open-remote-desktop" class="remote-desktop-button" onclick="openRemoteDesktop()">
                <span class="remote-icon">💻</span>
                打开远程桌面
            </button>
        </div>-->
    </div>

    <!-- 动态生成的仪表组件容器 -->
    <div id="dynamic-meters-container"></div>
     <!--<div class="xhyDiv"id="xhyDiv" onclick="changWin('SG')">
        <div class="oscilloscope-header" >
            <div class="oscilloscope-title">信号源
            </div> 
             <span class="material-icons" style="font-size:14px">drag_indicator</span>
        </div>
        
      
    </div>-->
    <script>
        console.log('🎯 syhj.html 页面开始执行...');
        console.log('🎯 页面URL:', window.location.href);

        // 记录当前的仪表数据（用于生成组件）
        console.log('🎯 localStorage 电流表数据:', localStorage.getItem('ammeters'));
        console.log('🎯 localStorage 电压表数据:', localStorage.getItem('voltmeters'));

    	var remoteWinParam="MwBjAG15c3Fs?username=tech&password=Tech1234"
    	var ychEquIp="*************"
    	var commObj={};
    	function loadSwitchs() {
    		  $(".switchs").remove()
    		  let switchs = localStorage.getItem('switchs');
              const switchJSON = JSON.parse(switchs); 
              if (!switchJSON || !Array.isArray(switchJSON) || switchJSON.length === 0) {
               	return;
              }
              
              switchJSON.forEach(swi => {
              	$(".bottombtn").append('<button cursta="0"  onclick="switchsCon(\''+swi.identifier+'\',this)" type="button" class="btn btn-info switchs">'+swi.identifier+'开</button>')
              })
    	}
    	function loadSwitchs2() {
    		  $(".sswitchs").remove()
    		  let switchs = localStorage.getItem('SPDTSwitchs');
              const switchJSON = JSON.parse(switchs); 
              if (!switchJSON || !Array.isArray(switchJSON) || switchJSON.length === 0) {
               	return;
              }
              
              switchJSON.forEach(swi => {
              	var identifier=swi.identifier
              	var sarr=identifier.split(",")
              	if(arr!=2){
              		$(".bottombtn").append('<button id="'+sarr[0]+'" cursta="0"  onclick="switchsCon2(\''+sarr[0]+'\',\''+sarr[1]+'\',this)" type="button" class="btn btn-info sswitchs">'+sarr[0]+'开</button>')
              		$(".bottombtn").append('<button id="'+sarr[1]+'" cursta="0"  onclick="switchsCon2(\''+sarr[1]+'\',\''+sarr[0]+'\',this)" type="button" class="btn btn-info sswitchs">'+sarr[1]+'开</button>')
              	}else{
              		$(".bottombtn").append('<button cursta="0"  onclick="switchsCon(\''+swi.identifier+'\',this)" type="button" class="btn btn-info sswitchs">'+swi.identifier+'开</button>')
              	}
              })
    	}
        // 从测点关系数据构建commObj，支持分组匹配逻辑
        function buildCommObjFromTestPointRelations() {
            try {
                console.log('🔍 开始构建commObj（优先使用localStorage正确数据）...');

                // 🎯 优先使用localStorage中的正确commObjData
                let correctCommObjData = localStorage.getItem('commObjData');
                if (!correctCommObjData) {
                    correctCommObjData = sessionStorage.getItem('commObjData');
                }

                if (correctCommObjData) {
                    console.log('✅ 发现localStorage中的正确commObjData，直接使用');
                    const correctData = JSON.parse(correctCommObjData);
                    console.log('📋 正确的commObjData:', correctData);

                    // 清空现有的commObj并使用正确数据
                    Object.keys(commObj).forEach(key => delete commObj[key]);
                    Object.assign(commObj, correctData);

                    console.log('✅ 使用正确commObjData完成，当前commObj:', commObj);
                    return true;
                }

                // 如果没有正确数据，才使用原来的构建逻辑
                console.log('⚠️ 未找到localStorage中的正确数据，使用原构建逻辑');

                // 获取模板测点关系数据
                let testPointRelationsData = localStorage.getItem('testPointRelations');
                if (!testPointRelationsData) {
                    testPointRelationsData = sessionStorage.getItem('testPointRelations');
                }

                // 获取学生测点关系数据
                let studentTestPointData = localStorage.getItem('test_point_relations');
                if (!studentTestPointData) {
                    studentTestPointData = sessionStorage.getItem('test_point_relations');
                }

                if (!testPointRelationsData) {
                    console.log('⚠️ 未找到模板测点关系数据');
                    return false;
                }

                const templateRelations = JSON.parse(testPointRelationsData);
                console.log('📋 模板测点关系数据:', templateRelations);

                // 清空现有的commObj
                Object.keys(commObj).forEach(key => delete commObj[key]);

                if (studentTestPointData) {
                    // 有学生数据，执行分组匹配逻辑
                    const studentData = JSON.parse(studentTestPointData);
                    const studentRelations = studentData.groupedByTestPoint || {};
                    console.log('📋 学生测点关系数据:', studentRelations);

                    // 执行分组匹配
                    const matchingResult = performTestPointGroupMatching(templateRelations, studentRelations);
                    console.log('� 分组匹配结果:', matchingResult);

                    // 根据匹配结果构建commObj
                    buildCommObjFromMatchingResult(matchingResult, templateRelations, studentRelations);
                } else {
                    // 没有学生数据，直接使用模板数据
                    console.log('⚠️ 未找到学生测点数据，直接使用模板数据');
                    Object.keys(templateRelations).forEach(groupKey => {
                        const group = templateRelations[groupKey];
                        if (group.testPoint) {
                            const testPoint = group.testPoint;
                            const identifier = testPoint.identifier;

                            commObj[identifier] = {
                                bval: testPoint.bval || testPoint.identifier,
                                channel: testPoint.channel || "1", // 🎯 修复：使用默认通道1，不使用groupKey
                                templateGroupId: groupKey // 记录模板分组ID
                            };

                            console.log(`✅ 添加模板测点映射: ${identifier} -> 分组${groupKey}, bval:${commObj[identifier].bval}, channel:${commObj[identifier].channel}`);
                        }
                    });
                }

                console.log('✅ 完整的commObj:', commObj);
                return true;
            } catch (error) {
                console.error('❌ 构建commObj时出错:', error);
                return false;
            }
        }

        // 执行测点分组匹配
        function performTestPointGroupMatching(templateRelations, studentRelations) {
            const templateGroups = [];
            const studentGroups = [];

            // 提取模板分组
            Object.keys(templateRelations).forEach(groupKey => {
                const group = templateRelations[groupKey];
                if (group.testPoint && group.connectedDevices) {
                    const deviceTypes = group.connectedDevices
                        .map(d => d.typeName)
                        .sort();
                    templateGroups.push({
                        id: groupKey,
                        testPointId: group.testPoint.identifier,
                        deviceTypes: deviceTypes,
                        deviceTypesStr: JSON.stringify(deviceTypes)
                    });
                }
            });

            // 提取学生分组
            Object.keys(studentRelations).forEach(testPointId => {
                const group = studentRelations[testPointId];
                if (group.testPoint && group.connectedDevices) {
                    const deviceTypes = group.connectedDevices
                        .map(d => d.typeName)
                        .sort();
                    studentGroups.push({
                        id: testPointId,
                        testPointId: group.testPoint.identifier,
                        deviceTypes: deviceTypes,
                        deviceTypesStr: JSON.stringify(deviceTypes)
                    });
                }
            });

            console.log('📋 模板分组:', templateGroups);
            console.log('📋 学生分组:', studentGroups);

            // 执行匹配
            const matchingResults = {};
            const usedStudentGroups = new Set();

            templateGroups.forEach(templateGroup => {
                let foundMatch = false;

                for (let i = 0; i < studentGroups.length; i++) {
                    if (usedStudentGroups.has(i)) continue;

                    const studentGroup = studentGroups[i];
                    if (templateGroup.deviceTypesStr === studentGroup.deviceTypesStr) {
                        foundMatch = true;
                        usedStudentGroups.add(i);

                        matchingResults[studentGroup.testPointId] = {
                            templateGroupId: templateGroup.id,
                            studentTestPointId: studentGroup.testPointId,
                            matched: true,
                            channel: templateGroup.id // 使用模板分组ID作为通道
                        };

                        console.log(`✅ 匹配成功: 学生测点${studentGroup.testPointId} ↔ 模板分组${templateGroup.id}`);
                        break;
                    }
                }

                if (!foundMatch) {
                    console.log(`❌ 未匹配: 模板分组${templateGroup.id}`);
                }
            });

            return matchingResults;
        }

        // 根据匹配结果构建commObj
        function buildCommObjFromMatchingResult(matchingResult, templateRelations, studentRelations) {
            Object.keys(matchingResult).forEach(studentTestPointId => {
                const match = matchingResult[studentTestPointId];
                if (match.matched) {
                    const templateGroup = templateRelations[match.templateGroupId];
                    if (templateGroup && templateGroup.testPoint) {
                        const templateTestPoint = templateGroup.testPoint;

                        commObj[studentTestPointId] = {
                            bval: templateTestPoint.bval || templateTestPoint.identifier,
                            channel: templateTestPoint.channel || "1", // 🎯 修复：使用模板的真实channel，不使用templateGroupId
                            templateGroupId: match.templateGroupId,
                            studentTestPointId: studentTestPointId
                        };

                        console.log(`✅ 构建匹配映射: 学生测点${studentTestPointId} -> 模板分组${match.templateGroupId}, bval:${commObj[studentTestPointId].bval}, channel:${commObj[studentTestPointId].channel}`);
                    }
                }
            });
        }

        function loadTestPoints() {
        	loadSwitchs();//单刀
        	loadSwitchs2();//双刀

        	console.log('🔧 开始加载测点...');

        	// 首先构建commObj
        	console.log('🔧 构建commObj...');
        	const buildSuccess = buildCommObjFromTestPointRelations();
        	console.log('🔧 commObj构建结果:', buildSuccess ? '成功' : '失败');
        	console.log('🔧 当前commObj状态:', commObj);

            try {
                // 获取测试点数据
                console.log('🔧 获取测试点数据...');
                let testPointsData = localStorage.getItem('circuit_test_points');
                if (!testPointsData) {
                    testPointsData = sessionStorage.getItem('circuit_test_points');
                }
                if (!testPointsData) {
                    console.log('⚠️ 未找到测试点数据');
                    return;
                }
                console.log('✅ 找到测试点数据:', testPointsData);
                // 解析测试点数据
                const testPoints = JSON.parse(testPointsData);
                if (!testPoints || !Array.isArray(testPoints) || testPoints.length === 0) {
                    console.log('测试点数据为空或格式不正确');
                    return;
                }
                
                // 获取图片元素
                const imgElement = document.getElementById('local-image');
                if (!imgElement || imgElement.style.display === 'none') {
                    console.log('图片不可见，无法添加测试点');
                    return;
                }
                
                // 获取图片的实际尺寸
                const imgRect = imgElement.getBoundingClientRect();
                const imgWidth = imgRect.width;
                const imgHeight = imgRect.height;
                
                // 获取图片容器
                const container = document.getElementById('image-container');
                
                // 清除已有的测试点
                const existingPoints = container.querySelectorAll('.test-point');
                existingPoints.forEach(point => point.remove());
                
                // 设置偏移量初始值（可以通过这些值进行微调）
                const offsetX = 58; // 水平偏移量，正值向右移动，负值向左移动
                const offsetY = 31; // 垂直偏移量，正值向下移动，负值向上移动
                // 添加测试点
                testPoints.forEach(point => {
                    // 创建测试点元素
                    const testPoint = document.createElement('div');
                    var identifier=point.identifier.trim();
                    
                    
                    testPoint.className = 'test-point';
                    
                    // 设置测试点标签
                    testPoint.setAttribute('data-label', point.label || 'TP');
                    testPoint.setAttribute('data-identifier', point.identifier.trim() || '信号');
                    testPoint.setAttribute('data-value', point.value || '1');
                    
                    
                    // 计算测试点在图片上的位置（基于百分比）
                    // 注意：这里使用图片的实际尺寸，而不是容器尺寸
                    const x = (point.x / 100) * imgWidth + imgRect.left - container.getBoundingClientRect().left + offsetX;
                    const y = (point.y / 100) * imgHeight + imgRect.top - container.getBoundingClientRect().top + offsetY;
                    
                    // 设置测试点位置
                    testPoint.style.left = `${x}px`;
                    testPoint.style.top = `${y}px`;
                    
                    // 添加到容器
                    container.appendChild(testPoint);
                    var pointobj=commObj[identifier]

                    // 🔧 原来的通道检查逻辑（注释掉）
                    // if(pointobj&&pointobj.channel&&pointobj.channel.indexOf("0")>=0){
                    // 	$(testPoint).addClass("pointchannel0")
                    // }
                    // if(pointobj&&pointobj.channel&&pointobj.channel.indexOf("1")>=0){
                    // 	$(testPoint).addClass("pointchannel1")
                    // }
                    // if(pointobj&&pointobj.channel&&pointobj.channel.indexOf("2")>=0){
                    // 	$(testPoint).addClass("pointchannel2")
                    // }

                    // 🔧 修复后的通道绑定逻辑（支持多通道）
                    if (pointobj && pointobj.channel) {
                        const channel = pointobj.channel.toString();

                        // 支持多通道：使用indexOf检查，一个测点可以属于多个通道
                        if (channel.indexOf("0") >= 0) {
                            $(testPoint).addClass("pointchannel0");
                        }
                        if (channel.indexOf("1") >= 0) {
                            $(testPoint).addClass("pointchannel1");
                        }
                        if (channel.indexOf("2") >= 0) {
                            $(testPoint).addClass("pointchannel2");
                        }
                        if (channel.indexOf("3") >= 0) {
                            $(testPoint).addClass("pointchannel3");
                        }

                        // 添加数据属性，便于调试和连接
                        testPoint.setAttribute('data-channel', channel);
                        testPoint.setAttribute('data-bval', pointobj.bval || identifier);
                        if (pointobj.templateGroupId) {
                            testPoint.setAttribute('data-template-group', pointobj.templateGroupId);
                        }


                    } else {
                        // 使用默认设置
                        $(testPoint).addClass("pointchannel1");
                        testPoint.setAttribute('data-channel', '1');
                        testPoint.setAttribute('data-bval', identifier);

                    }

                    console.log(`添加测试点: ${point.label} 在位置 (${x}, ${y}), 标识符: ${identifier}`);
                });
               
            } catch (error) {
                console.error('加载测试点时出错:', error);
            }
        }

        /**
         * 检查并动态生成仪表组件 - 暂时注释掉整个函数
         */
        /*
        function checkAndShowMeterComponents() {
            const container = document.getElementById('dynamic-meters-container');
            if (!container) {
                return;
            }

            container.innerHTML = '';

            const ammetersData = localStorage.getItem('ammeters');
            const voltmetersData = localStorage.getItem('voltmeters');

            let hasAmmeters = false;
            let hasVoltmeters = false;

            try {
                hasAmmeters = ammetersData && JSON.parse(ammetersData).length > 0;
            } catch (e) {
                hasAmmeters = false;
            }

            try {
                hasVoltmeters = voltmetersData && JSON.parse(voltmetersData).length > 0;
            } catch (e) {
                hasVoltmeters = false;
            }

            const hasAnyMeter = hasAmmeters || hasVoltmeters;

            if (!hasAnyMeter) {
                return;
            }

            let currentTop = 250;

            if (ammetersData) {
                try {
                    let ammeters = JSON.parse(ammetersData);

                    if (Array.isArray(ammeters)) {
                        const uniqueAmmeters = [];
                        const seenIds = new Set();

                        ammeters.forEach(ammeter => {
                            const key = `${ammeter.id}_${ammeter.identifier}`;
                            if (!seenIds.has(key)) {
                                seenIds.add(key);
                                uniqueAmmeters.push(ammeter);
                            }
                        });

                        ammeters = uniqueAmmeters;
                    }

                    if (Array.isArray(ammeters) && ammeters.length > 0) {
                        ammeters.forEach((ammeter, index) => {
                            const identifier = ammeter.identifier || `A${index + 1}`;

                            const meterDiv = document.createElement('div');
                            meterDiv.style.cssText = `
                                position: fixed;
                                top: ${currentTop}px;
                                right: 10px;
                                width: 180px;
                                height: 100px;
                                background: lightblue;
                                border: 2px solid blue;
                                border-radius: 8px;
                                box-shadow: 0 4px 20px rgba(0,0,255,0.5);
                                z-index: 9999;
                                padding: 10px;
                            `;

                            meterDiv.innerHTML = `
                                <div style="font-weight: bold; margin-bottom: 8px;">电流表 ${identifier}</div>
                                <div style="font-size: 18px; color: #333;">0.00 A</div>
                                <div style="font-size: 12px; color: #999;">量程: ${ammeter.range || '0-10A'}</div>
                            `;

                            container.appendChild(meterDiv);
                            currentTop += 120;
                        });
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }

            if (voltmetersData) {
                try {
                    const voltmeters = JSON.parse(voltmetersData);

                    if (Array.isArray(voltmeters) && voltmeters.length > 0) {
                        voltmeters.forEach((voltmeter, index) => {
                            const identifier = voltmeter.identifier || `V${index + 1}`;

                            try {
                                const meterDiv = document.createElement('div');
                            meterDiv.style.cssText = `
                                position: fixed;
                                top: ${currentTop}px;
                                right: 10px;
                                width: 180px;
                                height: 100px;
                                background: lightgreen;
                                border: 2px solid green;
                                border-radius: 8px;
                                box-shadow: 0 4px 20px rgba(0,255,0,0.5);
                                z-index: 9999;
                                padding: 10px;
                            `;

                            meterDiv.innerHTML = `
                                <div style="font-weight: bold; margin-bottom: 8px;">电压表 ${identifier}</div>
                                <div style="font-size: 18px; color: #333;">0.00 V</div>
                                <div style="font-size: 12px; color: #999;">量程: ${voltmeter.range || '0-30V'}</div>
                            `;

                                container.appendChild(meterDiv);
                                currentTop += 120;
                            } catch (error) {
                                // 静默处理错误
                            }
                        });
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }

        }
        */

        // 函数定义完成，等待DOMContentLoaded时调用

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOMContentLoaded 事件触发');

            // 🔧 优先检查并显示仪表组件 - 暂时注释掉
            // checkAndShowMeterComponents();

            // 加载本地存储的图片
            //loadImageFromLocalStorage();
            setTimeout(loadImageFromLocalStorage, 1000);
            // 监听自定义事件来加载测试点
//          document.addEventListener('load-test-points', loadTestPoints);
//          
//          // 直接加载测试点，不等待事件
//          setTimeout(loadTestPoints, 100);
           
            // 在窗口大小改变时更新测试点显示
            window.addEventListener('resize', function() {
                if (typeof updateAllProbeLines === 'function') {
                    updateAllProbeLines();
                }
            });
            
            // 示波器拖动功能
            const oscilloscope = document.getElementById('oscilloscope');
            const header = document.getElementById('oscilloscope-header');
            //const xhyDiv = document.getElementById('xhyDiv');
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            // 探头相关变量
            let activeProbe = null;
            let probeLine = null;
            const probeLines = new Map();

            // 示波器拖动
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);
			//xhyDiv.addEventListener('mousedown', ddown);
			//xhyDiv.addEventListener('mousemove', dmove);
			//xhyDiv.addEventListener('mouseup', dup);
            var xhyOffx=0;
            var xhyOffy=0;
            var xhyx=0;
            var xhyy=0;
            var active=false;
            function dragStart(e) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
                
                if (e.target === header || e.target.parentNode === header) {
                    isDragging = true;
                }
            }
			function dup(e){
               xhyx = currentX;
			    xhyy = currentY;
			    active = false;
			}
			function ddown(e){
				xhyx = e.clientX - xhyOffx;
                xhyy = e.clientY - xhyOffy;
                if (e.target === xhyDiv) {
        			active = true;
    			}
			}
			function dmove(e){
                if (active) {
                    e.preventDefault();
                    currentX = e.clientX - xhyx;
			        currentY = e.clientY - xhyy;
			 
			        xhyOffx = currentX;
			        xhyOffy = currentY;
			        xhyDiv.style.transform = "translate3d(" + currentX + "px, " + currentY + "px, 0)";
                 }
			}
            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;
					var domid=e.target.id   
					//if(domid=="xhyDiv"){
					//	e.target.style.transform = `translate(${currentX}px, ${currentY}px)`;
					//}else{
						oscilloscope.style.transform = `translate(${currentX}px, ${currentY}px)`;
                    	updateAllProbeLines();
					//}
                    
                }
            }

            function dragEnd() {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            }
            initProbes();
            //初始化连线事件
			function initProbes(){
				const probes = document.querySelectorAll('.probe');
            	probes.forEach(probe => {
                probe.addEventListener('mousedown', function(e) {
                	 const channel = probe.getAttribute('data-channel');
	                    const valueDisplay = document.getElementById(`channel-${channel}-value`);
	                    $(valueDisplay).attr("isyc","false").attr("pvalue",0).attr("pidentifier","")
	                    valueDisplay.textContent = '未连接';
	                    valueDisplay.style.color = '#999';
                    e.preventDefault();
                    e.stopPropagation();
                    
                    activeProbe = probe;

                    // 🔧 动态应用多通道支持：在每次点击探头时重新检查和设置测点样式类
                    document.querySelectorAll('.test-point').forEach(testPoint => {
                        const identifier = testPoint.getAttribute('data-identifier');
                        const pointobj = commObj[identifier];

                        if (pointobj && pointobj.channel) {
                            const pointChannel = pointobj.channel.toString();

                            // 动态添加缺失的通道样式类
                            if (pointChannel.indexOf("0") >= 0 && !testPoint.classList.contains("pointchannel0")) {
                                testPoint.classList.add("pointchannel0");
                            }
                            if (pointChannel.indexOf("1") >= 0 && !testPoint.classList.contains("pointchannel1")) {
                                testPoint.classList.add("pointchannel1");
                            }
                            if (pointChannel.indexOf("2") >= 0 && !testPoint.classList.contains("pointchannel2")) {
                                testPoint.classList.add("pointchannel2");
                            }
                            if (pointChannel.indexOf("3") >= 0 && !testPoint.classList.contains("pointchannel3")) {
                                testPoint.classList.add("pointchannel3");
                            }
                        }
                    });

                     $(".test-point").removeClass("redpoint")
                     $(".pointchannel"+channel).addClass("redpoint");
                    // 移除已有连线
                    if (probe.connectedLine) {
                        probe.connectedLine.remove();
                        probe.connectedLine = null;
                        
                        // 恢复测试点状态
                        if (probe.connectedPoint) {
                            probe.connectedPoint.classList.remove('connected');
                            probe.connectedPoint = null;
                            
                        }
                        
                        // 恢复通道显示
//                      const channel = probe.getAttribute('data-channel');
//                      $(valueDisplay).attr("isyc","false").attr("pvalue",0).attr("pidentifier","")
//                      const valueDisplay = document.getElementById(`channel-${channel}-value`);
//                      valueDisplay.textContent = '未连接';
//                      valueDisplay.style.color = '#999';
                    }

                    // 创建新连线 - 使用SVG实现曲线
                    probeLine = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    probeLine.setAttribute('width', '100%');
                    probeLine.setAttribute('height', '100%');
                    probeLine.style.position = 'fixed';
                    probeLine.style.top = '0';
                    probeLine.style.left = '0';
                    probeLine.style.pointerEvents = 'none';
                    probeLine.style.zIndex = '1001';
                    
                    // 创建路径
                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path.setAttribute('fill', 'none');
                    path.setAttribute('stroke', probe.getAttribute('data-channel') == '1' ? '#ff5722' : '#2196f3');
                    path.setAttribute('stroke-width', '2');
                    path.setAttribute('stroke-dasharray', '5,3'); // 保留虚线效果
                    probeLine.appendChild(path);
                    
                    document.body.appendChild(probeLine);

                    // 立即更新一次线条位置，确保起点正确
                    const probeRect = getProbeCenter(probe);
                    updateProbeLinePosition(probeRect.x, probeRect.y, e.clientX, e.clientY, probeLine);
					
                    document.addEventListener('mousemove', onProbeMove);
                    document.addEventListener('mouseup', onProbeUp);
                });
            });
			}
            // 探头拖动功能
           

            // 获取探头中心点的辅助函数
            function getProbeCenter(probe) {
                const rect = probe.getBoundingClientRect();
                // 获取探头的中心点
                const x = rect.left + rect.width / 2;
                const y = rect.top + rect.height / 2;
                
                // 获取探头的计算样式
                const style = window.getComputedStyle(probe);
                
                // 考虑边框宽度的影响
                const borderLeft = parseFloat(style.borderLeftWidth) || 0;
                const borderTop = parseFloat(style.borderTopWidth) || 0;
                
                // 考虑变换的影响
                const transform = style.transform;
                let offsetX = 0;
                let offsetY = 0;
                
                // 返回调整后的中心点
                return {
                    x: x + offsetX,
                    y: y + offsetY
                };
            }

            function onProbeMove(e) {
                if (!activeProbe || !probeLine) return;

                const probeCenter = getProbeCenter(activeProbe);
                updateProbeLinePosition(probeCenter.x, probeCenter.y, e.clientX, e.clientY, probeLine);

                // 检查是否悬停在测试点上
                const testPoints = document.querySelectorAll('.test-point');
                testPoints.forEach(point => {
                    const isNear = isPointNearTestPoint(e.clientX, e.clientY, point);
                    point.classList.toggle('highlight', isNear);
                });
            }

            function onProbeUp(e) {
                if (!activeProbe || !probeLine) return;
                
                const testPoints = document.querySelectorAll('.test-point');
                let connected = false;

                testPoints.forEach(point => {
                    if (isPointNearTestPoint(e.clientX, e.clientY, point)) {
                        connectProbeToTestPoint(activeProbe, point, probeLine);
                        connected = true;
                    }
                    point.classList.remove('highlight');
                });

                if (!connected && probeLine) {
                    probeLine.remove();
                }

                document.removeEventListener('mousemove', onProbeMove);
                document.removeEventListener('mouseup', onProbeUp);
                activeProbe = null;
            }

            // 连接探头到测试点
            function connectProbeToTestPoint(probe, testPoint, line) {
                const probeCenter = getProbeCenter(probe);
                const pointRect = testPoint.getBoundingClientRect();
                
                const endX = pointRect.left + pointRect.width / 2;
                const endY = pointRect.top + pointRect.height / 2;

                updateProbeLinePosition(probeCenter.x, probeCenter.y, endX, endY, line);
                
                probe.connectedLine = line;
                probe.connectedPoint = testPoint;
                probeLines.set(probe, line);
                
                // 更新测试点和探头状态
                testPoint.classList.add('connected');
                
                // 更新示波器显示
                const channel = probe.getAttribute('data-channel');
                const valueDisplay = document.getElementById(`channel-${channel}-value`);
                valueDisplay.textContent = testPoint.getAttribute('data-label') || 'TP';
                valueDisplay.style.color = '#4CAF50';
                var pidentifier=$(testPoint).attr("data-identifier")
                var pvalue=$(testPoint).attr("data-value")
				$(".test-point").removeClass("redpoint")
               	$(valueDisplay).attr("isyc","true").attr("pvalue",pvalue).attr("pidentifier",pidentifier)
               	changeCSD(channel,valueDisplay)
            }
            
            // 更新为曲线连线
            function updateProbeLinePosition(startX, startY, endX, endY, line) {
                if (!line) return;
                
                const path = line.querySelector('path');
                if (!path) return;
                
                // 计算控制点，使曲线看起来自然
                const dx = endX - startX;
                const dy = endY - startY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                // 为了创建曲线，我们需要让控制点偏离起点-终点连线
                // 计算垂直于连线的方向
                const perpX = -dy;
                const perpY = dx;
                const perpLength = Math.sqrt(perpX * perpX + perpY * perpY);
                
                // 曲线的弯曲程度
                const curvature = distance * 0.2; // 调整这个值可以改变弯曲程度
                
                // 计算控制点的偏移量
                const offsetX = perpX / perpLength * curvature;
                const offsetY = perpY / perpLength * curvature;
                
                // 第一个控制点：靠近起点，但有垂直偏移
                const controlX1 = startX + dx * 0.3 + offsetX;
                const controlY1 = startY + dy * 0.3 + offsetY;
                
                // 第二个控制点：靠近终点，但有垂直偏移
                const controlX2 = startX + dx * 0.7 + offsetX;
                const controlY2 = startY + dy * 0.7 + offsetY;
                
                // 创建贝塞尔曲线路径
                const pathData = `M ${startX},${startY} C ${controlX1},${controlY1} ${controlX2},${controlY2} ${endX},${endY}`;
                path.setAttribute('d', pathData);
            }

            function isPointNearTestPoint(x, y, testPoint) {
                const rect = testPoint.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const distance = Math.sqrt(
                    Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
                );
                return distance < 20; // 20px范围内判定为接近
            }

            function updateAllProbeLines() {
                probeLines.forEach((line, probe) => {
                    if (probe.connectedPoint) {
                        const probeCenter = getProbeCenter(probe);
                        const pointRect = probe.connectedPoint.getBoundingClientRect();
                        
                        const endX = pointRect.left + pointRect.width / 2;
                        const endY = pointRect.top + pointRect.height / 2;

                        updateProbeLinePosition(probeCenter.x, probeCenter.y, endX, endY, line);
                    }
                });
            }

            // 旋钮控制
            console.log('DOM加载完成，初始化旋钮控制...');
            
            // 获取旋钮元素
            const knobContainer = document.querySelector('.knob-container');
            const knob = document.querySelector('.knob');
            const knobValue = document.getElementById('knob-value');
            
            console.log('旋钮元素:', knob);
            console.log('旋钮值元素:', knobValue);
            
            if (!knob || !knobValue) {
                console.error('找不到旋钮元素，请检查HTML结构');
                return;
            }
            
            let knobRotation = 0;
            let resistanceValue = 50; // 默认值，单位为kΩ
            let isKnobDragging = false;
            let knobStartY;

            // 初始化旋钮位置
            knob.style.setProperty('--rotation', knobRotation + 'deg');

            // 从localStorage或API获取可变电阻器数据
            function loadVariableResistors() {
                try {
                    // 尝试从localStorage获取数据 - 修复key名称
                    let resistorsData = localStorage.getItem('variableResistors');
                    if (!resistorsData) {
                        resistorsData = sessionStorage.getItem('variableResistors');
                    }
                    
                    if (resistorsData) {
                        console.log('找到可变电阻器数据:', resistorsData);
                        const resistors = JSON.parse(resistorsData);
                        
                        // 确保resistors是数组且有数据
                        if (resistors && Array.isArray(resistors) && resistors.length > 0) {
                            console.log(`电阻器数量: ${resistors.length}`);
                            
                            // 使用第一个可变电阻器的值更新现有旋钮
                            const firstResistor = resistors[0];
                            if (firstResistor && typeof firstResistor.value === 'number') {
                                resistanceValue = firstResistor.value;
                                
                                // 计算旋钮旋转角度 (-150到150度)
                                knobRotation = ((resistanceValue / 100) * 300) - 150;
                                
                                // 更新旋钮显示
                                knob.style.setProperty('--rotation', knobRotation + 'deg');
                                knobValue.textContent = firstResistor.identifier;
                                $(knob).attr("identifier",firstResistor.identifier).attr("curR",knobRotation).attr("befR",knobRotation)
                                console.log('已加载电阻值:', resistanceValue + 'kΩ', '旋转角度:', knobRotation + 'deg');
                            }
                            
                            // 移除所有现有的额外旋钮
                            const existingExtraKnobs = document.querySelectorAll('.extra-knob-container');
                            existingExtraKnobs.forEach(knob => knob.remove());
                            
                            // 如果有多个电阻器，创建额外的旋钮
                            if (resistors.length > 1) {
                                // 获取现有旋钮容器的位置
                                const existingKnob = document.querySelector('.knob-container');
                                if (existingKnob) {
                                    const rect = existingKnob.getBoundingClientRect();
                                    
                                    // 为其余电阻器创建旋钮
                                    for (let i = 1; i < resistors.length; i++) {
                                        const resistor = resistors[i];
                                        
                                        // 创建旋钮容器
                                        const newKnobContainer = document.createElement('div');
                                        newKnobContainer.className = 'knob-container extra-knob-container';
                                        
                                        // 设置位置 - 修正为相对于文档的绝对位置
//                                      newKnobContainer.style.cssText = `
//                                          position: absolute;
//                                          top: ${rect.top + window.scrollY}px;
//                                          left: ${rect.right + 20 + (i-1) * (rect.width + 20)}px;
//                                          z-index: 200;
//                                      `;
                                        newKnobContainer.style.cssText = `
                                            z-index: 200;
                                        `;
                                        // 复制现有旋钮的HTML结构
                                        newKnobContainer.innerHTML = existingKnob.innerHTML;
                                        
                                        // 更新ID和数据属性
                                        const newKnob = newKnobContainer.querySelector('.knob');
                                        const newKnobInner = newKnobContainer.querySelector('.knob-inner');
                                        const newKnobValue = newKnobContainer.querySelector('.knob-value');
                                        
                                        if (newKnobInner) newKnobInner.id = `resistance-knob-${resistor.id}`;
                                        if (newKnobValue) newKnobValue.id = `knob-value-${resistor.id}`;
                                        
                                        // 设置电阻值和旋转角度
                                        const value = resistor.value || 50;
                                        const rotation = ((value / 100) * 300) - 150;
                                        $(newKnob).attr("identifier",resistor.identifier).attr("curR",knobRotation).attr("befR",knobRotation)

                                        if (newKnob) newKnob.style.setProperty('--rotation', rotation + 'deg');
                                        if (newKnobValue) newKnobValue.textContent = resistor.identifier;
                                        
                                        // 添加到文档
                                        $("#knoblist").append(newKnobContainer)
                                        //document.body.appendChild(newKnobContainer);
                                        
                                        // 添加事件监听
                                        if (newKnob) {
                                            newKnob.addEventListener('mousedown', function(e) {
                                                handleAdditionalKnobInteraction(e, newKnob, newKnobValue, resistor.id);
                                            });
                                            newKnob.addEventListener('mousewheel', mousewheel);
                                        }
                                        
                                        console.log(`已创建额外旋钮: ${resistor.id}, 位置: top=${rect.top}px, left=${rect.right + 20 + (i-1) * (rect.width + 20)}px, 值: ${value}kΩ`);
                                    }
                                }
                            }
                        } else {
                            console.log('电阻器数据无效或为空');
                        }
                    } else {
                        console.log('未找到可变电阻器数据，使用默认值');
                    }
                } catch (error) {
                    console.error('加载可变电阻器数据时出错:', error);
                }
            }



            /**
             * 创建仪表组件元素
             */
            function createMeterComponent(type, identifier, range, top) {
                console.log(`🔧 创建${type}组件: ${identifier}, 位置: ${top}px`);

                const meterDiv = document.createElement('div');
                meterDiv.className = `meter-component ${type}-component`;
                meterDiv.id = `${type}-${identifier}`;

                // 简化的样式设置
                meterDiv.style.position = 'fixed';
                meterDiv.style.top = top + 'px';
                meterDiv.style.right = '200px';
                meterDiv.style.width = '180px';
                meterDiv.style.background = 'white';
                meterDiv.style.borderRadius = '8px';
                meterDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                meterDiv.style.zIndex = '1000';
                meterDiv.style.border = '1px solid #ddd';

                const typeText = type === 'ammeter' ? '电流表' : '电压表';
                const unit = type === 'ammeter' ? 'A' : 'V';

                // 简化的HTML结构
                meterDiv.innerHTML = `
                    <div class="meter-header" style="
                        padding: 10px 15px;
                        background: #f5f5f5;
                        border-radius: 8px 8px 0 0;
                        border-bottom: 1px solid #ddd;
                        cursor: move;
                        font-weight: bold;
                    ">
                        ${typeText} ${identifier}
                    </div>
                    <div class="meter-content" style="padding: 15px;">
                        <div style="font-size: 18px; font-weight: bold; color: #333;">
                            0.00 ${unit}
                        </div>
                        <div style="font-size: 12px; color: #999; margin-top: 8px;">
                            量程: ${range}
                        </div>
                    </div>
                `;

                console.log(`✅ ${type}组件创建完成: ${identifier}`);
                return meterDiv;
            }

            /**
             * 初始化所有仪表的拖拽功能
             */
            function initAllMeterDragging() {
                const meterComponents = document.querySelectorAll('.meter-component');
                meterComponents.forEach(component => {
                    const header = component.querySelector('.meter-header');
                    if (header) {
                        makeDraggable(component, header);
                    }
                });
                console.log(`✅ 已为${meterComponents.length}个仪表组件初始化拖拽功能`);
            }



            /**
             * 使元素可拖拽
             */
            function makeDraggable(element, handle) {
                let isDragging = false;
                let startX, startY, initialX = 0, initialY = 0;

                handle.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    startX = e.clientX - initialX;
                    startY = e.clientY - initialY;
                    e.preventDefault();
                });

                document.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        e.preventDefault();
                        initialX = e.clientX - startX;
                        initialY = e.clientY - startY;
                        element.style.transform = `translate(${initialX}px, ${initialY}px)`;
                    }
                });

                document.addEventListener('mouseup', () => {
                    isDragging = false;
                });
            }

            // 初始化电阻值
            loadVariableResistors();
			// 🔧 loadTestPoints现在在服务器数据加载完成后调用，避免重复调用
			// loadTestPoints();

			// 仪表组件已在DOMContentLoaded开始时调用
            // 旋钮点击和拖动事件          handleAdditionalKnobInteraction
            knob.addEventListener('mousedown', handleKnobInteraction);
            knob.addEventListener('mousewheel', mousewheel);
            // 确保事件绑定成功
            console.log('已为旋钮绑定mousedown事件');
			function mousewheel(event){
				//alert(event.wheelDelta);
				var xzfx=1;
				var dom=event.target;
				var befR=$(dom).attr("curR")
				var identifier=$(dom).attr("identifier")
				var curR=befR*1;
				if(event.wheelDelta>0){
					curR=curR*1+25;
				}else{
					curR=curR*1-25;
				}
				changRotWheel(identifier,befR,curR);
				$(dom).css('--rotation', curR + 'deg').attr("curR",curR).attr("befR",befR);
			}
            function handleKnobInteraction(e) {
                console.log('旋钮交互开始');
                e.preventDefault();
                e.stopPropagation();
                var befR=$(knob).attr("curR")
                // 获取旋钮的位置和尺寸
                const knobRect = knob.getBoundingClientRect();
                const knobCenterX = knobRect.left + knobRect.width / 2;
                const knobCenterY = knobRect.top + knobRect.height / 2;
                
                // 计算鼠标相对于旋钮中心的角度
                const mouseX = e.clientX - knobCenterX;
                const mouseY = e.clientY - knobCenterY;
                let angle = Math.atan2(mouseY, mouseX) * (180 / Math.PI);
                
                // 调整角度，使0度在顶部，并限制在-150到150度范围内
                angle = angle + 90; // 调整为顶部为0度
                if (angle > 180) angle -= 360; // 确保角度在-180到180度范围内
                $(knob).attr("befR",befR).attr("curR",angle)
                changRot($(knob).attr("identifier"),befR,angle);
                // 限制在-150到150度范围内
                if (angle < -150) angle = -150;
                if (angle > 150) angle = 150;
                
                console.log('点击位置角度:', angle);
                
                // 更新旋钮旋转角度
                knobRotation = angle;
                
                // 计算电阻值 (0-100kΩ)
                resistanceValue = Math.round(((knobRotation + 150) / 300) * 100);
                
                // 更新旋钮显示
                knob.style.setProperty('--rotation', knobRotation + 'deg');
                //knobValue.textContent = resistanceValue + 'kΩ';
                
                // 保存电阻值到localStorage
                saveResistanceValue(resistanceValue);
                
                // 设置拖动状态
                isKnobDragging = true;
                knobStartY = e.clientY;
                
                // 添加全局事件监听
                //document.addEventListener('mousemove', rotateKnob);
                document.addEventListener('mouseup', stopKnobRotation);
                
                // 添加视觉反馈
                knob.classList.add('dragging');
            }

            function rotateKnob(e) {
                if (!isKnobDragging) return;
                
                const deltaY = knobStartY - e.clientY;
                knobStartY = e.clientY;
                
                // 更新旋转角度，限制在-150到150度之间
                knobRotation += deltaY;
                knobRotation = Math.max(-150, Math.min(150, knobRotation));
                
                // 计算电阻值 (0-100kΩ)
                resistanceValue = Math.round(((knobRotation + 150) / 300) * 100);
                
                // 更新旋钮显示
                knob.style.setProperty('--rotation', knobRotation + 'deg');
                //knobValue.textContent = resistanceValue + 'kΩ';
                $(knob).attr("rotation",knobRotation)
                // 保存电阻值到localStorage
                saveResistanceValue(resistanceValue);
            }

            function stopKnobRotation() {
                if (!isKnobDragging) return;
                
                console.log('旋钮交互结束');
                
                isKnobDragging = false;
                document.removeEventListener('mousemove', rotateKnob);
                document.removeEventListener('mouseup', stopKnobRotation);
                
                // 移除视觉反馈
                knob.classList.remove('dragging');
            }

            // 保存电阻值到localStorage
            function saveResistanceValue(value) {
                try {
                    // 尝试获取现有数据
                    let resistorsData = localStorage.getItem('variable_resistors');
                    let resistors = [];
                    
                    if (resistorsData) {
                        resistors = JSON.parse(resistorsData);
                    }
                    
                    // 如果数组为空，添加一个新的电阻器
                    if (!resistors || !Array.isArray(resistors) || resistors.length === 0) {
                        resistors = [{ id: 'r1', value: value }];
                    } else {
                        // 更新第一个电阻器的值
                        resistors[0].value = value;
                    }
                    
                    // 保存回localStorage
                    localStorage.setItem('variable_resistors', JSON.stringify(resistors));
                    console.log('已保存电阻值:', value + 'kΩ');
                } catch (error) {
                    console.error('保存电阻值时出错:', error);
                }
            }

            // 处理额外旋钮的交互
            function handleAdditionalKnobInteraction(e, knobElement, valueElement, resistorId) {
                console.log(`额外旋钮${resistorId}交互开始`);
                e.preventDefault();
                e.stopPropagation();
                var befR=$(knobElement).attr("curR")
                // 获取旋钮的位置和尺寸
                const knobRect = knobElement.getBoundingClientRect();
                const knobCenterX = knobRect.left + knobRect.width / 2;
                const knobCenterY = knobRect.top + knobRect.height / 2;
                
                // 计算鼠标相对于旋钮中心的角度
                const mouseX = e.clientX - knobCenterX;
                const mouseY = e.clientY - knobCenterY;
                let angle = Math.atan2(mouseY, mouseX) * (180 / Math.PI);
                
                // 调整角度，使0度在顶部，并限制在-150到150度范围内
                angle = angle + 90; // 调整为顶部为0度
                if (angle > 180) angle -= 360; // 确保角度在-180到180度范围内
                $(knobElement).attr("befR",befR).attr("curR",angle)
                changRot($(knobElement).attr("identifier"),befR,angle);
                // 限制在-150到150度范围内
                if (angle < -150) angle = -150;
                if (angle > 150) angle = 150;
                
                console.log(`额外旋钮点击位置角度: ${angle}`);
                
                // 更新旋钮旋转角度
                let currentRotation = angle;
                
                // 计算电阻值 (0-100kΩ)
                const resistanceValue = Math.round(((currentRotation + 150) / 300) * 100);
                
                // 更新旋钮显示
                knobElement.style.setProperty('--rotation', currentRotation + 'deg');
                //valueElement.textContent = resistanceValue + 'kΩ';
                
                // 保存电阻值到localStorage
                saveResistanceValue(resistorId, resistanceValue);
                
                // 设置拖动状态
                let isDragging = true;
                let startY = e.clientY;
                
                // 添加视觉反馈
                knobElement.classList.add('dragging');
                
                // 拖动处理函数
                function rotateAdditionalKnob(e) {
                    if (!isDragging) return;
                    
                    const deltaY = startY - e.clientY;
                    startY = e.clientY;
                    
                    // 更新旋转角度，限制在-150到150度之间
                    currentRotation += deltaY;
                    currentRotation = Math.max(-150, Math.min(150, currentRotation));
                    
                    // 计算电阻值 (0-100kΩ)
                    const resistanceValue = Math.round(((currentRotation + 150) / 300) * 100);
                    
                    // 更新旋钮显示
                    knobElement.style.setProperty('--rotation', currentRotation + 'deg');
                    //valueElement.textContent = resistanceValue + 'kΩ';
                    
                    // 保存电阻值到localStorage
                    saveResistanceValue(resistorId, resistanceValue);
                    //
                    var identifier=$(knobElement).attr("identifier")
                    var knobrotation=$(knobElement).attr("rotation")
                    //changRot(identifier,knobrotation)
                }
                
                // 停止拖动处理函数
                function stopAdditionalKnobRotation() {
                    if (!isDragging) return;
                    
                    console.log(`额外旋钮${resistorId}交互结束`);
                    
                    isDragging = false;
                    document.removeEventListener('mousemove', rotateAdditionalKnob);
                    document.removeEventListener('mouseup', stopAdditionalKnobRotation);
                    
                    // 移除视觉反馈
                    knobElement.classList.remove('dragging');
                }
                
                // 添加全局事件监听
                //document.addEventListener('mousemove', rotateAdditionalKnob);
                document.addEventListener('mouseup', stopAdditionalKnobRotation);
            }
        });
        
        // 从localStorage加载图片
        function loadImageFromLocalStorage() {
            try {
                // 尝试从两个可能的键名中加载图片数据
                let imageData = localStorage.getItem('temp_circuit_png');
                
                // 如果第一个键名没有数据，尝试第二个键名
                if (!imageData) {
                    imageData = localStorage.getItem('circuit_image');
                    console.log('尝试从circuit_image加载图片');
                }
                
                // 也尝试从sessionStorage中加载
                if (!imageData) {
                    imageData = sessionStorage.getItem('temp_circuit_png');
                    if (!imageData) {
                        imageData = sessionStorage.getItem('circuit_image');
                    }
                    console.log('尝试从sessionStorage加载图片');
                }
                
                // 测试：打印测试点数据
//              const testPointsLocalStorage = localStorage.getItem('circuit_test_points');
//              const testPointsSessionStorage = sessionStorage.getItem('circuit_test_points');
//              
//              if (testPointsLocalStorage) {
//                  console.log('从localStorage成功获取测试点数据:', testPointsLocalStorage);
//              }
//              
//              if (testPointsSessionStorage) {
//                  console.log('从sessionStorage成功获取测试点数据:', testPointsSessionStorage);
//              }
//              
//              if (!testPointsLocalStorage && !testPointsSessionStorage) {
//                  console.log('未找到测试点数据（localStorage和sessionStorage均无）');
//              }
                
                const imgElement = document.getElementById('local-image');
                const noImageMessage = document.getElementById('no-image-message');
                
                if (imageData) {
                    // 设置图片src为base64数据
                    imgElement.src = imageData;
                    imgElement.style.display = 'block';
                    noImageMessage.style.display = 'none';
                    
                    console.log('从存储中加载图片成功');
                    
                    // 图片加载成功后添加测试点
                    imgElement.onload = function() {
                        console.log('图片加载完成，开始添加测试点');
                        
                        // 获取图片区域尺寸信息（调试用）
                        const imgRect = imgElement.getBoundingClientRect();
                        const containerRect = document.getElementById('image-container').getBoundingClientRect();
                        console.log(`图片尺寸: ${imgRect.width}x${imgRect.height}, 位置: (${imgRect.left}, ${imgRect.top})`);
                        console.log(`容器尺寸: ${containerRect.width}x${containerRect.height}, 位置: (${containerRect.left}, ${containerRect.top})`);
                        
                        // 加载测试点
                        loadTestPoints();
                    };
                    
                    // 图片加载错误处理
                    imgElement.onerror = function() {
                        console.error('图片加载失败');
                        imgElement.style.display = 'none';
                        noImageMessage.style.display = 'block';
                        
                        // 即使图片加载失败也添加测试点
                        loadTestPoints();
                    };
                } else {
                    // 没有找到图片数据
                    console.log('存储中未找到图片数据');
                    imgElement.style.display = 'none';
                    noImageMessage.style.display = 'block';
                    
                    // 即使没有图片，也添加测试点
                    //document.dispatchEvent(new CustomEvent('load-test-points'));
                }
            } catch (error) {
                console.error('加载图片时出错:', error);
                document.getElementById('local-image').style.display = 'none';
                document.getElementById('no-image-message').style.display = 'block';
                
                // 即使出错，也尝试添加测试点
                document.dispatchEvent(new CustomEvent('load-test-points'));
            }
        }
        
        // 打开远程桌面功能
        function openRemoteDesktop() {
            console.log('开始打开远程桌面');
            
            // 创建远程桌面容器
            const desktopContainer = document.createElement('div');
            desktopContainer.id = 'remoteDesktopContainer';
            desktopContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 80%;
                height: 80%;
                background: white;
                z-index: 9999;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                display: flex;
                flex-direction: column;
                overflow: hidden;
            `;

            // 添加标题栏 - 浅色调
            const titleBar = document.createElement('div');
            titleBar.style.cssText = `
                padding: 10px 15px;
                background: #f5f7fa;
                color: #333;
                border-bottom: 1px solid #e5e9f0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
                border-radius: 8px 8px 0 0;
            `;
            titleBar.innerHTML = `
                <span style="font-weight: bold; font-size: 14px;">远程桌面</span>
                <button onclick="closeRemoteDesktop()" style="
                    border: none;
                    background: none;
                    cursor: pointer;
                    font-size: 20px;
                    color: #666;
                    padding: 0;
                    line-height: 1;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: background-color 0.2s;
                ">×</button>
            `;

            // 为关闭按钮添加悬停效果
            const closeButton = titleBar.querySelector('button');
            closeButton.onmouseover = function() {
                this.style.backgroundColor = '#f0f0f0';
            };
            closeButton.onmouseout = function() {
                this.style.backgroundColor = 'transparent';
            };

            // 创建内容区域
            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                flex: 1;
                display: flex;
                overflow: hidden;
                position: relative;
                background: #fff;
            `;

            // 创建iframe容器
            const iframeContainer = document.createElement('div');
            iframeContainer.style.cssText = `
                width: 100%;
                height: 100%;
                position: relative;
            `;

            // 添加远程桌面iframe
            const iframe = document.createElement('iframe');
            iframe.id = 'remoteIframe';
            iframe.style.cssText = `
                width: 100%;
                height: 100%;
                border: none;
            `;
            
            // 设置加载提示 - 浅色调
            const loadingDiv = document.createElement('div');
            loadingDiv.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: #666;
                font-size: 14px;
                background: rgba(255, 255, 255, 0.9);
                padding: 15px 25px;
                border-radius: 6px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            `;
            loadingDiv.innerHTML = '正在连接远程桌面...';
            
            // 添加iframe加载事件监听
            iframe.onload = function() {
                loadingDiv.style.display = 'none';
            };
            
            iframe.onerror = function() {
                loadingDiv.innerHTML = '连接远程桌面失败，请稍后重试';
                loadingDiv.style.color = '#e74c3c';
            };

            // 设置iframe的src为用户提供的远程桌面URL
            //iframe.src = "https://sp.penevision.com:7443/guacamole/#/client/MwBjAG15c3Fs?username=tech&password=Tech1234&params=398909";
            //iframe.src = "https://sp.penevision.com:7443/guacamole/#/client/"+remoteWinParam;

            iframe.frameBorder = "0";

            // 组装DOM结构
            iframeContainer.appendChild(iframe);
            iframeContainer.appendChild(loadingDiv);
            contentDiv.appendChild(iframeContainer);
            desktopContainer.appendChild(titleBar);
            desktopContainer.appendChild(contentDiv);

            // 添加遮罩层 - 更轻的透明度
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                z-index: 9998;
                backdrop-filter: blur(2px);
            `;
            
            // 点击遮罩层关闭弹窗
            overlay.onclick = function(e) {
                if (e.target === overlay) {
                    closeRemoteDesktop();
                }
            };

            // 添加到页面
            document.body.appendChild(overlay);
            document.body.appendChild(desktopContainer);

            console.log('远程桌面弹窗已创建');
        }

        // 关闭远程桌面
        function closeRemoteDesktop() {
            console.log('关闭远程桌面');
            const container = document.getElementById('remoteDesktopContainer');
            const overlay = document.querySelector('div[style*="backdrop-filter: blur(2px)"]');
            
            if (container) {
                container.remove();
            }
            
            if (overlay) {
                overlay.remove();
            }
        }
        /**
         * 远程桌面参数
         * 远程设备参数：信号源参数,测试点参数对应指令
         * pdf 浏览参数
         */
        //var chann0=[];
        //var chann1=[];
        //var chann2=[];
        function initOrderParamip(){
        	$.ajax({
        		type:"get",
        		url:"/hrbust_lab_platform/remoteorder/initOrderParamip?id="+localStorage.getItem("remoteOrderid"),
        		async:true,
        		success:function(res){
        			if(res.status==200){
        				localStorage.setItem("stationIp",res.stationIp);
        				//localStorage.setItem("experimentName", res.projectName);
        				//localStorage.setItem("courseName", res.courseName);
        				sessionStorage.setItem("stationIp",res.stationIp);
        				// sessionStorage.setItem("experimentName",res.projectName);
        				 sessionStorage.setItem("courseName",res.courseName);
        				//document.cookie = 'courseName=' + res.courseName 
        				//document.cookie = 'experimentName='+res.projectName;
        				//document.cookie = 'projectName='+res.projectName;
        				document.cookie = "projectName=" + encodeURIComponent(res.projectName) + "; path=/; expires=" + new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
        				document.cookie = "courseName=" + encodeURIComponent(res.courseName) + "; path=/; expires=" + new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
        				remoteWinParam=res.remoteWinParam
        				ychEquIp=res.ychEquIp
        				var pdfurl="/staticresource/course_DS0001/projectziliao/20230615/1619302实验二晶体管共射极单管放大器.pdf"
        				if(res.pdfurl=='undefined'){
        					pdfurl=res.pdfurl		
        				}
        				var iframe = document.getElementById('yczm');
        			
        			//示波器
        		//	iframe.src="https://sp.penevision.com:7443/guacamole/#/client/"+remoteWinParam;
        				
        		//iframe.src="http://************:8998/control.html?device=device230";
        		
        		//	iframe.src="http://************/labcontrolls/control.html?device=device230";
        	
        	//不用， 被覆盖了
        	iframe.src="";
        		
        		http://************/labcontrolls/control.html?device=xxx
        				
		  // iframe.src=res.remoteWinParam1;
        				pdfurl = '/hrbust_lab_platform/html/html5-online-pdf-viewer/web/viewer.html?file=' + pdfurl;
        				
        				$("#neirongframe").attr("src",pdfurl)
        				$("#projectName").text(res.projectName)
        				
						var bsdlist=res.bsdlist
						$(bsdlist).each(function(i,obj){
							commObj[obj.code]={bval:obj.bval,channel:obj.channel}
						})
						endtime=res.endtime
						setTishi();

						// 🔧 服务器数据加载完成，立即重新渲染测点以确保使用正确的commObj数据
						loadTestPoints();
        				changSta(1)
        				setTimeout(changXXYsta(1,res.by2), 500);
        			}else{
        				if(res.substr("login.jsp")){
        					// 登录失效，静默处理
                            // layer.alert("登录失效")
        				}
        				
        			}
        			
        			
        		}
        	});
        }
        //电源状态控制
        function changSta(typecomm){
        	$.ajax({
        		type:"post",
        		url:"/hrbust_lab_platform/remoteStationInstrument/openlabPower",
        		async:true,
        		data:{
        			type: typecomm,
        			ip: ychEquIp
        			},
        		success:function(res){
        			if(res.status==200){
        				if(res.result.optResult){
        					//$(".btn").removeClass("open")
        					//$(obj).addClass("open")
        				}else{
        					
        					layer.msg("电源开失败,"+res.result.optMessage)
        				}
        			}
        		}
        	});
        }

        // 测试函数：验证分组匹配和测点下发
        function testCommObjAndTestPoints() {
            console.log('🧪 === 测试分组匹配和测点下发功能 ===');

            // 1. 检查本地存储数据
            console.log('📋 检查本地存储数据...');
            const testPointRelationsData = localStorage.getItem('testPointRelations') || sessionStorage.getItem('testPointRelations');
            const studentTestPointData = localStorage.getItem('test_point_relations') || sessionStorage.getItem('test_point_relations');

            console.log('模板测点关系数据存在:', !!testPointRelationsData);
            console.log('学生测点关系数据存在:', !!studentTestPointData);

            if (testPointRelationsData) {
                console.log('模板测点关系数据:', JSON.parse(testPointRelationsData));
            }
            if (studentTestPointData) {
                console.log('学生测点关系数据:', JSON.parse(studentTestPointData));
            }

            // 2. 重新构建commObj（包含分组匹配逻辑）
            console.log('🔧 重新构建commObj（分组匹配）...');
            const buildSuccess = buildCommObjFromTestPointRelations();

            // 3. 显示当前commObj状态

            // 4. 验证分组匹配结果
            if (Object.keys(commObj).length > 0) {
                console.log('📋 验证分组匹配结果:');
                Object.keys(commObj).forEach(identifier => {
                    const mapping = commObj[identifier];
                    console.log(`🔍 学生测点 ${identifier}:`);
                    console.log(`  - 匹配的模板分组: ${mapping.templateGroupId}`);
                    console.log(`  - 下发通道: ${mapping.channel}`);
                    console.log(`  - 硬件标识值: ${mapping.bval}`);
                    console.log(`  - 完整映射: ${JSON.stringify(mapping)}`);
                });
            }

            // 5. 模拟测点下发（测试新的通道逻辑）
            console.log('🚀 === 模拟分组匹配下发测试 ===');
            if (Object.keys(commObj).length > 0) {
                Object.keys(commObj).forEach(identifier => {
                    console.log(`🔌 模拟连接学生测点: ${identifier}`);

                    // 创建模拟的valueDisplay对象
                    const mockValueDisplay = $('<div>').attr({
                        'isyc': 'true',
                        'pvalue': '1',
                        'pidentifier': identifier
                    });

                    // 模拟探头连接到通道1，但实际下发会根据分组匹配结果决定
                    console.log(`📡 探头连接到通道1，但根据分组匹配:`);
                    console.log(`  - 实际下发通道: ${commObj[identifier].channel}`);
                    console.log(`  - 实际下发标识值: ${commObj[identifier].bval}`);

                    // 可以取消注释来实际测试
                    // changeCSD(1, mockValueDisplay[0]);
                });
            } else {
                console.log('❌ commObj为空，可能没有匹配的分组');
            }

            console.log('✅ === 测试完成 ===');

            // 返回测试结果
            return {
                buildSuccess: buildSuccess,
                commObjCount: Object.keys(commObj).length,
                hasTemplateData: !!testPointRelationsData,
                hasStudentData: !!studentTestPointData,
                commObjDetails: commObj
            };
        }

        // 简化的测试函数，用于快速验证
        function quickTestCommObj() {
            console.log('⚡ 快速测试commObj状态');
            buildCommObjFromTestPointRelations();
            console.log('commObj:', commObj);
            console.log('测点数量:', Object.keys(commObj).length);
            return commObj;
        }

        // 测试测点连接功能
        function testTestPointConnection() {
            console.log('🔌 === 测试测点连接功能 ===');

            // 1. 检查页面上的测点元素
            const testPointElements = document.querySelectorAll('.test-point');
            console.log(`📍 页面上的测点数量: ${testPointElements.length}`);

            if (testPointElements.length === 0) {
                console.log('❌ 页面上没有测点，请先加载测点');
                return;
            }

            // 2. 检查每个测点的绑定信息
            testPointElements.forEach((element, index) => {
                const identifier = element.getAttribute('data-identifier');
                const channel = element.getAttribute('data-channel');
                const bval = element.getAttribute('data-bval');
                const templateGroup = element.getAttribute('data-template-group');

                console.log(`🔍 测点${index + 1}:`);
                console.log(`  - 标识符: ${identifier}`);
                console.log(`  - 绑定通道: ${channel}`);
                console.log(`  - 硬件标识值: ${bval}`);
                console.log(`  - 模板分组: ${templateGroup || '无'}`);
                console.log(`  - CSS类: ${element.className}`);

                // 检查commObj中的对应信息
                if (commObj[identifier]) {
                    console.log(`  - commObj映射: ${JSON.stringify(commObj[identifier])}`);
                } else {
                    console.log(`  - ⚠️ commObj中未找到映射`);
                }
            });

            // 3. 测试探头连接
            if (testPointElements.length > 0) {
                const firstTestPoint = testPointElements[0];
                const identifier = firstTestPoint.getAttribute('data-identifier');
                console.log(`\n🧪 模拟探头连接到测点: ${identifier}`);

                // 模拟探头连接事件
                if (typeof connectProbeToTestPoint === 'function') {
                    console.log('✅ connectProbeToTestPoint函数存在，可以进行连接测试');
                } else {
                    console.log('⚠️ connectProbeToTestPoint函数不存在');
                }
            }

            console.log('✅ === 测点连接测试完成 ===');
        }

        //测试点切换 - 支持分组匹配逻辑
        function changeCSD(channel,obj){
        	var isyc=$(obj).attr("isyc")
        	var pvalue=$(obj).attr("pvalue")

        	if(isyc=="false"){
        		return;
        	}
        	if(!Number.isInteger(pvalue*1)){
        		pvalue=0;
        	}
        	var pidentifier=$(obj).attr("pidentifier")

        	console.log('🔌 测点切换调试信息:');
        	console.log('- 探头连接通道:', channel);
        	console.log('- 原始pvalue:', pvalue);
        	console.log('- 测点标识符:', pidentifier);
        	console.log('- commObj:', commObj);

        	// 🔧 新逻辑：根据分组匹配结果决定实际下发的通道和指令
        	let actualChannel = channel; // 默认使用探头连接的通道
        	let actualBval = pvalue; // 默认使用原始值

        	if(pidentifier&&pidentifier.length>0){
        		pidentifier=pidentifier.trim()
        		if(commObj[pidentifier]){
        		  // 🎯 关键逻辑：使用分组匹配的结果
        		  actualBval = commObj[pidentifier].bval;
        		  actualChannel = commObj[pidentifier].channel; // 使用模板分组ID作为通道

        		  console.log('✅ 从分组匹配获取的信息:');
        		  console.log('  - 模板分组ID (作为通道):', actualChannel);
        		  console.log('  - 硬件标识值 (bval):', actualBval);
        		  console.log('  - 模板分组ID:', commObj[pidentifier].templateGroupId);
        		  console.log('  - 学生测点ID:', commObj[pidentifier].studentTestPointId);
        		} else {
        		  console.log('⚠️ 警告：在commObj中未找到标识符:', pidentifier);
        		  console.log('  - 将使用默认值: 通道', channel, ', 标识值', pvalue);
        		}
        	}

        	// 更新实际使用的值
        	pvalue = actualBval;
        	channel = actualChannel;
        	
      		if(pidentifier.length>0&&pidentifier.indexOf("地")>0){
      			channel=0;
      		}
        	$.ajax({
        		type:"get",
        		url:"/hrbust_lab_platform/remoteduojo/csshiboqi",
        		async:true,
        		data:{
        			param2: channel,
        			param:pvalue,
        			ip: ychEquIp
        			},
        		success:function(res){
        			if(res.status==200){
        				//loadRemoteWin();
        				if(res.result.optResult){
        					//loadRemoteWin();	
        				}else{
        					layer.msg("测试点调整失败,"+res.result.optMessage)
        				}
        				//openRemoteDesktop();
        			}
        		}
        		
        	});
//      	$.ajax({
//      		type:"get",
//      		url:"/hrbust_lab_platform/remoteStationInstrument/openlabPower",
//      		async:true,
//      		data:{type: 1,ip: '*************'},//远程开
//      		success:function(res){
//      			if(res.status==200){
//      				var pidentifier=$(obj).attr(pidentifier)
//      				var pvalue=$(obj).attr(pvalue)
//      				if(pidentifier.constant("地")){
//      					channel=0;
//      				}
//      				
//      			}
//      		}
//      	});
        }
        //滑动电阻旋转
        function changRot(identifier,befR,curR){
        	var xf=16;
        	var dwqh=1;
        	if(befR<0){
        		befR=360-Math.abs(befR)
        	}
        	if(curR<0){
        		curR=360-Math.abs(curR)
        	}
        	var rotCha=curR-befR;
        	if(rotCha>0&&rotCha<180){
        		xf=17
        	}else if(rotCha>=180){
        		rotCha=360-rotCha
        		xf=18
        	}else if(rotCha<=0&&rotCha>-180){
        		xf=18
        		rotCha=Math.abs(rotCha)
        	}else{
        		xf=17
        		rotCha=rotCha+360
        	}
        	var rnum=Math.floor(rotCha/50)
//      	if(identifier&&identifier.length>1){
//      		dwqh=identifier.substr(1);
//      	}
        	if(identifier&&identifier.length>0){
        		identifier=identifier.trim()
        		if(commObj[identifier]){
        		  dwqh=commObj[identifier].bval;
        		}
        	}
        	
        	for(var i=0;i<=rnum;i++){
        		$.ajax({
	        		type:"get",
	        		url:"/hrbust_lab_platform/remoteStationInstrument/getdianwieqi",
	        		async:true,
	        		data:{
	        			'ip':ychEquIp,
	        			'banhao':'0',
	        			'dushu':'990',
	        			'canshuxz':xf,
	        			'dianweiqi':dwqh,
	        		},
	        		success:function(){
	        			if(!res.result.optResult){
        					layer.msg("电位器调整失败,"+res.result.optMessage)
        				}
	        		}
	        	});
        	}
        	
        	
        }
        function changRotWheel(identifier,befR,curR){
        	var xf=16;
        	var dwqh=1;
        	if(befR>curR){
        		xf=16
        	}else{
        		xf=17
        	}
        	
        	if(identifier&&identifier.length>0){
        		identifier=identifier.trim()
        		if(commObj[identifier]){
        		  dwqh=commObj[identifier].bval;
        		}
        	}
        	$.ajax({
        		type:"get",
        		url:"/hrbust_lab_platform/remoteStationInstrument/getdianwieqi",
        		async:true,
        		data:{
        			'ip':ychEquIp,
        			'banhao':'0',
        			'dushu':'390',
        			'canshuxz':xf,
        			'dianweiqi':dwqh,
        		},
        		success:function(){
        			if(!res.result.optResult){
    					layer.msg("电位器调整失败,"+res.result.optMessage)
    				}
        		}
        	});
        	
        }
        //示波器切换
        function optionsbq(otype){
        	
        	if(otype==1){
        		$(".sbqlin2").hide();
        		//$(".sbqlin0").show();
        	}else if(otype==2){
        		//$(".sbqlin0").hide();
        		$(".sbqlin2").show();
        	}
        	initProbes();//初始化测试点
        }
         //加载远程桌面
         var curIfm=1;
        function loadRemoteWin(){
        	curIfm=2;
        	$("#iframe-container").show()
        	$("#image-container").hide()
        	 //if (iframe.contentWindow) {
		    //    iframe.contentWindow.location.reload(true); // true 表示强制从服务器重新加载（忽略缓存）
		    //}
		    
		    
		    
		      // 设置iframe的src为新的远程控制页面
    $("#yczm").attr("src", "/labcontrolls/control.html?device=device230");

		    
		 //   console.log("http://************:8998/control.html?device=device230");
		    
		    $(".loadRemoteWin").hide()
        	$(".loaddlt").show();
        	$(".oscilloscope").hide();
        	$(".upo").hide();
        	
        	$("svg").hide()
        }
        //加载电路图
        function loaddlt(){
        	curIfm=1;
        	$("#iframe-container").hide()
        	$("#image-container").show()
        	$(".loadRemoteWin").show()
        	$(".loaddlt").hide()
        	$("svg").show()
        	$(".oscilloscope").show();
        	$(".upo").hide();
        	$(".sg").hide();
        }
         //信息源开关
         //https://sp.penevision.com:6443/hrbust_lab_platform/remoteduojo/kaiguanliang
         function changXXYsta(comm,sort){
         		console.info("信息源开关changXXYsta")

    		$.ajax({
        		type:"post",
        		url:"/hrbust_lab_platform/remoteduojo/kaiguanliang",
        		async:true,
        		data:{
        			'duojiip':ychEquIp,
        			'sort':sort,
        			'param': comm,
				},
				success: function(res) {
					console.info(res)
					if(!res.result.optResult) {
						layer.msg("信息源开失败"+res.result.optMessage)
					}
				}
			});
		}
        function switchsCon(bs,obj){
        	var bsstr=bs.trim();
        	//cursta="close"
        	var swComm=1;//标识值
        	var newSta=1;//状态1开,2关
        	var cursta=$(obj).attr("cursta")
			var newStaStr="开";//状态1开,2关
        	if(cursta=="0"){
        		newSta=1
        		newStaStr="关"
        	}else{
        		newSta=0;
        		newStaStr="开"
        	}
        	
        	if(bsstr.length>0){
        		if(commObj[bsstr]){
        			swComm=commObj[bsstr]
        		}
        	}
        	//$(obj).attr("cursta",newSta).text(bsstr+newStaStr)
         	$.ajax({
        		type:"post",
        		url:"/hrbust_lab_platform/remoteduojo/kaiguanliang",
        		async:true,
        		data:{
        			'duojiip':ychEquIp,
        			'sort':swComm,
        			'param': newSta,
				},
				success: function(res) {
					console.info(res)
					if(!res.result.optResult) {
						layer.msg("信息源开失败"+res.result.optMessage)
					}else{
						$(obj).attr("cursta",newSta).text(bsstr+newStaStr)
					}
				}
			});
         }
        function switchsCon2(bs,bs2,obj){
        	var bsstr=bs.trim();
        	//cursta="close"
        	var swComm=1;//标识值
        	var newSta=1;//状态1开,2关
        	var cursta=$(obj).attr("cursta")
			var newStaStr="开";//状态1开,2关
			
			var xgs2=$("#"+bs2)
			var newStaStr="开";//状态1开,2关
        	if(cursta=="0"){
        		newSta=1
        		newStaStr="关"
        		xgs2.attr("cursta",0)
        		xgs2.text(bs2+"开")
        	}else{
        		newSta=0;
        		newStaStr="开"
        	}
        	
        	if(bsstr.length>0){
        		if(commObj[bsstr]){
        			swComm=commObj[bsstr]
        		}
        	}
        	//$(obj).attr("cursta",newSta).text(bsstr+newStaStr)
         	$.ajax({
        		type:"post",
        		url:"/hrbust_lab_platform/remoteduojo/kaiguanliang",
        		async:true,
        		data:{
        			'duojiip':ychEquIp,
        			'sort':swComm,
        			'param': newSta,
				},
				success: function(res) {
					console.info(res)
					if(!res.result.optResult) {
						layer.msg("信息源开失败"+res.result.optMessage)
					}else{
						$(obj).attr("cursta",newSta).text(bsstr+newStaStr)
					}
				}
			});
         }
var BASE_URL='/';
function createmydir() {
	// 定义要发送的参数

	const cookieString = document.cookie; // 获取浏览器中的 cookies 字符串

	// 使用正则表达式匹配第一个 'userId' 的值
	const match = cookieString.match(/(?:^|;\s*)userId=([^;]*)/);
	var targetIp = localStorage.getItem('stationIp'); // 获取存储的目标IP地址
	var stu_id = match[1]; // 示例用户名
	var projectName = decodeURIComponent(document.cookie.replace(/(?:(?:^|.*;\s*)projectName\s*=\s*([^;]*).*$)|^.*$/, '$1'));
	//  var projectPath ='F:\\Keil C51-EL-EMCU-IV\\实验指导书';

	// 通过 GET 请求获取 JSON 数据
	fetch(BASE_URL + 'remote_server/shiyan?target=' + encodeURIComponent(targetIp))
		.then(response => response.json()) // 解析 JSON 文件
		.then(data => {

			let projectPath = data.hasOwnProperty(projectName) ? data[projectName] : 'F:\\Keil C51-EL-EMCU-IV\\实验指导书'; // 如果没有找到，使用默认路径

			// 提取路径中的最后一个目录层级
			var lastDirectory = projectPath.split(/[\\\/]/).pop(); // 使用正则匹配 \\ 或 /
			console.log('最后一个目录层级:', lastDirectory); // 输出最后一个目录层级

			// 将最后一个目录层级存储到 cookie 中
			document.cookie = 'lastDirectory=' + lastDirectory + '; path=/; expires=' + new Date(Date.now() + 2 * 60 * 60 * 1000).toUTCString();

			// 构建 URL，包含查询参数
			var url = BASE_URL + '/remote_server/checkDir?stu_id=' + encodeURIComponent(stu_id) + '&dir=' + encodeURIComponent(lastDirectory) + '&target=' + encodeURIComponent(targetIp);

			//    -
			// 发送请求检查历史环境是否存在
			fetch(url)
				.then(response => response.json())
				.then(data => {
					if(data === true) {
						// 如果历史环境存在，显示弹框询问是否创建新环境
						showDialog(() => createHistoryEnvironment(stu_id, lastDirectory), () => {
							createNewEnvironment(stu_id, projectPath);
						});
					} else {
						// 如果历史环境不存在，直接创建新环境
						createNewEnvironment(stu_id, projectPath);
					}
				})
				.catch(error => {
					console.error('检查历史环境失败:', error);
					// 静默处理错误
				});
		})
		.catch(error => {
			console.error('获取 JSON 数据失败,创建一个空环境', error);
			createNewEnvironment(stu_id, projectPath);
		});

}
// 创建新环境
function createNewEnvironment(stu_id, projectPath) {
        var url = BASE_URL+'remote_server/createByUserLoginName';

		
        // 构建 POST 请求的请求体
        var body = new URLSearchParams();
        body.append('stu_id', stu_id);
        body.append('dir', projectPath);
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
			body:body.toString()
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应失败');
            }
            return response.text();
        })
        .then(data => {
            console.log('成功:', data);
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
    }
var editablesta=0






// 主页面监听编辑器iframe的截图请求
window.addEventListener('message', async function(e){
    // 安全性可加origin判断，略
    if(e.data && e.data.type === 'screenshot'){
    	
    	// alert("1111");
    	
        // 获取波形iframe里的canvas
        var yczmFrame = document.getElementById('yczm');
        if(!yczmFrame) return;

        try {
         /*   var canvas = yczmFrame.contentWindow.document.getElementById('pic');
            if(!canvas){
                alert("波形页面还没加载好，canvas不存在！");
                return;
            }
            // toDataURL转base64
            var base64 = canvas.toDataURL('image/png');
            
            */
            
                // 直接调用波形iframe里的截图API！
            var base64 = yczmFrame.contentWindow.capturePicScreenshot();
            
            console.log(base64);
            
            // 把base64通过postMessage发给编辑iframe
            var editableFrame = document.getElementById('editable');
            editableFrame.contentWindow.postMessage({ type: 'screenshotResult', base64 }, '*');
        } catch(e){
            alert("截图失败！同源策略或canvas未准备好");
            console.error(e);
        }
    }
    
  else if(e.data && e.data.type === 'video'){
  	
  	
  	// alert("2222");
  	
  	
  	
  	
  	
        var yczmFrame = document.getElementById('yczm');
        var editableFrame = document.getElementById('editable');
        try {
        	
        	
        	
     var base64 = await yczmFrame.contentWindow.startRecord(e.data.duration || 10);
     
         console.log(base64);
// 直接转发base64，无需FileReader
editableFrame.contentWindow.postMessage({ type: 'videoResult', base64 }, '*');



        } catch(err) {
            alert('录制视频失败');
        }
    }
   
   
   
   
   
   
   
    
});
















function toggleFloatingBox() {
	if(editablesta==0){
		editablesta=1;
		$("#editable").show()
		$("#neirongframe").hide()
		$("#tat").text("实验记录")
		$("#togbtn").text("实验指导书")
	}else{
		editablesta=0
		$("#editable").hide()
		$("#neirongframe").show()
		$("#tat").text("实验指导书")
		$("#togbtn").text("实验记录")
	}
	
}
var endtime=0;
function setTishi() {
	var startTime = new Date().getTime()
	endtime = endtime*1;
	var shicha=endtime-startTime;
	if(shicha>0){
		var sym=shicha % 3600000;
		var sys=shicha % 60000;
		var h = Math.floor(shicha / 3600000);
		var m = Math.floor(sym / 60000);
		var s = Math.floor(sys / 1000);
		if(h < 10) {
			h = "0" + h;
		}
		if(m < 10) {
			m = "0" + m;
		}
		if(s < 10) {
			s = "0" + s;
		}
		$("#timeshow").html('<span>剩余时间:&nbsp;</span><span>' + h + '</span>:<span>' + m + '</span>:<span>' + s + '</span>')
		setTimeout(setTishi, 1000)
	}else{
		$("#timeshow").html('<span>剩余时间:&nbsp;</span><span>00:00:00</span>')
		// 远程实验已结束，静默处理
		$("#yczm").attr("src", "");
		$("#iframe-container").hide();
		$("#image-container").show()
	}
	
}
/**
 * 数据初始化
 * 页面元素高宽初始化  事件提示开启-setTishi();
        			开关状态改变changSta(1)
        			信号源状态改变setTimeout(changXXYsta(1,res.by2), 500);
 * 1s后加载测试点
 * 1s后加载开关
 * 键盘监听事件开启
 * 全屏事件监听开启
 */
document.addEventListener('DOMContentLoaded', function() {
	var clientHeight = parseInt(window.screen.availHeight);
	var clientWidth = parseInt(window.screen.width);
	var newhei=clientHeight * 0.71
	//rightpar leftpar
	$("#leftpar").css("width",clientWidth*0.36)
	$("#rightpar").css("width",clientWidth*0.63)
	$("#leftpar").css("height",newhei+110)
	$("#rightpar").css("height","100%")
	$("#iframe-container").attr("width", clientWidth * 0.6 + "px");
	$("#iframe-container").attr("height",clientHeight-100+"px");
	$("#yczm").css("height",(window.innerHeight-100)+"px")
	
	$("#image-container").css("height", window.innerHeight-100+"px");
	$("#image-container").attr("width", clientWidth * 0.6 + "px");
	//$("#local-image").css("height",(window.innerHeight-105)+"px")
	//$("#local-image").css("width",(clientWidth * 0.62 + "px"))
	//yczm
    // 🔧 重新设计渲染流程：确保数据加载完成后再渲染测点
    console.log('🚀 页面初始化开始...');
    initOrderParamip(); // 这会在success回调中调用loadTestPoints()
    //$("#yczm").css("height",(window.innerHeight-70)+"px")
	//const xhyDiv = document.getElementById('xhyDiv');
//	const iframe = document.getElementById('yczm');
	// 添加键盘事件监听，使用事件捕获模式确保iframe优先接收键盘事件
//  document.addEventListener('keydown', function(e) {
//      // 如果iframe已加载且有焦点
//      if (iframe.contentWindow && document.activeElement === iframe) {
//          try {
//              // 尝试直接调用Guacamole的键盘处理函数
//              if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
//                  iframe.contentWindow.guac.keyboard.press(e.keyCode);
//                  // 阻止事件冒泡以避免重复处理
//                  e.preventDefault();
//                  e.stopPropagation();
//              }
//          } catch (err) {
//              console.log('无法直接调用Guacamole键盘处理:', err);
//          }
//      } else {
//          // 如果iframe没有焦点，将焦点给iframe
//          iframe.focus();
//          if (iframe.contentWindow) {
//              iframe.contentWindow.focus();
//          }
//      }
//  }, true); // true表示在捕获阶段处理事件
//  
//  document.addEventListener('keyup', function(e) {
//      if (iframe.contentWindow && document.activeElement === iframe) {
//          try {
//              if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
//                  iframe.contentWindow.guac.keyboard.release(e.keyCode);
//                  // 阻止事件冒泡以避免重复处理
//                  e.preventDefault();
//                  e.stopPropagation();
//              }
//          } catch (err) {
//              console.log('无法直接调用Guacamole键盘处理:', err);
//          }
//      }
//  }, true); // true表示在捕获阶段处理事件
//  
//  // 添加输入事件监听
//  document.addEventListener('input', function(e) {
//      if (iframe.contentWindow) {
//          try {
//              if (iframe.contentWindow.guac && iframe.contentWindow.guac.keyboard) {
//                  iframe.contentWindow.guac.keyboard.type(e.data);
//              }
//          } catch (err) {
//              console.log('无法直接调用Guacamole输入处理:', err);
//          }
//      }
//  });
    // 添加点击事件，确保iframe获得焦点
//  iframe.addEventListener('click', function() {
//      this.focus();
//      if (this.contentWindow) {
//          this.contentWindow.focus();
//      }
//  }); 
    const rightpar = document.getElementById('rightpar');
	rightpar.addEventListener('fullscreenchange', (event) => {
	  if (document.fullscreenElement) {
	    // 进入了全屏模式
	    isFullScreen=2;
	    $("#yczm").css("height",(window.screen.availHeight-50)+"px")
	    $("#image-container").css("height", window.screen.availHeight-50+"px");
	    // 在这里执行进入全屏后的操作，例如隐藏其他UI元素
	  } else {
	    // 退出了全屏模式
	    isFullScreen=1;
	    $("#yczm").css("height",(window.innerHeight-100)+"px")
	    $("#image-container").css("height", window.innerHeight-100+"px");
	    // 在这里执行退出全屏后的操作，例如恢复隐藏的UI元素
	  }
	});
});
//应用切换
function changWin(str){
	if(str=='SG'){
		$(".upo").show()
		$(".sg").hide()
	}else{
		$(".upo").hide()
		$(".sg").show()
	}
	var stationIp=localStorage.getItem('stationIp');
	$.ajax({
		type:"post",
		url:"/remote_server/window/upo?target="+stationIp+"&keyword="+str,
//		data:{
//			'target':stationIp,
//			'keyword':str,
//		},
		success: function(res) {
			console.info(res)
		}
	});
}
var isFullScreen=1;
function launchFullscreen() {
	var obj=$("#rightpar").get(0)
	if(isFullScreen==2){
		const exitFullscreen = document.exitFullscreen || document.mozCancelFullScreen || document.webkitExitFullscreen || document.msExitFullscreen;
		//obj.exitFullscreen()
		if (exitFullscreen) {
		    exitFullscreen.call(document);
		}
	}
	if(obj.requestFullscreen) {
		obj.requestFullscreen()
	} else if(obj.mozRequestFullScreen) {
		obj.webkitRequestFullScreen()
	} else if(obj.msRequestFullscreen) {
		obj.msRequestFullscreen()
	} else if(obj.webkitRequestFullscreen) {
		obj.mozRequestFullScreen()
	}
}


    </script>
</body>
</html>