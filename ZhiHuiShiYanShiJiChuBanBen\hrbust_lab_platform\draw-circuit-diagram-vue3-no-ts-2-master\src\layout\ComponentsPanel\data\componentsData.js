// componentsData.js

import ammeterIcon from './icons/ammeter.svg' // 电流表
import polarizedCapacitorIcon from './icons/polarizedCapacitor.svg' // 有极性电容
import nonPolarizedCapacitorIcon from './icons/nonPolarizedCapacitor.svg' // 无极性电容
import diodeIcon from './icons/diode.svg' // 二极管
import groundIcon from './icons/ground.svg' // 接地
import inductorIcon from './icons/inductor.svg' // 电感
import oscilloscopeIcon from './icons/oscilloscope.svg' // 示波器
import resistorIcon from './icons/resistor.svg' // 电阻
import switchIcon from './icons/switch.svg' // 单刀单掷开关
import transistorNPNIcon from './icons/Transistor_NPN.svg' // NPN 三极管
import transistorPNPIcon from './icons/Transistor_PNP.svg' // PNP 三极管
import voltage_sourceIcon from './icons/voltage_source.svg' // 电压源
import voltmeterIcon from './icons/voltmeter.svg' // 电压表
import rheostatIcon from './icons/rheostat.svg' // 可变电阻
import ZenerDiodeIcon from './icons/ZenerDiode.svg' // 稳压管
import DualZenerDiodeIcon from './icons/DualZenerDiode.svg' // 双向稳压管
import FuseIcon from './icons/Fuse.svg' // 保险管
import LM78xxIcon from './icons/LM78xx.svg' // LM78xx 系列芯片
import Op_AmpIcon from './icons/Op_Amp.svg' // 运算放大器
import testPointIcon from './icons/testPoint.svg' // 测点
import SPDTSwitchIcon from './icons/SPDTSwitch.svg' // 单刀双掷开关
import VariableCapacitorIcon from './icons/VariableCapacitor.svg' // 可变电容
//import singalIcon from './icons/signal.svg' // 信号源
//var singalIcon='<svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="40" height="40"><path d="M512 107.8c-192.9 0-349.9 157-349.9 349.9 0 63.1 17 124.9 49.3 178.9 31.3 52.4 75.9 95.9 128.9 125.9 12 6.8 27.3 2.6 34.1-9.5 6.8-12 2.6-27.3-9.5-34.1-45.5-25.7-83.8-63.1-110.6-108-27.6-46.2-42.2-99.2-42.2-153.3 0-165.4 134.5-299.9 299.9-299.9s299.9 134.5 299.9 299.9c0 54-14.6 107-42.2 153.3-26.8 44.9-65.1 82.3-110.6 108-12 6.8-16.3 22-9.5 34.1 4.6 8.1 13.1 12.7 21.8 12.7 4.2 0 8.4-1 12.3-3.2 53.1-30 97.7-73.5 128.9-125.9 32.2-54 49.3-115.8 49.3-178.9 0-193-157-349.9-349.9-349.9z"></path></svg>'
import {
  resistorConnectionPoints,
  nonPolarizedCapacitorConnectionPoints,
  polarizedCapacitorConnectionPoints,
  inductorConnectionPoints,
  diodeConnectionPoints,
  voltageSourceConnectionPoints,
  groundConnectionPoints,
  switchConnectionPoints,
  oscilloscopeConnectionPoints,
  ammeterConnectionPoints,
  voltmeterConnectionPoints,
  rheostatConnectionPoints,
  Op_AmpConnectionPoints,
  FuseConnectionPoints,
  LM78xxConnectionPoints,
  VariableCapacitorConnectionPoints,
  ZenerDiodeConnectionPoints,
  DualZenerDiodeConnectionPoints,
  Transistor_NPNConnectionPoints,
  Transistor_PNPConnectionPoints,
  SPDTSwitchConnectionPoints,
  testPointConnectionPoints,
 // singal,
} from './connectionPointsData.js'

const HAS_POLARITY = {
  YES: true, // 有极性
  NO: false, // 无极性
}

export const componentsData = [
  {
    type: 'resistor',
    label: '电阻',
    symbol: 'R',
    defaultValue: 1,
    defaultUnit: 'KΩ',
    unitList: ['mΩ', 'Ω', 'KΩ', 'MΩ', 'GΩ'], // 添加的单位集合
    icon: resistorIcon,
    isPolar: HAS_POLARITY.NO, // 是否有极性
    connectionPoints: resistorConnectionPoints,
  },
  {
    type: 'rheostat',
    label: '可变电阻',
    symbol: 'R',
    defaultValue: 1,
    defaultUnit: 'KΩ',
    unitList: ['mΩ', 'Ω', 'KΩ', 'MΩ', 'GΩ'], // 添加的单位集合
    icon: rheostatIcon,
    isPolar: HAS_POLARITY.NO, // 是否有极性
    connectionPoints: rheostatConnectionPoints,
  },
  {
    type: 'nonPolarizedCapacitor',
    label: '无极性电容',
    symbol: 'C',
    defaultValue: 10,
    defaultUnit: 'µF',
    unitList: ['F', 'mF', 'µF', 'nF', 'pF'], // 添加的单位集合
    icon: nonPolarizedCapacitorIcon,
    isPolar: HAS_POLARITY.NO, // 是否有极性
    connectionPoints: nonPolarizedCapacitorConnectionPoints,
  },
  {
    type: 'polarizedCapacitor',
    label: '极性电容',
    symbol: 'C',
    defaultValue: 10,
    defaultUnit: 'µF',
    unitList: ['F', 'mF', 'µF', 'nF', 'pF'], // 添加的单位集合
    icon: polarizedCapacitorIcon,
    isPolar: HAS_POLARITY.YES, // 是否有极性
    connectionPoints: polarizedCapacitorConnectionPoints,
  },

  {
    type: 'transistor_NPN',
    label: 'NPN',
    symbol: 'Q',
    defaultValue: '9013',
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: transistorNPNIcon,
    connectionPoints: Transistor_NPNConnectionPoints,
  },

  {
    type: 'transistor_PNP',
    label: 'PNP',
    symbol: 'Q',
    defaultValue: '9012',
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: transistorPNPIcon,
    connectionPoints: Transistor_PNPConnectionPoints,
  },

  {
    type: 'inductor',
    label: '电感',
    symbol: 'L',
    defaultValue: 10,
    defaultUnit: 'µH',
    unitList: ['nH', 'µH', 'mH', 'H', 'kH'], // 添加的单位集合
    icon: inductorIcon,
    connectionPoints: inductorConnectionPoints,
  },
  {
    type: 'diode',
    label: '二极管',
    symbol: 'D',
    defaultValue: null,
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: diodeIcon,
    connectionPoints: diodeConnectionPoints,
  },

  {
    type: 'ground',
    label: '接地',
    symbol: 'GND',
    defaultValue: null,
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: groundIcon,
    connectionPoints: groundConnectionPoints,
  },

  {
    type: 'voltage_source',
    label: '直流电压源',
    symbol: 'Ucc',
    defaultValue: '+10',
    defaultUnit: 'V',
    unitList: ['µV', 'mV', 'V', 'kV', 'MV'], // 添加的单位集合
    icon: voltage_sourceIcon,
    isPolar: HAS_POLARITY.YES, // 有极性
    connectionPoints: voltageSourceConnectionPoints,
  },

  {
    type: 'switch',
    label: '单刀单掷开关',
    symbol: 'S',
    defaultValue: null,
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: switchIcon,
    connectionPoints: switchConnectionPoints,
  },

  {
    type: 'oscilloscope',
    label: '示波器',
    symbol: '示波器 ',
    defaultValue: null,
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: oscilloscopeIcon,
    connectionPoints: oscilloscopeConnectionPoints,
  },

  {
    type: 'ammeter',
    label: '电流表',
    symbol: 'A',
    defaultValue: null,
    defaultUnit: '',
    unitList: ['µA', 'mA', 'A', 'kA'], // 添加的单位集合
    icon: ammeterIcon,
    connectionPoints: ammeterConnectionPoints,
  },

  {
    type: 'voltmeter',
    label: '电压表',
    symbol: 'V',
    defaultValue: null,
    defaultUnit: '',
    unitList: ['µV', 'mV', 'V', 'kV', 'MV'], // 添加的单位集合
    icon: voltmeterIcon,
    connectionPoints: voltmeterConnectionPoints,
  },

  {
    type: 'ZenerDiode',
    label: '单向稳压管',
    symbol: 'D',
    defaultValue: 2.5,
    defaultUnit: 'V',
    unitList: ['V', 'mV'], // 添加的单位集合
    icon: ZenerDiodeIcon,
    connectionPoints: ZenerDiodeConnectionPoints,
  },

  {
    type: 'DualZenerDiode',
    label: '双向稳压管',
    symbol: 'D',
    defaultValue: 2.5,
    defaultUnit: 'V',
    unitList: ['V', 'mV'], // 添加的单位集合
    icon: DualZenerDiodeIcon,
    connectionPoints: DualZenerDiodeConnectionPoints,
  },

  {
    type: 'Fuse',
    label: '保险管',
    symbol: 'F',
    defaultValue: 3,
    defaultUnit: 'A',
    unitList: ['A', 'mA'], // 添加的单位集合
    icon: FuseIcon,
    connectionPoints: FuseConnectionPoints,
  },

  {
    type: 'LM78xx',
    label: '三端稳压集成电路',
    symbol: 'U',
    defaultValue: 'LM7805',
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: LM78xxIcon,
    connectionPoints: LM78xxConnectionPoints,
  },

  {
    type: 'Op_Amp',
    label: '运算放大器',
    symbol: 'U',
    defaultValue: 'LM741',
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: Op_AmpIcon,
    connectionPoints: Op_AmpConnectionPoints,
  },

  {
    type: 'SPDTSwitch',
    label: '单刀双掷开关',
    symbol: 'S',
    defaultValue: null,
    defaultUnit: '',
    unitList: [], // 添加的单位集合
    icon: SPDTSwitchIcon,
    connectionPoints: SPDTSwitchConnectionPoints,
  },

  {
    type: 'VariableCapacitor',
    label: '可变电容',
    symbol: 'C',
    defaultValue: 10,
    defaultUnit: 'µF',
    unitList: ['F', 'mF', 'µF', 'nF', 'pF'], // 添加的单位集合
    icon: VariableCapacitorIcon,
    connectionPoints: VariableCapacitorConnectionPoints,
  },

  {
    type: 'testPoint',
    label: '测点',
    symbol: '',
    defaultValue: '',
    defaultUnit: '',
    unitList: [],
    icon: testPointIcon,
    isPolar: HAS_POLARITY.NO,
    connectionPoints: testPointConnectionPoints,
    isTestPoint: true,
    hideLabel: true,
    noValueEdit: true,
  },
//{
//  type: 'signal',
//  label: '信号源',
//  symbol: '',
//  defaultValue: '',
//  defaultUnit: '',
//  icon: singalIcon,
//  isPolar: HAS_POLARITY.NO,
//  connectionPoints: singal,
// 
//},
]
