<template>
  <span>交互式电路设计器</span>
</template>

<script setup name="Header"></script>

<style scoped>
  span {
    font-size: 25px; /* 字体大小 */
    color: #333; /* 文本颜色 */
    font-weight: bold; /* 加粗 */
    font-family: 'Arial', sans-serif; /* 字体族 */
    text-align: center; /* 居中对齐 */
    display: inline-block; /* 行内块状元素，适合添加内边距 */
    padding: 5px 10px; /* 内边距 */
    border: 1px solid #ccc; /* 边框 */
    border-radius: 5px; /* 圆角 */
    background-color: #f9f9f9; /* 背景色 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影 */
  }
</style>
