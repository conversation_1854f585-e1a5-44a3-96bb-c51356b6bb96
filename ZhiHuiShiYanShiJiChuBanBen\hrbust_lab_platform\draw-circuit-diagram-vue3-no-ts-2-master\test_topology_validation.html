<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拓扑校验功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .result {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>电路拓扑校验功能测试</h1>
        <p>此页面用于测试改进后的连接关系校验功能，特别是基于组件类型的拓扑比较。</p>

        <div class="test-section">
            <div class="test-title">测试1: 组件类型规范化</div>
            <button onclick="testNormalizeComponentType()">运行测试</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2: 连接描述解析</div>
            <button onclick="testExtractComponentType()">运行测试</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3: 拓扑结构构建</div>
            <button onclick="testBuildTopology()">运行测试</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试4: 拓扑结构比较</div>
            <button onclick="testCompareTopology()">运行测试</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试5: 完整校验流程</div>
            <button onclick="testFullValidation()">运行测试</button>
            <div id="test5-result"></div>
        </div>
    </div>

    <script>
        // 模拟改进后的校验函数
        function normalizeComponentType(type) {
            if (!type) return type

            // 保持电容的极性区分
            if (type.includes('极性电容')) return '极性电容'
            if (type.includes('无极性电容')) return '无极性电容'

            // 电阻类型规范化
            if (type.includes('电阻')) {
                if (type.includes('可变')) return '可变电阻'
                return '电阻'
            }

            // 示波器类型规范化
            if (type.includes('示波器')) return '示波器'

            // 测点类型规范化
            if (type.includes('测点')) return '测点'

            // 二极管类型规范化
            if (type.includes('二极管')) return '二极管'

            // 其他组件保持原样
            return type
        }

        function extractComponentType(connectionDesc) {
            if (!connectionDesc) return ''
            
            // 连接描述格式: "组件类型 - 组件ID - 参数 - 连接点 - 极性"
            const parts = connectionDesc.split(' - ')
            if (parts.length > 0) {
                return normalizeComponentType(parts[0].trim())
            }
            
            return ''
        }

        function buildTopology(connections) {
            const topology = {}
            
            connections.forEach(conn => {
                const fromType = extractComponentType(conn.from)
                const toType = extractComponentType(conn.to)
                
                // 初始化节点
                if (!topology[fromType]) topology[fromType] = new Set()
                if (!topology[toType]) topology[toType] = new Set()
                
                // 添加双向连接
                topology[fromType].add(toType)
                topology[toType].add(fromType)
            })
            
            // 将Set转换为Array以便比较
            Object.keys(topology).forEach(key => {
                topology[key] = Array.from(topology[key]).sort()
            })
            
            return topology
        }

        function compareTopology(topo1, topo2) {
            const keys1 = Object.keys(topo1).sort()
            const keys2 = Object.keys(topo2).sort()
            
            // 比较节点数量
            if (keys1.length !== keys2.length) {
                return false
            }
            
            // 比较每个节点的连接
            for (let i = 0; i < keys1.length; i++) {
                if (keys1[i] !== keys2[i]) {
                    return false
                }
                
                const connections1 = topo1[keys1[i]]
                const connections2 = topo2[keys2[i]]
                
                if (JSON.stringify(connections1) !== JSON.stringify(connections2)) {
                    return false
                }
            }
            
            return true
        }

        // 测试函数
        function testNormalizeComponentType() {
            const testCases = [
                { input: '极性电容', expected: '极性电容' },
                { input: '无极性电容', expected: '无极性电容' },
                { input: '电阻', expected: '电阻' },
                { input: '可变电阻', expected: '可变电阻' },
                { input: '示波器', expected: '示波器' },
                { input: '测点', expected: '测点' },
                { input: '二极管', expected: '二极管' },
                { input: '其他组件', expected: '其他组件' }
            ]

            let results = []
            testCases.forEach(test => {
                const result = normalizeComponentType(test.input)
                const passed = result === test.expected
                results.push(`${test.input} → ${result} ${passed ? '✅' : '❌'}`)
            })

            document.getElementById('test1-result').innerHTML = `
                <div class="result ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">
                    <div class="test-data">${results.join('\n')}</div>
                </div>
            `
        }

        function testExtractComponentType() {
            const testCases = [
                {
                    input: '极性电容 - C1 - 10 μF - 连接点1 - P',
                    expected: '极性电容'
                },
                { 
                    input: '可变电阻 - R1 - 1kΩ - 连接点2 - null', 
                    expected: '可变电阻' 
                },
                { 
                    input: '测点 - TP1 - - 连接点1 - null', 
                    expected: '测点' 
                }
            ]

            let results = []
            testCases.forEach(test => {
                const result = extractComponentType(test.input)
                const passed = result === test.expected
                results.push(`${test.input}\n→ ${result} ${passed ? '✅' : '❌'}`)
            })

            document.getElementById('test2-result').innerHTML = `
                <div class="result ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">
                    <div class="test-data">${results.join('\n\n')}</div>
                </div>
            `
        }

        function testBuildTopology() {
            const connections = [
                {
                    from: '极性电容 - C1 - 10 μF - 连接点1 - P',
                    to: '电阻 - R1 - 1kΩ - 连接点1 - null'
                },
                {
                    from: '电阻 - R1 - 1kΩ - 连接点2 - null',
                    to: '测点 - TP1 - - 连接点1 - null'
                }
            ]

            const topology = buildTopology(connections)
            
            document.getElementById('test3-result').innerHTML = `
                <div class="result info">
                    <div class="test-data">输入连接:
${JSON.stringify(connections, null, 2)}

生成的拓扑结构:
${JSON.stringify(topology, null, 2)}</div>
                </div>
            `
        }

        function testCompareTopology() {
            const topo1 = {
                '极性电容': ['电阻'],
                '电阻': ['极性电容', '测点'],
                '测点': ['电阻']
            }

            const topo2 = {
                '极性电容': ['电阻'],
                '电阻': ['极性电容', '测点'],
                '测点': ['电阻']
            }

            const topo3 = {
                '极性电容': ['电阻'],
                '电阻': ['极性电容'],
                '测点': ['示波器']
            }

            const result1 = compareTopology(topo1, topo2)
            const result2 = compareTopology(topo1, topo3)

            document.getElementById('test4-result').innerHTML = `
                <div class="result ${result1 && !result2 ? 'success' : 'error'}">
                    <div class="test-data">相同拓扑比较: ${result1 ? '✅ 通过' : '❌ 失败'}
不同拓扑比较: ${!result2 ? '✅ 通过' : '❌ 失败'}

拓扑1: ${JSON.stringify(topo1)}
拓扑2: ${JSON.stringify(topo2)}
拓扑3: ${JSON.stringify(topo3)}</div>
                </div>
            `
        }

        function testFullValidation() {
            // 模拟模板连接关系
            const templateConnections = [
                {
                    from: '极性电容 - C1 - 10 μF - 连接点1 - P',
                    to: '电阻 - R1 - 1kΩ - 连接点1 - null'
                },
                {
                    from: '电阻 - R1 - 1kΩ - 连接点2 - null',
                    to: '测点 - TP1 - - 连接点1 - null'
                }
            ]

            // 模拟用户连接关系（ID不同但拓扑相同）
            const userConnections = [
                {
                    from: '极性电容 - C2 - 10 μF - 连接点1 - P',
                    to: '电阻 - R2 - 1kΩ - 连接点1 - null'
                },
                {
                    from: '电阻 - R2 - 1kΩ - 连接点2 - null',
                    to: '测点 - TP2 - - 连接点1 - null'
                }
            ]

            const templateTopo = buildTopology(templateConnections)
            const userTopo = buildTopology(userConnections)
            const isMatch = compareTopology(templateTopo, userTopo)

            document.getElementById('test5-result').innerHTML = `
                <div class="result ${isMatch ? 'success' : 'error'}">
                    <div class="test-data">校验结果: ${isMatch ? '✅ 通过' : '❌ 失败'}

模板拓扑: ${JSON.stringify(templateTopo)}
用户拓扑: ${JSON.stringify(userTopo)}

说明: 即使组件ID不同(C1→C2, R1→R2, TP1→TP2)，但拓扑结构相同，校验应该通过。</div>
                </div>
            `
        }
    </script>
</body>
</html>
