// main.js

import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus' // 引入 element-plus
import 'element-plus/dist/index.css' // 引入 element-plus 样式
import * as ElementPlusIconsVue from '@element-plus/icons-vue' // 引入 element-plus-icon 样式
import Layui from '@layui/layui-vue' // 引入 layui-vue
import '@layui/layui-vue/lib/index.css' // 引入 layui-vue 样式
import { createPinia } from 'pinia' // 引入 pinia

const app = createApp(App)
const pinia = createPinia()

// 注册 element-plus 全局插件
app.use(ElementPlus)
// 注册 layui 全局插件
app.use(Layui)
// 注册 pinia 插件
app.use(pinia)

// 注册 element-plus-icon 全局组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 引入自定义插件：注册全局组件
import allGlobalLayout from '@/layout/index.js'
import allGlobalComponents from '@/components/index'
// 安装自定义插件
app.use(allGlobalLayout)
app.use(allGlobalComponents)

// svg 插件需要配置代码
import 'virtual:svg-icons-register'

// 校验 URL 来源：刷线页面、网址栏直接访问    ||    从其他页面跳转过来
import validateURLReference from '@/utils/before/validateURLReference.js'
// validateURLReference()

import { validateURLParam, getCircuitDataAndLoadData } from '@/utils/before/validateURLParam.js'
// 校验 URL 参数
validateURLParam()
// 🔧 修复：移除重复的数据加载，避免双重GET请求
// 数据加载将在App.vue的onMounted中处理
// getCircuitDataAndLoadData()

app.mount('#app')
