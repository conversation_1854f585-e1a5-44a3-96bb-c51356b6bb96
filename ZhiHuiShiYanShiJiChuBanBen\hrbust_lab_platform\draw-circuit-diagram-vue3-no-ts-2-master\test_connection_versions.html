<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接关系版本对比</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .version-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .version-panel {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #fafafa;
        }
        .version-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .v1-title {
            background: #e3f2fd;
            color: #1976d2;
        }
        .v1_1-title {
            background: #e8f5e8;
            color: #388e3c;
        }
        .data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .stats {
            background: #fff3cd;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .difference {
            background: #ffcdd2;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>连接关系版本对比 - v1 vs v1.1</h1>
        <p>展示原始连接关系（v1）和规范化连接关系（v1.1）的差异</p>

        <div style="background: #f0f9ff; padding: 15px; margin: 15px 0; border-radius: 8px; border: 1px solid #0066cc;">
            <h3 style="margin: 0 0 10px 0; color: #0066cc;">🔧 版本配置</h3>
            <p style="margin: 5px 0;"><strong>当前使用版本：<span id="currentVersion" style="color: #0066cc;"></span></strong></p>
            <div style="font-size: 14px; color: #333;">
                <p style="margin: 5px 0;">• <strong>v1版本</strong>：原始版本，使用包含匹配（可能存在组件混淆）</p>
                <p style="margin: 5px 0;">• <strong>v1.1版本</strong>：智能匹配算法，自动支持新器件（避免组件混淆）</p>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">
                💡 要切换版本，请修改代码中的 <code>VALIDATION_VERSION</code> 变量
            </p>
        </div>

        <button onclick="generateComparison()">生成版本对比</button>
        <button onclick="showDifferences()">显示关键差异</button>

        <div class="version-comparison">
            <div class="version-panel">
                <div class="version-title v1-title">v1版本 - 原始连接关系</div>
                <div class="stats" id="v1-stats"></div>
                <div class="data" id="v1-data"></div>
            </div>
            
            <div class="version-panel">
                <div class="version-title v1_1-title">v1.1版本 - 规范化连接关系</div>
                <div class="stats" id="v1_1-stats"></div>
                <div class="data" id="v1_1-data"></div>
            </div>
        </div>

        <div id="differences-section"></div>
    </div>

    <script>
        // 您提供的实际v1版本数据
        const v1Connections = [
            {"from":"极性电容 - C1 - 10 μF - 连接点1 - P","to":"无极性电容 - C1 - 10 μF - 连接点2 - null"},
            {"from":"极性电容 - C1 - 10 μF - 连接点1 - P","to":"测点 - Jk53VZ - - 连接点 - null"},
            {"from":"极性电容 - C1 - 10 μF - 连接点2 - N","to":"二极管 - D1 -   - 连接点2 - N"},
            {"from":"无极性电容 - C1 - 10 μF - 连接点1 - null","to":"电阻 - R1 - 1 KΩ - 连接点1 - null"},
            {"from":"无极性电容 - C1 - 10 μF - 连接点2 - null","to":"测点 - Jk53VZ - - 连接点 - null"},
            {"from":"电阻 - R1 - 1 KΩ - 连接点2 - null","to":"示波器 - 示波器 1 -   - 连接点1 - N"},
            {"from":"电阻 - R1 - 1 KΩ - 连接点2 - null","to":"测点 - HsETk9 - - 连接点 - null"},
            {"from":"二极管 - D1 -   - 连接点1 - P","to":"示波器 - 示波器 1 -   - 连接点2 - P"},
            {"from":"二极管 - D1 -   - 连接点1 - P","to":"测点 - FxSnj1 - - 连接点 - null"},
            {"from":"示波器 - 示波器 1 -   - 连接点1 - N","to":"测点 - HsETk9 - - 连接点 - null"},
            {"from":"示波器 - 示波器 1 -   - 连接点2 - P","to":"测点 - FxSnj1 - - 连接点 - null"}
        ];

        // 规范化函数
        // 版本配置 - 在这里切换版本！
        const VALIDATION_VERSION = 'v1.1' // 'v1' 或 'v1.1'

        // v1版本 - 原始版本（包含匹配）
        function normalizeComponentTypeV1(type) {
            if (!type) return type

            // 保持电容的极性区分 - 注意顺序！先判断无极性电容
            if (type.includes('无极性电容')) return '无极性电容'
            if (type.includes('极性电容')) return '极性电容'

            // 电阻类型规范化
            if (type.includes('电阻')) {
                if (type.includes('可变')) return '可变电阻'
                return '电阻'
            }

            // 示波器类型规范化
            if (type.includes('示波器')) return '示波器'

            // 测点类型规范化
            if (type.includes('测点')) return '测点'

            // 二极管类型规范化
            if (type.includes('二极管')) return '二极管'

            // 其他组件保持原样
            return type
        }

        // v1.1版本 - 智能匹配算法（自动支持新器件）
        function normalizeComponentTypeV1_1(type) {
            if (!type) return type

            // 🚀 智能匹配算法：基于组件库自动匹配，支持新器件
            const typeStr = type.trim()

            // 模拟组件库数据（实际项目中从组件库导入）
            const componentTypes = [
                '无极性电容', '极性电容', '可变电容',
                '可变电阻', '电阻',
                '单刀双掷开关', '单刀单掷开关', '开关',
                '单向稳压管', '双向稳压管', '二极管',
                'NPN三极管', 'PNP三极管', '三极管',
                '电流表', '电压表', '万用表',
                '示波器', '测点', '电感', '直流电压源', '电压源', '接地', '保险管', '运算放大器'
            ].sort((a, b) => b.length - a.length) // 长度降序：确保"无极性电容"优先于"极性电容"

            // 特殊处理：NPN/PNP 三极管的标准化
            if (typeStr === 'NPN') return 'NPN'
            if (typeStr === 'PNP') return 'PNP'

            // 智能匹配：找到第一个包含的组件类型
            for (const compType of componentTypes) {
                if (typeStr.includes(compType)) {
                    return compType
                }
            }

            // 如果没有找到匹配，返回原始类型
            return type
        }

        // 统一的规范化函数 - 根据配置选择版本
        function normalizeComponentType(type) {
            if (VALIDATION_VERSION === 'v1.1') {
                return normalizeComponentTypeV1_1(type)
            } else {
                return normalizeComponentTypeV1(type)
            }
        }

        // 规范化连接字符串 - 保留组件类型、连接点、极性，去掉ID和参数
        function normalizeConnectionString(connectionDesc) {
            if (!connectionDesc) return ''

            // 连接描述格式: "组件类型 - 组件ID - 参数 - 连接点 - 极性"
            const parts = connectionDesc.split(' - ')

            if (parts.length >= 5) {
                const componentType = normalizeComponentType(parts[0].trim())
                const connectionPoint = parts[3].trim() // 连接点信息
                const polarity = parts[4].trim() // 极性信息

                // 重新组合：组件类型 - 连接点 - 极性
                return `${componentType} - ${connectionPoint} - ${polarity}`
            } else if (parts.length === 4) {
                // 处理4段格式，可能是参数为空的情况
                const componentType = normalizeComponentType(parts[0].trim())
                const connectionPoint = parts[2].trim() // 连接点信息
                const polarity = parts[3].trim() // 极性信息

                return `${componentType} - ${connectionPoint} - ${polarity}`
            } else if (parts.length === 3) {
                // 处理3段格式
                const componentType = normalizeComponentType(parts[0].trim())
                const connectionPoint = parts[1].trim() // 连接点信息
                const polarity = parts[2].trim() // 极性信息

                return `${componentType} - ${connectionPoint} - ${polarity}`
            } else if (parts.length >= 1) {
                // 如果格式不标准，至少保留组件类型
                const componentType = normalizeComponentType(parts[0].trim())
                return `${componentType} - 连接点 - null`
            }

            return connectionDesc
        }

        // 生成v1.1版本 - 新规则
        function generateV1_1Connections(v1Connections) {
            return v1Connections.map(conn => ({
                from: normalizeConnectionString(conn.from),
                to: normalizeConnectionString(conn.to)
            }));
        }

        function generateComparison() {
            // 生成v1.1版本
            const v1_1Connections = generateV1_1Connections(v1Connections);

            // 显示v1版本
            document.getElementById('v1-data').textContent = JSON.stringify(v1Connections, null, 2);
            document.getElementById('v1-stats').innerHTML = `
                <strong>统计信息：</strong><br>
                • 连接数量: ${v1Connections.length}<br>
                • 包含具体ID: 是<br>
                • 包含参数信息: 是<br>
                • 包含连接点信息: 是
            `;

            // 显示v1.1版本
            document.getElementById('v1_1-data').textContent = JSON.stringify(v1_1Connections, null, 2);
            document.getElementById('v1_1-stats').innerHTML = `
                <strong>统计信息：</strong><br>
                • 连接数量: ${v1_1Connections.length}<br>
                • 包含具体ID: 否<br>
                • 包含参数信息: 否<br>
                • 只保留组件类型: 是
            `;
        }

        function showDifferences() {
            const v1_1Connections = generateV1_1Connections(v1Connections);
            
            let differencesHtml = `
                <div class="difference">
                    <h3>🔍 关键差异分析</h3>
                    
                    <h4>1. 数据结构对比</h4>
                    <p><strong>v1版本示例：</strong></p>
                    <code>"from": "极性电容 - C1 - 10 μF - 连接点1 - P"</code>

                    <p><strong>v1.1版本示例：</strong></p>
                    <code>"from": "极性电容 - 连接点1 - P"</code>

                    <h4>2. 保留的信息</h4>
                    <p><strong>✅ 保留：</strong> 组件类型、连接点、极性信息</p>
                    <p><strong>❌ 去除：</strong> 组件ID、参数信息</p>

                    <h4>3. 测点ID处理</h4>
                    <p><strong>v1版本：</strong> "测点 - Jk53VZ - - 连接点 - null"</p>
                    <p><strong>v1.1版本：</strong> "测点 - 连接点 - null"</p>

                    <h4>4. 极性信息保持</h4>
                    <p>✅ 示波器连接点1-N ≠ 示波器连接点2-P (保持区分)</p>
                    <p>✅ 极性电容 ≠ 无极性电容 (保持区分)</p>

                    <h4>5. 校验优势</h4>
                    <p><strong>v1版本：</strong> 精确匹配，但受ID影响</p>
                    <p><strong>v1.1版本：</strong> 结构匹配，保留关键信息，忽略ID差异</p>
                </div>
            `;

            // 显示具体转换示例
            differencesHtml += `
                <div class="difference">
                    <h3>📋 转换示例</h3>
            `;

            for (let i = 0; i < Math.min(3, v1Connections.length); i++) {
                const v1 = v1Connections[i];
                const v1_1 = generateV1_1Connections([v1])[0];
                
                differencesHtml += `
                    <p><strong>连接 ${i + 1}:</strong></p>
                    <p>v1: <code>${v1.from}</code> → <code>${v1.to}</code></p>
                    <p>v1.1: <code>${v1_1.from}</code> → <code>${v1_1.to}</code></p>
                    <hr>
                `;
            }

            differencesHtml += `</div>`;

            document.getElementById('differences-section').innerHTML = differencesHtml;
        }

        // 页面加载时自动生成对比
        window.onload = function() {
            // 显示当前版本
            document.getElementById('currentVersion').textContent = VALIDATION_VERSION;
            generateComparison();
        };
    </script>
</body>
</html>
