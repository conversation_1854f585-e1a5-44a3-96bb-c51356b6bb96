import service from '@/utils/request'

const baseURL = '/hrbust_lab_platform'

/**
 * 可调电位器
 * @param {*} ip 远程机器 IP
 * @param {*} canshuxz 参数选择（电位器按设定目标值转动=16，正向转动电位器=17，反向转动电位器=18）
 * @param {*} banhao 版本号（0-2）
 * @param {*} dianweiqi 电位器（n号）
 * @param {*} dushu 度数/设定值
 * @returns
 */
export function getdianwieqi(ip, canshuxz = 16, banhao, dianweiqi, dushu) {
  return service({
    baseURL,
    url: '/remoteStationInstrument/getdianwieqi',
    method: 'get',
    params: { ip, canshuxz, banhao, dianweiqi, dushu },
  })
}

/**
 * 万用表：测试电压
 * @param {*} ip 远程机器 IP
 * @param {*} param 正极（2-30）
 * @param {*} param2 负极（2-30）
 * @returns
 */
export function csdianyajizhun(ip, param, param2) {
  return service({
    baseURL,
    url: '/remoteduojo/csdianyajizhun',
    method: 'get',
    params: { ip, param, param2 },
  })
}

/**
 * 万用表：测试电流
 * @param {*} ip 远程机器 IP
 * @param {*} param 正极（2-30）
 * @param {*} param2 负极（2-30）
 * @returns
 */
export function csdianliu(ip, param, param2) {
  return service({
    baseURL,
    url: '/remoteduojo/csdianliu',
    method: 'get',
    params: { ip, param, param2 },
  })
}

/**
 * 选择可变电容
 * @param {*} ip 远程机器 IP
 * @param {*} param 组号（0-1）
 * @param {*} param2 位置（1-30）
 * @returns
 */
export function cskebiandianrong(ip, param, param2) {
  return service({
    baseURL,
    url: '/remoteduojo/cskebiandianrong',
    method: 'get',
    params: { ip, param, param2 },
  })
}

/**
 * 选择可变电阻
 * @param {*} ip 远程机器 IP
 * @param {*} param 组号（0-5）
 * @param {*} param2 位置（1-15）
 * @returns
 */
export function cskebiandianzu(ip, param, param2) {
  return service({
    baseURL,
    url: '/remoteduojo/cskebiandianzu',
    method: 'get',
    params: { ip, param, param2 },
  })
}

/**
 * 继电器-开关量
 * @param {*} duojiip 远程机器 IP
 * @param {*} sort 电位器序号
 * @param {*} param 1=打开，0=关闭
 * @returns
 */
export function kaiguanliang(duojiip, sort, param) {
  return service({
    baseURL,
    url: '/remoteduojo/kaiguanliang',
    method: 'post',
    data: { duojiip, sort, param },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded', // 设置正确的请求头
    },
  })
}

/**
 * 示波器
 * @param {*} ip 远程机器 IP
 * @param {*} param 通道号（0-2）（0=GND，1=GNDLO1,2=GNDLO2）
 * @param {*} param2 测试点（1-58）
 * @returns
 */
export function csshiboqi(ip, param, param2) {
  return service({
    baseURL,
    url: '/remoteduojo/csshiboqi',
    method: 'get',
    params: { ip, param, param2 },
  })
}

/**
 * 单独设置某一舵机某一IO偏移量
 * @param {*} ip 远程机器 IP
 * @param {*} duoji 舵机号（1-75）
 * @param {*} iopbianhao IO编号（1-30）
 * @param {*} pyl 偏移量（1-30）
 * @returns
 */
export function duojiIoPianYi(ip, duoji, iopbianhao, pyl) {
  return service({
    baseURL,
    url: '/remoteStationInstrument/duojiIoPianYi',
    method: 'get',
    params: { ip, duoji, iopbianhao, pyl },
  })
}
