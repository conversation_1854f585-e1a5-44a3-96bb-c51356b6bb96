<template>
  <el-button 
    class="control-button" 
    type="primary" 
    size="small"
    @click="goToExperiment">
    <el-icon size="14px">
      <svg-icon name="experiment" width="14px" height="14px" />
    </el-icon>
    <span>实验环境</span>
  </el-button>
</template>

<script setup name="GoToExperiment">
import { inject } from 'vue'
import { ElMessage } from 'element-plus'

// 获取状态管理器或事件总线
const store = inject('store', null)
const emitter = inject('emitter', null)

const goToExperiment = () => {
  console.log("点击进入实验环境按钮");
  
  // 获取电路图数据
  const circuitImageData = sessionStorage.getItem('temp_circuit_png')
  console.log("电路图数据是否存在:", !!circuitImageData);
  
  // 检查是否有电路图数据
  if (!circuitImageData) {
    ElMessage({
      message: '请先导出电路图再进入实验环境',
      type: 'warning'
    })
    return
  }
  
  // 确保数据是有效的图像数据
  if (!circuitImageData.startsWith('data:image')) {
    ElMessage({
      message: '电路图数据格式不正确，请重新导出',
      type: 'error'
    })
    return
  }
  
  console.log("设置from_circuit_editor标志为true");
  // 跳转前设置标志，表示从编辑器跳转
  sessionStorage.setItem('from_circuit_editor', 'true')
  
  // 跳转到实验环境页面
  console.log("准备跳转到实验环境页面");
  window.open('/shiyanhuanjing_new.html', '_blank')
}
</script>

<style scoped>
  .control-button {
    width: 75px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
    border-radius: 4px;
    padding: 0 8px;
  }
</style> 